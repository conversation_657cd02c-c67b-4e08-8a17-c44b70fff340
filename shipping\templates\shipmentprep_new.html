<div class="page" data-ng-controller="shipment_prep_new">
    <div class="row ui-section">
        <div class="col-md-12">
            
            <article class="article">
                <script type="text/ng-template" id="password.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            TPVR for Closing Bin (Bin ID : {{CurrentBin.BinName}})
                        </div>
                        <div class="panel-body">
                            <!-- Error Message Area -->
                            <div ng-show="errorMessage" class="alert alert-danger" style="margin-bottom: 15px;">
                                <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">error</i>
                                {{errorMessage}}
                            </div>

                            <!-- Processing Indicator -->
                            <div ng-show="processing" class="alert alert-info" style="margin-bottom: 15px;">
                                <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">hourglass_empty</i>
                                Processing request, please wait...
                            </div>

                            <div class="form-horizontal verification-form">
                                <form name="tpvForm">
                                    <md-input-container class="md-block">
                                        <label>Controller</label>
                                        <input required name="AuditController" id="AuditController" ng-model="confirmDetails.AuditController" ng-maxlength="100" type="text" ng-enter="FocusNextField('password','0')">
                                        <div ng-messages="tpvForm.AuditController.$error" multiple ng-if='tpvForm.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>
                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="password" ng-model="confirmDetails.Password" ng-maxlength="50" type="password" ng-enter="FocusNextField('NewSealID','0')">
                                        <div ng-messages="tpvForm.Password.$error" multiple ng-if='tpvForm.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Seal ID</label>
                                        <input required name="NewSealID" id="NewSealID" ng-model="confirmDetails.NewSealID" ng-maxlength="100" type="text" ng-enter="FocusNextField('BinWeight','0')">
                                        <div ng-messages="tpvForm.NewSealID.$error" multiple ng-if='tpvForm.NewSealID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Weight</label>
                                        <input required name="BinWeight" id="BinWeight" ng-model="confirmDetails.BinWeight" ng-max="999999" ng-min="0" type="number" ng-enter="hide()">
                                        <div ng-messages="tpvForm.BinWeight.$error" multiple ng-if='tpvForm.BinWeight.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="min">Weight must be greater than 0.</div>
                                            <div ng-message="max">Weight cannot exceed 999999.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()" ng-disabled="processing">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="processing">
                                <span ng-show="!processing">Continue</span>
                                <span ng-show="processing">Processing...</span>
                            </button>
                        </div>
                    </div>
                </script>



                <md-card class="no-margin-h mt-0">
                    <md-toolbar class="md-table-toolbar md-default" ng-init="ShipmentDetails = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="ShipmentDetails = !ShipmentDetails">
                            <i class="material-icons md-primary" ng-show="ShipmentDetails">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! ShipmentDetails">keyboard_arrow_down</i>
                            <span>Shipment Details</span>
                        </div>
                    </md-toolbar>
                    <div class="row mb-10" ng-show="ShipmentDetails">

                        <form name="shipmentForm">
                            <div class="col-md-12">
                                <div class="col-md-3">
                                    <md-input-container class="md-block" flex-gt-sm>
                                        <label>Ticket ID</label>
                                        <input required name="ShippingID" ng-model="shipping.ShippingID" ng-maxlength="50" ng-disabled="ShippingID">
                                        <div class="error-sapce">
                                            <div ng-messages="shipmentForm.ShippingID.$error" multiple ng-if='shipmentForm.ShippingID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 50.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Facility</label>
                                        <md-select name="FacilityID" ng-model="shipping.FacilityID" required ng-disabled="true" ng-change="GetFacilityPackageTypes();GetFacilityByProducts()">
                                            <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facilities">{{fac.FacilityName}}</md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="shipmentForm.FacilityID.$error" multiple ng-if='shipmentForm.FacilityID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div style="clear: both;"></div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Transition Facility</label>
                                        <md-select name="DestinationFacilityID" ng-model="shipping.DestinationFacilityID" ng-disabled="shipping.Containers.length > 0" ng-change="DestinationFacilityChanged()">
                                            <!-- <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facilities" ng-hide="fac.FacilityID == shipping.FacilityID">{{fac.FacilityName}}</md-option> -->
                                            <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facilities" >{{fac.FacilityName}}</md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="shipmentForm.DestinationFacilityID.$error" multiple ng-if='shipmentForm.DestinationFacilityID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>   
                                
                                <div class="col-md-3 text-center">
                                    <p class="mb-0" style="line-height: 54px;"><strong class="text-danger">( OR )</strong></p>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Destination</label>
                                        <!-- <md-select name="VendorID" ng-model="shipping.VendorID" required ng-change="GetVendorPOC();GetDestinationRemovalTypes()" ng-disabled="ShippingID"> -->
                                        <md-select name="VendorID" ng-model="shipping.VendorID" ng-disabled="shipping.Containers.length > 0" ng-change="GetVendorPOC();GetDestinationRemovalTypes();DestinationChanged()">
                                            <md-option value="{{vend.VendorID}}" ng-repeat="vend in Vendors">{{vend.VendorName}}</md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="shipmentForm.VendorID.$error" multiple ng-if='shipmentForm.VendorID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- <div style="clear: both;"></div> -->

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Destination POC</label>
                                        <input name="DestinationPOC" ng-model="shipping.DestinationPOC" ng-disabled="true" />
                                        <div class="error-sapce"></div>
                                    </md-input-container>
                                </div>
                                
                            </div>


                            <!-- <div class="col-md-12 btns-row" ng-show="ShippingID == ''"> -->
                            <div class="col-md-12 btns-row" >
                                <button class="md-button md-raised btn-w-md md-default" ng-click="CancelTicket()">
                                    Cancel
                                </button>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                    data-ng-disabled="shipmentForm.$invalid || shipping.busy" ng-click="CreateShipment()">
                                    <span ng-show="! shipping.busy">Save</span>
                                    <span ng-show="shipping.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                </md-button>
                            </div>

                        </form>

                    </div>


                </md-card>



                <!--List Start-->
                <md-card class="no-margin-h" ng-show="ShippingID != ''">

                    <!-- Shipment Summary Block -->
                    <div ng-show="shipping.Bins.length > 0" style="background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; padding: 15px;">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 style="margin: 0 0 10px 0; color: #495057; font-weight: 600;">Shipment Summary</h4>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div style="background-color: #e8f5e8; padding: 10px; border-radius: 4px; text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #388e3c;">{{shipmentSummary.totalWeight || 0}}</div>
                                            <div style="font-size: 12px; color: #666; margin-top: 2px;">Total Weight (Closed Bins Only)</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div style="background-color: #e8f5e8; padding: 10px; border-radius: 4px; text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #388e3c;">{{shipmentSummary.parentBinCount || 0}}</div>
                                            <div style="font-size: 12px; color: #666; margin-top: 2px;">Parent Bin Count</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div style="background-color: #e3f2fd; padding: 10px; border-radius: 4px; text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #2196F3;">{{shipmentSummary.childBinCount || 0}}</div>
                                            <div style="font-size: 12px; color: #666; margin-top: 2px;">Child Bin Count</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <md-toolbar class="md-table-toolbar md-default" ng-init="AssetsSanizedPanel = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;">
                            <i ng-click="AssetsSanizedPanel = !AssetsSanizedPanel" class="material-icons md-primary" ng-show="AssetsSanizedPanel">keyboard_arrow_up</i>
                            <i ng-click="AssetsSanizedPanel = !AssetsSanizedPanel" class="material-icons md-primary" ng-show="! AssetsSanizedPanel">keyboard_arrow_down</i>
                            <span ng-click="AssetsSanizedPanel = !AssetsSanizedPanel">List Of Bins</span>
                            <div flex></div>
                            
                            <div class="col-md-3 dis_none_v">
                                <md-input-container md-no-float class="md-block md-no-float includedsearch tdinput">
                                    <input required name="SearchBin" id="SearchBin" ng-model="SearchBin" required placeholder="Add Bin" ng-enter="AddBinToShipment($event)">
                                    <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="AddBinToShipment($event)" ng-disabled="!SearchBin">
                                        Go
                                    </md-button>
                                </md-input-container>
                            </div>

                            <div flex></div>
                            <button class="md-button md-raised md-default mr-5" ng-click="exportpacking(shipping.ShippingID)" title="Export Packing Slip" ng-show="shipping.Bins.length > 0">
                                <md-icon md-svg-src="../assets/images/box.svg"></md-icon>
                                Export to Excel
                            </button>
                            <button class="md-button md-raised btn-w-md md-primary mt-10" style="display: flex;" ng-click="ManageBin()">
                                <i class="material-icons">add</i> Create New Bin
                            </button>
                        </div>

                        <div class="col-md-12 dis_open_v" style="display:none;">
                            <md-input-container md-no-float class="md-block md-no-float includedsearch tdinput">
                                <input required name="SearchBin" ng-model="SearchBin" required placeholder="Add Bin" ng-enter="AddBinToShipment($event)">
                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="AddBinToShipment($event)" ng-disabled="!SearchBin">
                                    Go
                                </md-button>
                            </md-input-container>
                        </div>
                    </md-toolbar>

                    <div ng-show="AssetsSanizedPanel">
                        <div class="callout callout-warning" ng-show="shipping.Bins.length == 0">
                            <p>No Bins Available.</p>
                        </div>

                        <div class="col-md-12">
                            <!-- Bin Legend -->
                            <div class="bin-legend" ng-show="shipping.Bins.length > 0" style="margin-bottom: 10px; padding: 8px; background-color: #f5f5f5; border-radius: 4px; font-size: 12px;">
                                <strong>Legend:</strong>
                                <span style="margin-left: 15px; color: #333;">
                                    Parent/Standalone Bin
                                </span>
                                <span style="margin-left: 20px; color: #2196F3;">
                                    Child Bin (shown with blue background)
                                </span>
                            </div>

                            <!-- Bin Search Bar -->
                            <div ng-show="shipping.Bins.length > 0" style="margin-bottom: 15px;">
                                <div class="input-group" style="width: 100%;">
                                    <div class="input-group-addon" style="background-color: #2196F3; color: white; border: 1px solid #2196F3;">
                                        <i class="material-icons" style="font-size: 20px;">search</i>
                                    </div>
                                    <input type="text"
                                           ng-model="binSearchFilter"
                                           placeholder="Search bins by name, disposition, location, package type, part type, MPN..."
                                           class="form-control"
                                           style="font-size: 16px; padding: 12px; border: 1px solid #2196F3; border-left: none;">
                                    <div class="input-group-addon" ng-show="binSearchFilter" style="background-color: #f44336; color: white; border: 1px solid #f44336; cursor: pointer;" ng-click="binSearchFilter = ''">
                                        <i class="material-icons" style="font-size: 16px;">clear</i>
                                    </div>
                                </div>
                            </div>

                            <md-table-container ng-show="shipping.Bins.length > 0">
                                <table md-table class="table">
                                    <thead md-head>
                                        <tr md-row>
                                            <th style="width:70px" md-column>Action</th>
                                            <th md-column style="min-width:120px; width:120px">Bin ID</th>
                                            <th md-column style="min-width:120px; width:120px">Parent Bin</th>
                                            <th md-column style="width:230px">Bin Type</th>
                                            <th md-column style="width:150px">Part Type</th>
                                            <th md-column style="width:200px">Qty X MPN</th>
                                            <th md-column >Removal Type</th>
                                            <th md-column style="width:70px">Weight</th>
                                            <!-- <th md-column>Custom ID</th> -->
                                            <th md-column style="width:120px">Seal ID</th>
                                            <th md-column >Bin Notes</th>
                                            <th md-column>Location</th>
                                            <th md-column style="min-width:220px">Move Serials</th>
                                            
                                        </tr>
                                    </thead>
                                    <tbody md-body>
                                        <tr md-row ng-repeat="bin in shipping.Bins | filter:binFilter"
                                            ng-class="{'child-bin-row': bin.ParentBinName && bin.ParentBinName.trim() !== ''}"
                                            ng-style="bin.ParentBinName && bin.ParentBinName.trim() !== '' ? {'background-color': '#e3f2fd', 'border-left': '3px solid #2196F3'} : {}">
                                            <td md-cell class="actionicons" style="min-width:200px">
                                                <!-- Edit Icon -->
                                                <i class="material-icons edit text-danger" ng-click="EditBin(bin)" ng-show="bin.ASNContainer == '0' && (bin.SealID == '' || bin.SealID == null)" title="Edit Bin">edit</i>

                                                <!-- Print Icon -->
                                                <a href="{{host}}label/master/examples/ShipmentContainerlabel.php?id={{bin.CustomPalletID}}" target="_blank" title="Print Label"><i class="material-icons print">print</i></a>

                                                <!-- Remove/Delete Icon -->
                                                <i class="material-icons text-danger" ng-hide="bin.ASNContainer == '1'" ng-click="DeleteByProductBinFromShipment(bin,$event)" ng-show="!bin.ParentBinName || bin.ParentBinName.trim() === ''" title="Remove Bin">close</i>

                                                <!-- Close Bin Icon -->
                                                <i class="material-icons text-warning" ng-click="CloseBin(bin,$event)" ng-show="(bin.SealID == '' || bin.SealID == null) && bin.ASNContainer == '0'" title="Close Bin">lock</i>

                                                <!-- Reopen Bin Icon -->
                                                <i class="material-icons text-success" ng-click="ReopenBin(bin,$event)" ng-show="(bin.SealID != '' && bin.SealID != null) && bin.ASNContainer == '0'" title="Reopen Bin">lock_open</i>

                                                <!-- Unlink Icon -->
                                                <i class="material-icons text-info" ng-click="UnlinkBin(bin,$event)" ng-show="!bin.ParentBinName || bin.ParentBinName.trim() === ''" title="Unlink from Shipment">link_off</i>
                                            </td>
                                            <td md-cell>
                                                <div ng-if="bin.ParentBinName && bin.ParentBinName.trim() !== ''">
                                                    {{bin.BinName}}
                                                </div>
                                                <div ng-if="!bin.ParentBinName || bin.ParentBinName.trim() === ''" style="font-weight: 500;">
                                                    {{bin.BinName}}
                                                </div>
                                            </td>
                                            <td md-cell>{{bin.ParentBinName || '-'}}</td>
                                            <td md-cell>{{bin.packageName}}</td>
                                            <td md-cell>{{bin.PartTypeSummary || '-'}}</td>
                                            <td md-cell>
                                                <span ng-click="UpdateBinPartTypeSummary(bin)" style="cursor: pointer;">
                                                    <md-icon class="material-icons text-danger" style="font-size: 16px; vertical-align: middle; margin-right: 5px;">refresh</md-icon>
                                                </span>
                                                {{bin.MPNSummary || '-'}}
                                            </td>
                                            <td md-cell>{{bin.disposition}}</td>
                                            <td md-cell>{{bin.ContainerWeight}}</td>
                                            <!-- <td md-cell>{{bin.CustomID}}</td> -->
                                            <td md-cell>{{bin.SealID}}</td>
                                            <td md-cell>{{bin.Description}}</td>
                                            <td md-cell>{{bin.LocationName}}</td>
                                            <td md-cell style="min-width:220px">
                                                <md-input-container md-no-float class="md-block md-no-float includedsearch tdinput" ng-hide="bin.ASNContainer == '1'">
                                                    <input required name="NewBinName" ng-model="bin.NewBinName" required placeholder="Move Serials into BIN">
                                                    <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="DeleteBinFromShipment(bin,$event)" ng-disabled="!bin.NewBinName" title="Move Serials">
                                                        Go
                                                    </md-button>
                                                </md-input-container>
                                            </td>
                                        </tr>
                                    </tbody>

                                </table>

                                <!-- No results message -->
                                <div ng-show="binSearchFilter && (shipping.Bins | filter:binFilter).length === 0"
                                     style="text-align: center; padding: 20px; color: #666; font-style: italic;">
                                    <i class="material-icons" style="font-size: 48px; color: #ccc;">search_off</i>
                                    <div style="margin-top: 10px; font-size: 16px;">No bins found matching "{{binSearchFilter}}"</div>
                                    <div style="margin-top: 5px; font-size: 14px;">Try adjusting your search terms</div>
                                </div>
                            </md-table-container>
                        </div>
                    </div>

                </md-card>
                <!--List Close-->


                <!--Create New Bin Start-->
                <md-card class="no-margin-h" ng-show="ShippingID != '' && addingBin">

                    <md-toolbar class="md-table-toolbar md-default" ng-init="AddBin = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;">
                            <i ng-click="AddBin = !AddBin" class="material-icons md-primary" ng-show="AddBin">keyboard_arrow_up</i>
                            <i ng-click="AddBin = !AddBin" class="material-icons md-primary" ng-show="! AddBin">keyboard_arrow_down</i>
                            <span ng-click="AddBin = !AddBin" ng-show="!newBin.CustomPalletID">Create New Bin</span>
                            <span ng-click="AddBin = !AddBin" ng-show="newBin.CustomPalletID" style="color: #ff9800; font-weight: bold;">
                                <i class="material-icons" style="vertical-align: middle; margin-right: 5px;">edit</i>
                                Editing Bin: {{newBin.BinName}}
                            </span>
                            <div flex></div>

                            <span ng-show="canServerCountbeShown()"><strong class="mr-5">Server SN Count:</strong><span ng-class="{'badge bg-danger': ScannedServerCount() != newBin.ExpectedServersCount,'badge bg-success': newBin.ScannedServersCount == newBin.ExpectedServersCount}">{{ScannedServerCount()}} / {{newBin.ExpectedServersCount}}</span></span>
                            <!-- <button class="md-button md-raised btn-w-md md-primary mt-10" style="display: flex;">
                                <i class="material-icons">add</i> Add Serials
                            </button> -->
                        </div>
                    </md-toolbar>

                    <div ng-show="AddBin">
                        <!-- Editing Mode Notice -->
                        <div ng-show="newBin.CustomPalletID" class="alert alert-info" style="margin: 15px; padding: 15px; background-color: #e3f2fd; border: 1px solid #2196F3; border-radius: 4px;">
                            <div style="display: flex; align-items: center;">
                                <i class="material-icons" style="color: #2196F3; margin-right: 10px; font-size: 24px;">edit</i>
                                <div>
                                    <h4 style="margin: 0; color: #1976D2; font-weight: bold;">Editing Bin</h4>
                                    <p style="margin: 5px 0 0 0; color: #424242;">
                                        <strong>Bin Name:</strong> {{newBin.BinName}} |
                                        <strong>Bin Type:</strong> {{newBin.packageName}} |
                                        <strong>Current Assets:</strong> {{newBin.AssetsCount || 0}}
                                        <span ng-show="newBin.ParentBinName"> | <strong>Parent Bin:</strong> {{newBin.ParentBinName}}</span>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- <pre>{{newBin}}</pre> -->
                        <div style="margin-bottom: 10px;">
                            <form class="row" name="binForm">
                                <div class="col-md-12">
                                    <!-- Row 1: Bin Type, Bin Name, Facility -->
                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Bin Type</label>
                                            <md-select name="idPackage" ng-model="newBin.idPackage" required aria-label="select">
                                                <md-option ng-repeat="packageType in PackageTypes" value="{{packageType.idPackage}}"> {{packageType.packageName}} </md-option>
                                            </md-select>
                                            <div ng-messages="binForm.idPackage.$error" multiple ng-if='binForm.idPackage.$dirty'>
                                                <div ng-message="required">Bin Type is required.</div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Bin Name</label>
                                            <div style="position: relative;">
                                                <input name="BinName" id="BinNameID" ng-model="newBin.BinName" required  ng-minlength="6" autofocus ng-disabled="newBin.CustomPalletID && newBin.CustomPalletID !== ''" style="padding-right: 40px;" />
                                                <md-button class="md-icon-button" ng-click="AutoGenerateBinName()" ng-disabled="newBin.CustomPalletID && newBin.CustomPalletID !== ''"
                                                           style="position: absolute; right: 0; top: 50%; transform: translateY(-50%); margin: 0; width: 32px; height: 32px;"
                                                           aria-label="Auto Generate Bin Name">
                                                    <md-icon>refresh</md-icon>
                                                </md-button>
                                            </div>
                                            <div ng-messages="binForm.BinName.$error" multiple ng-if='binForm.BinName.$dirty'>
                                                <div ng-message="required">Bin Name is required.</div>
                                                <div ng-message="minlength">Min length 6.</div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <!-- <label>Parent Bin (Optional)</label> -->
                                            <md-autocomplete flex style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                                md-input-name="ParentBinName"
                                                md-input-maxlength="100"
                                                ng-disabled="shipping.FacilityID == 0 || shipping.FacilityID == NULL || (newBin.CustomPalletID && newBin.CustomPalletID !== '')"
                                                md-no-cache="noCache"
                                                md-search-text-change="ParentBinChange(newBin.ParentBinName)"
                                                md-search-text="newBin.ParentBinName"
                                                md-selected-item="newBin.selectedParentBinItem"
                                                md-items="item in queryParentBinSearch(newBin.ParentBinName)"
                                                md-item-text="item.BinName"
                                                md-selected-item-change="selectedParentBinChange(item)"
                                                md-min-length="0"
                                                ng-model-options='{ debounce: 1000 }'
                                                md-escape-options="clear"
                                                md-floating-label="Parent Bin">
                                                <md-item-template>
                                                    <span md-highlight-text="newBin.ParentBinName" md-highlight-flags="^i">{{item.BinName}}</span>
                                                    <span style="color: #666; font-size: 12px;"> - {{item.disposition}}</span>
                                                </md-item-template>
                                                <md-not-found>
                                                    No bins matching "{{newBin.ParentBinName}}" were found in this shipment.
                                                </md-not-found>
                                                <div ng-messages="binForm.ParentBinName.$error" ng-if="binForm.ParentBinName.$touched">
                                                    <div ng-message="required">Parent Bin is required.</div>
                                                    <div ng-message="minlength">Min length 2.</div>
                                                    <div ng-message="maxlength">Max length 100.</div>
                                                </div>
                                            </md-autocomplete>
                                            <!-- <div ng-if="newBin.ParentBinName && newBin.ParentBinName.trim() !== ''" style="color: #2196F3; font-size: 12px; margin-top: 5px;">
                                                <i class="material-icons" style="font-size: 14px; vertical-align: middle;">info</i>
                                                This bin will be nested under the selected parent bin
                                            </div> -->
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Facility</label>
                                            <md-select name="FacilityID" ng-model="newBin.FacilityID" required ng-disabled="true" ng-change="GetBinPackageTypes()">
                                                <md-option ng-repeat="facilityinformation in Facilities" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                            </md-select>
                                            <div ng-messages="binForm.FacilityID.$error" multiple ng-if='binForm.FacilityID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <!-- Row 2: Location Group, Disposition -->

                                    <div class="col-md-4" ng-hide="isChildBin()">
                                        <div class="autocomplete">
                                            <md-autocomplete flex style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                                md-input-name="group"
                                                md-input-maxlength="100"
                                                ng-disabled="isLocationGroupDisabled() || shipping.FacilityID == 0 || shipping.FacilityID == NULL"
                                                md-no-cache="noCache"
                                                md-search-text-change="LocationChange1(newBin.group)"
                                                md-search-text="newBin.group"
                                                md-selected-item="newBin.selectedLocationItem"
                                                md-items="item in queryLocationSearch1(newBin.group)"
                                                md-item-text="item.GroupName"
                                                md-selected-item-change="selectedLocationChange1(item)"
                                                md-min-length="0"
                                                ng-model-options='{ debounce: 1000 }'
                                                md-escape-options="clear"
                                                md-floating-label="Location Group"
                                                ng-required="!isChildBin()">
                                                <md-item-template>
                                                    <span md-highlight-text="newBin.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                </md-item-template>
                                                <md-not-found>
                                                    No Records matching "{{newBin.group}}" were found.
                                                </md-not-found>
                                                <div ng-messages="binForm.group.$error" ng-if="binForm.group.$touched">
                                                    <div ng-message="required">Location Group is required.</div>
                                                    <div ng-message="minlength">Min length 2.</div>
                                                    <div ng-message="maxlength">Max length 100.</div>
                                                </div>
                                            </md-autocomplete>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Disposition</label>
                                            <md-select name="disposition_id" ng-model="newBin.disposition_id" required aria-label="select" ng-disabled="isDispositionDisabled()">
                                                <md-option ng-repeat="dis_position in Dispositions" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}} <span style="color:red;" ng-show="dis_position.sub_disposition > 0">(Sub Disposition)</span> </md-option>
                                            </md-select>
                                            <div ng-messages="binForm.disposition_id.$error" multiple ng-if='binForm.disposition_id.$dirty'>
                                                <div ng-message="required">Disposition is required.</div>
                                            </div>
                                            <div ng-if="isDispositionDisabled()" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                                <i class="material-icons" style="font-size: 14px; vertical-align: middle;">info</i>
                                                Cannot change disposition when bin has serials
                                            </div>
                                        </md-input-container>
                                    </div>                                    

                                    <!-- Row 3: Physical/Logical, Reference Type, Reference ID Required -->
                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Physical / Logical</label>
                                            <md-select name="BinType" ng-model="newBin.BinType" required aria-label="select">
                                                <md-option value="Physical"> Physical </md-option>
                                                <md-option value="Logical"> Logical </md-option>
                                            </md-select>
                                            <div ng-messages="binForm.BinType.$error" multiple ng-if='binForm.BinType.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Reference Type</label>
                                            <md-select name="ReferenceTypeID" ng-model="newBin.ReferenceTypeID" aria-label="select" ng-change="GetReferenceType()">
                                                <md-option ng-repeat="ref_type in ReferenceType" value="{{ref_type.ReferenceTypeID}}"> {{ref_type.ReferenceType}} </md-option>
                                            </md-select>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <md-switch ng-model="newBin.ReferenceIDRequired" ng-disabled="true" aria-label="ReferenceIDRequired" ng-true-value="'1'" ng-false-value="'0'" class="md-primary">
                                                Reference ID Required
                                            </md-switch>
                                        </md-input-container>
                                    </div>

                                    <!-- <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Container Weight</label>
                                            <input name="ContainerWeight" ng-model="newBin.ContainerWeight" ng-max="999999" ng-min="0" required type="number">
                                            <div ng-messages="containerForm.ContainerWeight.$error" multiple ng-if='containerForm.ContainerWeight.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="min">Min Value 0.</div>
                                                <div ng-message="max">Max Value 999999.</div>
                                            </div>
                                        </md-input-container>
                                    </div> -->

                                    <!-- <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Custom ID</label>
                                            <input name="CustomID" ng-model="newBin.CustomID" ng-maxlength="50">
                                            <div ng-messages="containerForm.CustomID.$error" multiple ng-if='containerForm.CustomID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 50.</div>
                                            </div>
                                        </md-input-container>
                                    </div> -->

                                    <!-- Row 4: Reference ID, Maximum Assets Switch, Maximum Assets -->
                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Reference ID</label>
                                            <input name="ReferenceID" ng-model="newBin.ReferenceID" ng-required="newBin.ReferenceIDRequired == '1'" />
                                            <div ng-messages="binForm.ReferenceID.$error" multiple ng-if='binForm.ReferenceID.$dirty'>
                                                <div ng-message="required">Reference ID is required when Reference Type requires it.</div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <md-switch ng-model="newBin.MaxLimitRequired" ng-true-value="'1'" ng-false-value="'0'" aria-label="Required Maximum Assets" class="md-primary" ng-change="onMaxLimitRequiredChange()">
                                                Required Maximum Assets
                                            </md-switch>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-4" ng-if="newBin.MaxLimitRequired == '1'">
                                        <md-input-container class="md-block">
                                            <label>Maximum Assets</label>
                                            <input type="number" name="MaximumAssets" ng-model="newBin.MaximumAssets"
                                                   ng-min="getMaxLimitMin()" ng-max="10000"
                                                   ng-required="newBin.MaxLimitRequired == '1'"
                                                   ng-change="validateMaxLimit()" />
                                            <div ng-messages="binForm.MaximumAssets.$error" multiple ng-if='binForm.MaximumAssets.$dirty'>
                                                <div ng-message="required">This is required when Maximum Assets is enabled.</div>
                                                <div ng-message="min">Minimum {{getMaxLimitMin()}}{{(newBin.AssetsCount && parseInt(newBin.AssetsCount) > 0) ? ' (current serials count)' : ''}}.</div>
                                                <div ng-message="max">Maximum 10000.</div>
                                            </div>
                                            <div ng-if="newBin.AssetsCount && parseInt(newBin.AssetsCount) > 0" style="color: #ff9800; font-size: 12px; margin-top: 5px;">
                                                <i class="material-icons" style="font-size: 14px; vertical-align: middle;">info</i>
                                                Minimum limit is {{newBin.AssetsCount}} (current serials in bin)
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <!-- Row 5: Customer Lock Switch, Customer, Location Name -->
                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <md-switch ng-model="newBin.CustomerLock" ng-true-value="'1'" ng-false-value="'0'" aria-label="Customer Lock" class="md-primary" ng-change="onCustomerLockChange()" ng-disabled="isCustomerLockDisabled()">
                                                Customer Lock
                                            </md-switch>
                                            <div ng-if="isCustomerLockDisabled()" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                                <i class="material-icons" style="font-size: 14px; vertical-align: middle;">info</i>
                                                Cannot change customer lock when bin has serials
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-4" ng-if="newBin.CustomerLock == '1'">
                                        <md-input-container class="md-block">
                                            <label>Customer</label>
                                            <md-select name="AWSCustomerID" ng-model="newBin.AWSCustomerID" aria-label="select" ng-disabled="isCustomerDisabled()">
                                                <md-option ng-repeat="customer in customers" value="{{customer.AWSCustomerID}}"> {{customer.Customer}} </md-option>
                                            </md-select>
                                            <div ng-if="isCustomerDisabled()" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                                <i class="material-icons" style="font-size: 14px; vertical-align: middle;">info</i>
                                                Cannot change customer when bin has serials
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-4" ng-show="newBin.LocationName != '' && newBin.LocationName">
                                        <md-input-container class="md-block">
                                            <label>Location Name</label>
                                            <input type="text" name="LocationName" ng-model="newBin.LocationName" ng-disabled="true" />
                                        </md-input-container>
                                    </div>

                                    <!-- Row 6: Notes (Full Width) -->
                                    <div class="col-md-12">
                                        <md-input-container class="md-block">
                                            <label>Notes</label>
                                            <input type="text" name="Notes" ng-model="newBin.Description" ng-maxlength="250" />
                                            <div ng-messages="binForm.Notes.$error" multiple ng-if='binForm.Notes.$dirty'>
                                                <div ng-message="maxlength">Max length 250.</div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <!-- <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Seal ID</label>
                                            <input name="SealID" ng-model="newBin.SealID" ng-maxlength="50" required>
                                            <div ng-messages="containerForm.CustomID.$error" multiple ng-if='containerForm.SealID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 50.</div>
                                            </div>
                                        </md-input-container>
                                    </div> -->

                                    <!-- <div class="col-md-3" >                                        
                                        <div class="autocomplete">
                                            <md-autocomplete flex style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                                    md-input-name="location"
                                                    md-input-maxlength="100"
                                                    ng-disabled="shipping.FacilityID == 0 || shipping.FacilityID == NULL "
                                                    md-no-cache="noCache"
                                                    md-search-text-change="LocationChange(newBin.location)"
                                                    md-search-text="newBin.location"
                                                    md-items="item in queryLocationSearch(newBin.location)"
                                                    md-item-text="item.LocationName"
                                                    md-selected-item-change="selectedLocationChange(item)"
                                                    md-min-length="0"
                                                    md-escape-options="clear"
                                                    md-floating-label="Outbound Location"
                                                    >
                                                    <md-item-template>
                                                        <span md-highlight-text="newBin.location" md-highlight-flags="^i">{{item.LocationName}}</span>
                                                    </md-item-template>
                                                    <md-not-found>
                                                        No Records matching "{{newBin.location}}" were found.
                                                    </md-not-found>
                                                    <div ng-messages="containerForm.location.$error" ng-if="containerForm.location.$touched">
                                                        <div ng-message="required">No Records matching.</div>
                                                        <div ng-message="minlength">Min length 2.</div>
                                                        <div ng-message="maxlength">Max length 100.</div>
                                                    </div>
                                            </md-autocomplete>
                                        </div>
                                    </div> -->




                                    




                                    <div class="col-md-3" ng-if="shipping.DestinationFacilityID > 0">
                                        <md-switch class="mt-10" ng-model="newBin.BatchRecovery" aria-label="Batch Recovery" ng-true-value="'1'" ng-false-value="'0'"> Batch Recovery</md-switch>
                                    </div>
                                    


                                    <!-- <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label style="padding-left:30px;">Controller Login ID </label>
                                            <i class="material-icons text-danger error" style="position: absolute; margin-top: 4px;" role="img" aria-label="error" ng-show="! newBin.PasswordVerified">close</i>
                                            <i class="material-icons text-success yes" style="position: absolute; margin-top: 4px;" role="img" aria-label="yes" ng-show="newBin.PasswordVerified">done</i>
                                            <input style="padding-left:30px;" name="ShippingControllerLoginID" ng-model="newBin.ShippingControllerLoginID" ng-maxlength="100" required ng-change="newBin.PasswordVerified = false" ng-enter="ValidateSanitizationControllerPopup($event)" />
                                            <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!newBin.ShippingControllerLoginID" ng-click="ValidateSanitizationControllerPopup($event)">
                                                <md-icon class="material-icons" style="margin-top: -6px; margin-left: 3px;">arrow_forward</md-icon>
                                            </md-button>
                                            <div class="error-sapce">
                                                <div ng-messages="containerForm.ShippingControllerLoginID.$error" multiple ng-if='containerForm.ShippingControllerLoginID.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="minlength">Min length 3.</div>
                                                    <div ng-message="maxlength">Max length 100.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div> -->

                                    <!-- <div class="col-md-3" ng-show="newBin.StatusID == '1'">
                                        <md-input-container class="md-block" >
                                            <md-checkbox ng-model="newBin.CloseContainer" name="CloseContainer"> Close Container </md-checkbox>
                                        </md-input-container>
                                    </div> -->

                                    <div class="col-md-3" ng-show="newBin.StatusID == '3'">
                                        Container Closed
                                    </div>

                                    <div class="col-md-12 btns-row">
                                        <button class="md-button md-raised btn-w-md  md-default" ng-click="ClearContainer()">
                                            Cancel
                                        </button>

                                        <md-button class="md-raised btn-w-md md-primary btn-w-md" id="save_button"
                                            data-ng-disabled="binForm.$invalid || newBin.busy" ng-click="CreateBin()">
                                            <span ng-show="! newBin.busy && !newBin.CustomPalletID">Create Bin</span>
                                            <span ng-show="! newBin.busy && newBin.CustomPalletID">Update Bin</span>
                                            <span ng-show="newBin.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                        </md-button>
                                    </div>
                                </div>
                            </form>
                            <div style="clear: both;"></div>
                        </div>
                        
                        <!-- <div class="bg-grey-light pt-5" style="margin-bottom: 20px;" ng-show="newBin.CreatedBy > 0 && newBin.StatusID == '1' && newBin.PasswordVerified"> -->
                        <div style="margin-bottom: 20px;" ng-show="newBin.CustomPalletID > 0 && (newBin.SealID == '' || newBin.SealID == null)">
                            <div  class="bg-grey-light pt-5">
                                <form class="row" name="SerialForm">

                                    <!--Tabs Started-->
                                    <div class="col-md-12">
                                        <md-tabs md-dynamic-height md-border-bottom class="md-litegrey">

                                            <md-tab label="Inventory">
                                                <div class="mt-10">
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block includedsearch">
                                                            <label>SN</label>
                                                            <input required name="SerialNumber" ng-model="newBin.SerialNumber" ng-maxlength="50" id="SerialNumber" ng-enter="GetCurrentTime(newBin,'serial_scan_time');ValidateSerialNumber(newBin.SerialNumber)"/>
                                                            <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="SN" ng-disabled="!newBin.SerialNumber" ng-click="GetCurrentTime(newBin,'serial_scan_time');ValidateSerialNumber(newBin.SerialNumber)">
                                                                <md-icon class="scanicon" md-svg-src="../assets/images/search.svg"></md-icon>
                                                            </md-button>
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block includedsearch">
                                                            <label>MPN</label>
                                                            <!-- <input required name="UniversalModelNumber" ng-model="newBin.UniversalModelNumber" ng-maxlength="100" ng-enter="AutoNavigateInventorySave()" /> -->
                                                            <input required name="UniversalModelNumber" ng-model="newBin.UniversalModelNumber" ng-maxlength="100" ng-enter="GetCurrentTime(newBin,'mpn_scan_time');GetExactMPN(newBin.UniversalModelNumber)" />
                                                            <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right"
                                                                ng-disabled="!newBin.UniversalModelNumber" ng-click="GetCurrentTime(newBin,'mpn_scan_time');GetExactMPN(newBin.UniversalModelNumber)">
                                                                <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                                            </md-button>
                                                        </md-input-container>
                                                    </div>

                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block includedsearch">
                                                            <label>Seal ID</label>
                                                            <!-- <input required name="UniversalModelNumber" ng-model="newBin.UniversalModelNumber" ng-maxlength="100" ng-enter="AutoNavigateInventorySave()" /> -->
                                                            <input required id="sanitization_seal_id" name="sanitization_seal_id" ng-model="newBin.sanitization_seal_id" ng-maxlength="100" ng-enter="ValidateSanitizationSealID(newBin.sanitization_seal_id,newBin.SerialNumber,newBin.AssetScanID)" />
                                                            <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right"
                                                                ng-disabled="!newBin.sanitization_seal_id" ng-click="ValidateSanitizationSealID(newBin.sanitization_seal_id,newBin.SerialNumber,newBin.AssetScanID)">
                                                                <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                                            </md-button>
                                                        </md-input-container>
                                                    </div>

                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block">
                                                            <label>Sanitization Verification ID</label>
                                                            <input required name="SanitizationVerificationID" ng-model="newBin.SanitizationVerificationID" data-ng-disabled="true" ng-maxlength="100" >
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block">
                                                            <label>Part Notes</label>
                                                            <input required name="Notes" ng-model="newBin.Notes" ng-maxlength="500" >
                                                        </md-input-container>
                                                    </div>

                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block">
                                                            <label>COO</label>
                                                            <md-select name="COOID" ng-model="newBin.COOID" id="COOID" required ng-change="GetCurrentTime(newBin,'coo_scan_time');">
                                                                <md-option value="{{ir.COOID}}" ng-repeat="ir in COOList">{{ir.COO}}</md-option>
                                                            </md-select>                                                            
                                                        </md-input-container>
                                                    </div>

                                                    <div class="col-md-12">
                                                        <div class="row"> 
                                                            <div class="col-md-4">
                                                                <md-input-container class="md-block includedsearch" style="opacity:0;">
                                                                    <label>Scan for Save</label>
                                                                    <input name="scan_for_save" ng-model="scan_for_save" ng-enter="AddSerialToContainer()" id="scan_for_save" style="width:2px;">
                                                                </md-input-container>
                                                            </div>
                                                            <div class="col-md-4 btns-row">                                              
                                                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                                                    data-ng-disabled="(!newBin.SerialNumber || !newBin.Notes || !newBin.SanitizationVerificationID || !newBin.UniversalModelNumber) || newBin.busy" ng-click="AddSerialToContainer()">
                                                                    <span ng-show="! newBin.busy">Save</span>
                                                                    <span ng-show="newBin.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                                </md-button>
                                                            </div>
                                                        </div> 
                                                    </div>

                                                </div>
                                            </md-tab>
                                            <!-- <md-tab label="SubComponent Inventory">
                                                <div class="mt-10">
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block includedsearch">
                                                            <label>Inventory SN</label>
                                                            <input required name="InventorySerialNumber" ng-model="newBin.InventorySerialNumber" ng-maxlength="50" id="InventorySerialNumber" ng-enter="GetCurrentTime(newBin,'inventory_serial_scan_time');ValidateInventorySerialNumber(newBin.InventorySerialNumber)" />
                                                            <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="SN" ng-disabled="!newBin.InventorySerialNumber" ng-click="GetCurrentTime(newBin,'inventory_serial_scan_time');ValidateInventorySerialNumber(newBin.InventorySerialNumber)">
                                                                <md-icon class="scanicon" md-svg-src="../assets/images/search.svg"></md-icon>
                                                            </md-button>
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block includedsearch">
                                                            <label>MPN</label>                                                            
                                                            <input required name="InventoryUniversalModelNumber" ng-model="newBin.InventoryUniversalModelNumber" ng-maxlength="100" ng-enter="GetCurrentTime(newBin,'inventory_mpn_scan_time');GetExactMPNSN(newBin.InventoryUniversalModelNumber)" />
                                                            <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right"
                                                                ng-disabled="!newBin.InventoryUniversalModelNumber" ng-click="GetCurrentTime(newBin,'inventory_mpn_scan_time');GetExactMPNSN(newBin.InventoryUniversalModelNumber)">
                                                                <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                                            </md-button>
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block">
                                                            <label>Part Notes</label>
                                                            <input required name="InventoryNotes" ng-model="newBin.InventoryNotes" ng-maxlength="500" >
                                                        </md-input-container>
                                                    </div>

                                                    <div class="col-md-12">
                                                        <div class="row"> 
                                                            <div class="col-md-4">
                                                                <md-input-container class="md-block includedsearch" style="opacity:0;">
                                                                    <label>Scan for SubComponent Save</label>
                                                                    <input name="scan_for_subcomponentsave" ng-model="scan_for_subcomponentsave" ng-enter="AddInventorySerialToContainer()" id="scan_for_subcomponentsave" style="width:2px;">
                                                                </md-input-container>
                                                            </div>
                                                            <div class="col-md-4 btns-row">                                              
                                                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                                                    data-ng-disabled="(!newBin.InventorySerialNumber || !newBin.InventoryNotes || !newBin.InventoryUniversalModelNumber) || newBin.busy" ng-click="AddInventorySerialToContainer()">
                                                                    <span ng-show="! newBin.busy">Save</span>
                                                                    <span ng-show="newBin.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                                </md-button>
                                                            </div>
                                                        </div> 
                                                    </div>

                                                </div>
                                            </md-tab> -->
                                            <md-tab label="Byproduct">
                                                <div class="mt-10">
                                                    <div class="col-md-4 col-md-offset-4">
                                                        <md-input-container class="md-block">
                                                            <label>Part Type</label>
                                                            <md-select name="byproduct_id" ng-model="newBin.byproduct_id" required ng-disabled="byproduct_id">
                                                                <!-- <md-option value="{{pro.byproduct_id}}" ng-repeat="pro in ByProducts">{{pro.part_type}}</md-option> -->
                                                                <md-option value="{{pro.byproduct_id}}" ng-repeat="pro in ByProducts">{{pro.parttype}}</md-option>
                                                            </md-select>
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block includedsearch" style="opacity:0;">
                                                            <label>Scan for Save</label>
                                                            <input name="scan_for_save_byproduct" ng-model="scan_for_save_byproduct" ng-enter="AddByProductToContainer()" id="scan_for_save_byproduct" style="width:2px;">
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-12 btns-row">
                                                        <md-button class="md-raised btn-w-md md-primary btn-w-md" ng-disabled="(!newBin.byproduct_id) || newBin.busy" ng-click="AddByProductToContainer()">
                                                            <span ng-show="! newBin.busy" >Save</span>
                                                            <span ng-show="newBin.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                        </md-button>
                                                    </div>
                                                </div>
                                            </md-tab>
                                            <md-tab label="Recovered Gear">
                                                <div class="mt-10">
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block includedsearch">
                                                            <label>SN</label>
                                                            <input required name="SerialNumberServer" ng-model="newBin.SerialNumberServer" ng-maxlength="50" id="SerialNumberServer" ng-enter="GetCurrentTime(newBin,'server_serial_scan_time');ValidateServerSerialNumber(newBin.SerialNumberServer)"/>
                                                            <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="SN" ng-disabled="!newBin.SerialNumberServer" ng-click="GetCurrentTime(newBin,'server_serial_scan_time');ValidateServerSerialNumber(newBin.SerialNumberServer)">
                                                                <md-icon class="scanicon" md-svg-src="../assets/images/search.svg"></md-icon>
                                                            </md-button>
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block">
                                                            <label>Type</label>
                                                            <input name="Type" ng-model="newBin.Type" data-ng-disabled="true">
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block" ng-if="newBin.Type == 'Server' || newBin.Type == 'SERVER' || newBin.Type == 'server'">
                                                            <label>Sanitization Verification ID</label>
                                                            <input required name="ServerSanitizationVerificationID" id="ServerSanitizationVerificationID" ng-model="newBin.ServerSanitizationVerificationID" data-ng-disabled="true" ng-maxlength="100" ng-enter="FocusNextField('scan_for_save_server','0')">
                                                            <!-- <input required name="ServerSanitizationVerificationID" id="ServerSanitizationVerificationID" ng-model="newBin.ServerSanitizationVerificationID"  ng-maxlength="100" ng-enter="FocusNextField('scan_for_save_server','0')"> -->
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block" ng-if="newBin.Type != 'Server' && newBin.Type != 'SERVER' && newBin.Type != 'server'">
                                                            <label>MPN</label>
                                                            <input required name="MPN" ng-model="newBin.MPN" ng-maxlength="100" ng-enter="GetCurrentTime(newBin,'server_mpn_scan_time');AutoNavigateInventorySave()" />
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block">
                                                            <label>Part Notes</label>
                                                            <input required name="NotesServer" ng-model="newBin.NotesServer" ng-maxlength="500" >
                                                        </md-input-container>
                                                    </div>

                                                    <div class="col-md-3" ng-show="canServerCountbeShown()">
                                                        <md-switch class="mt-10" ng-model="newBin.ByPassContainerMatching" aria-label="Notes Required" ng-true-value="'1'" ng-false-value="'0'"> Bypass Container and Rack Matching</md-switch>
                                                    </div>
                                                    
                                                    <div class="col-md-12">
                                                        <div class="row"> 
                                                            <div class="col-md-4">
                                                                <md-input-container class="md-block includedsearch" style="opacity:0;">
                                                                    <label>Scan for Save</label>
                                                                    <input name="scan_for_save_server" ng-model="scan_for_save_server" ng-enter="AddServerSerialToContainer()" id="scan_for_save_server" style="width:2px;">
                                                                </md-input-container>
                                                            </div>
                                                            <div class="col-md-4 btns-row">                                              
                                                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                                                    data-ng-disabled="(!newBin.SerialNumberServer || !newBin.NotesServer ) || newBin.busy" ng-click="AddServerSerialToContainer()">
                                                                    <span ng-show="! newBin.busy">Save</span>
                                                                    <span ng-show="newBin.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                                </md-button>
                                                            </div>
                                                        </div> 
                                                    </div>
                                                </div>
                                            </md-tab>
                                            <md-tab label="Reuse Media">
                                                <div class="mt-10">
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block includedsearch">
                                                            <label>Media SN</label>
                                                            <input required name="MediaSerialNumber" ng-model="newBin.MediaSerialNumber" ng-maxlength="50" id="MediaSerialNumber" ng-enter="GetCurrentTime(newBin,'media_serial_scan_time');ValidateMediaSerialNumber(newBin.MediaSerialNumber)"/>
                                                            <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="SN" ng-disabled="!newBin.MediaSerialNumber" ng-click="GetCurrentTime(newBin,'media_serial_scan_time');ValidateMediaSerialNumber(newBin.MediaSerialNumber)">
                                                                <md-icon class="scanicon" md-svg-src="../assets/images/search.svg"></md-icon>
                                                            </md-button>
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block">
                                                            <label>Part Type</label>                                                            
                                                            <md-select name="ReuseMedia_part_type" ng-model="newBin.ReuseMedia_part_type" required ng-disabled="true">
                                                                <md-option value="HDD">HDD</md-option>
                                                                <md-option value="SSD">SSD</md-option>
                                                            </md-select>
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <md-input-container class="md-block includedsearch">
                                                            <label>MPN</label>
                                                            <input required name="ReuseMediaUniversalModelNumber" id="ReuseMediaUniversalModelNumber" ng-model="newBin.ReuseMediaUniversalModelNumber" ng-maxlength="100" ng-enter="GetCurrentTime(newBin,'media_mpn_scan_time');AddMediaToContainer()" />
                                                        </md-input-container>
                                                    </div>
                                                    <div class="col-md-12 btns-row">
                                                        <md-button class="md-raised btn-w-md md-primary btn-w-md" ng-disabled="(!newBin.MediaSerialNumber || !newBin.ReuseMedia_part_type || !newBin.ReuseMediaUniversalModelNumber) || newBin.busy" ng-click="AddMediaToContainer()">
                                                            <span ng-show="! newBin.busy" >Save</span>
                                                            <span ng-show="newBin.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                        </md-button>
                                                    </div>
                                                </div>
                                            </md-tab>

                                        </md-tabs>
                                    </div>
                                    <!--Tabs Closed-->


                                </form>
                            </div>

                            <div class="row"  ng-show="ContainerSerials.length > 0">

                                <div class="col-md-12">
                                    <div class="col-md-12">

                                        <div ng-show="ContainerSerials" class="pull-right">
                                            <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                            of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div>

                                        <div class="table-responsive" style="overflow: auto;">
                                            <table class="table table-striped" md-table md-row-select>

                                                <thead md-head>

                                                    <tr class="th_sorting" md-row>
                                                        <th style="min-width: 80px;">Actions</th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('SerialNumber')" ng-class="{'orderby' : OrderBy == 'SerialNumber'}">
                                                            <div>
                                                                Serial Number<i class="fa fa-sort pull-right" ng-show="OrderBy != 'SerialNumber'"></i>
                                                                <span ng-show="OrderBy == 'SerialNumber'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('InventorySerialNumber')" ng-class="{'orderby' : OrderBy == 'InventorySerialNumber'}">
                                                            <div style="min-width: 220px;">
                                                                Inventory Serial Number<i class="fa fa-sort pull-right" ng-show="OrderBy != 'InventorySerialNumber'"></i>
                                                                <span ng-show="OrderBy == 'InventorySerialNumber'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ServerSerialNumber')" ng-class="{'orderby' : OrderBy == 'ServerSerialNumber'}">
                                                            <div style="min-width: 220px;">
                                                                Switch / Server SN<i class="fa fa-sort pull-right" ng-show="OrderBy != 'ServerSerialNumber'"></i>
                                                                <span ng-show="OrderBy == 'ServerSerialNumber'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('MediaSerialNumber')" ng-class="{'orderby' : OrderBy == 'MediaSerialNumber'}">
                                                            <div style="min-width: 220px;">
                                                                Media SN<i class="fa fa-sort pull-right" ng-show="OrderBy != 'MediaSerialNumber'"></i>
                                                                <span ng-show="OrderBy == 'MediaSerialNumber'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('Notes')" ng-class="{'orderby' : OrderBy == 'Notes'}">
                                                            <div>
                                                                Part Notes <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Notes'"></i>
                                                                <span ng-show="OrderBy == 'Notes'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('UniversalModelNumber')" ng-class="{'orderby' : OrderBy == 'UniversalModelNumber'}">
                                                            <div>
                                                                MPN <i class="fa fa-sort pull-right" ng-show="OrderBy != 'UniversalModelNumber'"></i>
                                                                <span ng-show="OrderBy == 'UniversalModelNumber'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('part_type')" ng-class="{'orderby' : OrderBy == 'part_type'}">
                                                            <div>
                                                                Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'part_type'"></i>
                                                                <span ng-show="OrderBy == 'part_type'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('COO')" ng-class="{'orderby' : OrderBy == 'COO'}">
                                                            <div>
                                                                COO <i class="fa fa-sort pull-right" ng-show="OrderBy != 'COO'"></i>
                                                                <span ng-show="OrderBy == 'COO'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('BinName')" ng-class="{'orderby' : OrderBy == 'BinName'}">
                                                            <div>
                                                                Bin ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'BinName'"></i>
                                                                <span ng-show="OrderBy == 'BinName'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('CreatedDate')" ng-class="{'orderby' : OrderBy == 'CreatedDate'}">
                                                            <div>
                                                                Date Added <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CreatedDate'"></i>
                                                                <span ng-show="OrderBy == 'CreatedDate'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th>
                                                            <div>Move To BIN</div>
                                                        </th>
                                                    </tr>

                                                    <tr md-row class="errornone">
                                                        <td></td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="SerialNumber" ng-model="filter_text[0].SerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="InventorySerialNumber" ng-model="filter_text[0].InventorySerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>

                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="ServerSerialNumber" ng-model="filter_text[0].ServerSerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>

                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="MediaSerialNumber" ng-model="filter_text[0].MediaSerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>

                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="Notes" ng-model="filter_text[0].Notes" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="UniversalModelNumber" ng-model="filter_text[0].UniversalModelNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="part_type" ng-model="filter_text[0].part_type" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>

                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="COO" ng-model="filter_text[0].COO" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="BinName" ng-model="filter_text[0].BinName" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="CreatedDate" ng-model="filter_text[0].CreatedDate" ng-change="MakeFilter()" aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td></td>
                                                    </tr>
                                                </thead>

                                                <tbody md-body ng-show="ContainerSerials.length > 0">
                                                    <tr md-row ng-repeat="serial in ContainerSerials">
                                                        <td class="actionicons" style="min-width: 60px;">
                                                            <a href="{{host}}label/master/examples/shipmentseriallabel.php?id={{serial.SerialID}}" target="_blank">
                                                                <i class="material-icons print" role="img" aria-label="print">print</i>
                                                            </a>
                                                        </td>

                                                        <td md-cell>
                                                            {{serial.SerialNumber}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.InventorySerialNumber}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.ServerSerialNumber}}
                                                        </td>

                                                        <td md-cell>
                                                            {{serial.MediaSerialNumber}}
                                                        </td>

                                                        <td md-cell>
                                                            {{serial.Notes}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.UniversalModelNumber}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.part_type}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.COO}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.BinName}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block md-no-float includedsearch tdinput" ng-show="serial.byproduct_id == '' || serial.byproduct_id == NULL">
                                                                <input required name="BinName" ng-model="serial.BinName" required>
                                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="DeleteSerialFromContainer(serial,$event)" ng-disabled="!serial.BinName">
                                                                    Go
                                                                </md-button>
                                                            </md-input-container>

                                                            <i class="material-icons text-danger" ng-click="DeleteByProductFromContainer(serial,$event)" ng-show="serial.byproduct_id > 0">close</i>

                                                        </td>
                                                    </tr>
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <td colspan="11">
                                                            <div>
                                                                <ul class="pagination">
                                                                    <li ng-class="prevPageDisabled()">
                                                                        <a href ng-click="firstPage()"><< First</a>
                                                                    </li>
                                                                    <li ng-class="prevPageDisabled()">
                                                                        <a href ng-click="prevPage()"><< Prev</a>
                                                                    </li>
                                                                    <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                        <a style="cursor:pointer;">{{n+1}}</a>
                                                                    </li>
                                                                    <li ng-class="nextPageDisabled()">
                                                                        <a href ng-click="nextPage()">Next >></a>
                                                                    </li>
                                                                    <li ng-class="nextPageDisabled()">
                                                                        <a href ng-click="lastPage()">Last >></a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>

                </md-card>
                <!--Create New Bin Close-->

                <!-- Back to Top Button -->
                <button id="backToTopBtn"
                        class="md-button md-fab md-raised md-primary"
                        style="position: fixed;
                               bottom: 30px;
                               right: 30px;
                               z-index: 1000;
                               display: none;
                               width: 56px;
                               height: 56px;
                               border-radius: 50%;
                               box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                               transition: all 0.3s ease;"
                        title="Back to Top"
                        ng-click="scrollToTop()">
                    <i class="material-icons" style="font-size: 24px;">keyboard_arrow_up</i>
                </button>

            </article>
        </div>
    </div>
</div>

<script>
    // Back to Top Button functionality
    $(document).ready(function() {
        var backToTopBtn = $('#backToTopBtn');

        // Show/hide button based on scroll position
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                backToTopBtn.fadeIn();
            } else {
                backToTopBtn.fadeOut();
            }
        });

        // Smooth scroll to top when button is clicked
        backToTopBtn.click(function() {
            $('html, body').animate({
                scrollTop: 0
            }, 600);
            return false;
        });

        // Add hover effect
        backToTopBtn.hover(
            function() {
                $(this).css('transform', 'scale(1.1)');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );
    });
</script>