
<div class="row page" data-ng-controller="User">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>User</span>
                        <div flex></div>
                            <a href="#!/UserList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to User List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>User Name</label>
                                    <input type="text" name="UserName"  ng-model="newUser['UserName']"  required ng-maxlength="45" ng-disabled="newUser.UserId" />
                                    <div class="error-space"> 
                                        <div ng-messages="material_signup_form.UserName.$error" multiple ng-if='material_signup_form.UserName.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 45.</div>                           
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>First Name</label>
                                    <input type="text" name="FirstName"  ng-model="newUser['FirstName']"  required ng-maxlength="45" />
                                    <div class="error-space"> 
                                    <div ng-messages="material_signup_form.FirstName.$error" multiple ng-if='material_signup_form.FirstName.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Middle Name</label>
                                    <input type="text" name="MiddleName"  ng-model="newUser['MiddleName']"  />
                                    <div class="error-space"> 
                                    <div ng-messages="material_signup_form.MiddleName.$error" multiple ng-if='material_signup_form.MiddleName.$dirty'>                                                      
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Last Name</label>
                                    <input type="text" name="LastName"  ng-model="newUser['LastName']"  required ng-maxlength="45" />
                                    <div class="error-space"> 
                                    <div ng-messages="material_signup_form.LastName.$error" multiple ng-if='material_signup_form.LastName.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Email</label>
                                    <input type="text" name="Email"  ng-model="newUser['Email']"  required ng-maxlength="45" />
                                    <div class="error-space"> 
                                    <div ng-messages="material_signup_form.Email.$error" multiple ng-if='material_signup_form.Email.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Password</label>
                                    <input type="password" name="Password"  ng-model="newUser['Password']"  required ng-maxlength="45" ng-pattern="/^\S*$/" />
                                    <div class="error-space"> 
                                    <div ng-messages="material_signup_form.Password.$error" multiple ng-if='material_signup_form.Password.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>
                                        <div ng-message="pattern" class="my-message">Space is not allowed in Password.</div>                           
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                            <md-input-container class="md-block">
                                    <label>Profile</label>   
                                    <md-select name="ProfileID" ng-model="newUser.ProfileID" aria-label="select" required>
                                        <md-option ng-repeat="profile in profiles" value="{{profile.ProfileID}}"> {{profile.ProfileName}} </md-option>
                                    </md-select>  
                                    <div class="error-space"> 
                                    <div ng-messages="material_signup_form.ProfileID.$error" multiple ng-if='material_signup_form.ProfileID.$dirty'>                            
                                        <div ng-message="required">This is required.</div>                                                               
                                    </div> 
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                            <md-input-container class="md-block">
                                    <label>Facility</label>   
                                    <md-select name="FacilityID" ng-model="newUser.FacilityID" aria-label="select" required>
                                        <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                    
                                    </md-select> 
                                    <div class="error-space"> 
                                    <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>                            
                                        <div ng-message="required">This is required.</div>                                                               
                                    </div>   
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                            <md-input-container class="md-block">
                                    <label>Language</label>   
                                    <md-select name="LanguageID" ng-model="newUser.LanguageID" aria-label="select">
                                        <md-option ng-repeat="language in languages" value="{{language.LanguageID}}"> {{language.Language}} </md-option>
                                    <!--  <md-option ng-repeat="facility in Facility" value="{{facility.FacilityID}}"> {{facility.FacilityName}} </md-option>  -->
                                    </md-select> 
                                    <div class="error-space"></div>  
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Program</label>
                                    <md-select name="Program" ng-model="newUser.Program" required aria-label="select">
                                        <md-option ng-repeat="Pro in Program" value="{{Pro.ProgramID}}"> {{Pro.ProgramName}} </md-option>
                                        <!-- <md-option value="Disposition/FA"> Disposition/FA </md-option>
                                        <md-option value="SPEED"> SPEED </md-option>
                                        <md-option value="Support"> Support </md-option>
                                        <md-option value="ReLo"> ReLo </md-option> -->
                                    </md-select>   
                                    <div class="error-space"> 
                                    <div ng-messages="material_signup_form.Program.$error" multiple ng-if='material_signup_form.Program.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>  
                                    </div>                                           
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Shift</label>
                                    <input type="text" name="Shift"  ng-model="newUser['Shift']" ng-maxlength="45" />
                                    <div class="error-space"> 
                                    <div ng-messages="material_signup_form.Shift.$error" multiple ng-if='material_signup_form.Shift.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Controller</label>
                                    <md-select multiple name="Controller" ng-model="newUser.Controller">
                                        <md-option value="SanitizationController"> Sanitization Controller </md-option>
                                        <md-option value="RemovalController"> Removal Controller </md-option>
                                        <md-option value="AuditController"> Audit Controller </md-option>
                                        <md-option value="ServerRecoveryController"> Server Recovery Controller </md-option>
                                        <md-option value="MediaRecoveryController"> Media Recovery Controller </md-option>
                                        <md-option value="PendingMediaController"> Pending Media Controller </md-option>
                                        <md-option value="DispositionOverrideController"> Disposition Override Controller </md-option>
                                        <md-option value="DestructionController"> Destruction Controller </md-option>

                                        <md-option value="TDRSpotterController"> TDR Spotter Controller </md-option>
                                        <md-option value="TDRLeadController"> TDR Lead Controller </md-option>
                                        <md-option value="TDRManagerController"> TDR Manager Controller </md-option>
                                        <md-option value="PickerController"> Picker Controller </md-option>
                                        <!-- <md-option value="BulkRecoveryController"> Bulk Recovery Controller </md-option> -->
                                    </md-select>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="newUser.Status" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="0"> In active </md-option>
                                    </md-select>  
                                    <div class="error-space">  
                                    <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>   
                                    </div>                                          
                                </md-input-container>
                            </div>


                            <div style="clear: both;"></div>

                            
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Manager</label>   
                                    <md-select name="Manager" ng-model="newUser.Manager" required  aria-label="select">
                                        <md-option ng-repeat="Manager in AcManager" value="{{Manager.UserId}}"> {{Manager.UserName}} </md-option>
                                        <md-option value="0" ng-selected=true>Other Manager</md-option>
                                    </md-select> 
                                    <div class="error-space"></div>  
                                </md-input-container>
                            </div>

                            <div class="col-md-3" ng-if="newUser.Manager==0">
                                <md-input-container class="md-block">
                                    <label>Other Manager</label>
                                    <input type="text" name="OtherManager"  ng-model="newUser['OtherManager']"  ng-maxlength="45" />
                                    <div class="error-space"> 
                                    <div ng-messages="material_signup_form.OtherManager.$error" multiple ng-if='material_signup_form.OtherManager.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                           

                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Accout Type</label>
                                    <md-select name="AccountType" ng-model="newUser.AccountType" required aria-label="select">
                                        <md-option value="BB"> BB </md-option>
                                        <md-option value="GB"> GB </md-option>
                                        <md-option value="YB"> YB </md-option>
                                    </md-select>   
                                    <div class="error-space"> 
                                        <div ng-messages="material_signup_form.AccountType.$error" multiple ng-if='material_signup_form.AccountType.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>  
                                    </div>                                           
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="newUser.BinBulkUploadEligible" aria-label="Bin Bulk Upload Eligible" ng-true-value="'1'" ng-false-value="'0'"> Bin Bulk Upload Eligible</md-switch>
                            </div>
                            <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="newUser.BulkRecoveryController" aria-label="Bulk Recovery Controller" ng-true-value="'1'" ng-false-value="'0'"> Bulk Recovery Controller</md-switch>
                            </div>

                            <!-- <div class="col-md-4">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newUser.SanitizationController" aria-label="SanitizationController" ng-true-value="'1'" ng-false-value="'0'" class="mb-10"> Sanitization Controller </md-checkbox>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newUser.RemovalController" aria-label="RemovalController" ng-true-value="'1'" ng-false-value="'0'" class="mb-10"> Removal Controller </md-checkbox>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newUser.AuditController" aria-label="AuditController" ng-true-value="'1'" ng-false-value="'0'" class="mb-10"> Audit Controller </md-checkbox>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newUser.ServerRecoveryController" aria-label="ServerRecoveryController" ng-true-value="'1'" ng-false-value="'0'" class="mb-10"> Server Recovery Controller </md-checkbox>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newUser.MediaRecoveryController" aria-label="MediaRecoveryController" ng-true-value="'1'" ng-false-value="'0'" class="mb-10"> Media recovery Controller </md-checkbox>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newUser.PendingMediaController" aria-label="PendingMediaController" ng-true-value="'1'" ng-false-value="'0'" class="mb-10"> Pending Media Controller </md-checkbox>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newUser.DispositionOverrideController" aria-label="DispositionOverrideController" ng-true-value="'1'" ng-false-value="'0'" class="mb-10"> Disposition Override Controller </md-checkbox>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>
                            <div class="col-md-4">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newUser.DestructionController" aria-label="DestructionController" ng-true-value="'1'" ng-false-value="'0'" class="mb-10"> Destruction Controller </md-checkbox>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div> -->

                            <div class="col-md-12 btns-row">
                                <a href="#!/UserList" style="text-decoration: none;">
                                <md-button class="md-button md-raised btn-w-md  md-default">
                                    Cancel
                                </md-button>
                                </a> 
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || newUser.busy" ng-click="CreateUser()">
                                <span ng-show="! newUser.busy">Save</span>
                                <span ng-show="newUser.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>

                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>