<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['TruckListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "TruckBookingList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Truck Booking List');
$header = array('Facility','Load #','Shipment Type','Parking Location','Carrier','Arrival Date','Expected Arrival Time','Expected Departure Time','Load Type','Load Quantity','Truck Reg','Trailer No','Driver','Driver ID','Notes','Status');

$sql = "select T.*,PL.ParkingLocationName,F.FacilityName,C.CarrierName from Truck T left join ParkingLocation PL on T.ParkingLocationID = PL.ParkingLocationID left join facility F on T.FacilityID = F.FacilityID left join Carrier C on T.CarrierID = C.CarrierID where T.Status NOT IN ('Complete', 'No Show', 'Failed') AND T.FacilityID = '" . mysqli_real_escape_string($connectionlink, $_SESSION['user']['FacilityID']) . "'";
if($data[0] && count($data[0]) > 0) {
           foreach ($data[0] as $key => $value) {
                if ($value != '') {
                    if ($key == 'FacilityName') {
                        $sql = $sql . " AND F.FacilityName like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'CarrierName') {
                        $sql = $sql . " AND C.CarrierName like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'ParkingLocationName') {
                        $sql = $sql . " AND PL.ParkingLocationName like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'ArrivalType') {
                        $sql = $sql . " AND T.ArrivalType like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'ArrivalDate') {
                        $sql = $sql . " AND T.ArrivalDate like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'ArrivalTime') {
                        $sql = $sql . " AND T.ArrivalTime like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'LoadType') {
                        $sql = $sql . " AND T.LoadType like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'TruckReg') {
                        $sql = $sql . " AND T.TruckReg like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'DriverName') {
                        $sql = $sql . " AND T.DriverName like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'DriverID') {
                        $sql = $sql . " AND T.DriverID like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'Notes') {
                        $sql = $sql . " AND T.Notes like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'LoadNumber') {
                        $sql = $sql . " AND T.LoadNumber like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'LoadQuantity') {
                        $sql = $sql . " AND T.LoadQuantity like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'TrailerNumber') {
                        $sql = $sql . " AND T.TrailerNumber like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    // Status filter removed - base query already filters to exclude Complete and No Show

                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

            if ($data['OrderBy'] == 'FacilityName') {
                    $sql = $sql . " order by F.FacilityName " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'CarrierName') {
                    $sql = $sql . " order by C.CarrierName " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'ParkingLocationName') {
                    $sql = $sql . " order by PL.ParkingLocationName " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'ArrivalType') {
                    $sql = $sql . " order by T.ArrivalType " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'ArrivalDate') {
                    $sql = $sql . " order by T.ArrivalDate " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'ArrivalTime') {
                    $sql = $sql . " order by T.ArrivalTime " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'LoadType') {
                    $sql = $sql . " order by T.LoadType " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'TruckReg') {
                    $sql = $sql . " order by T.TruckReg " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'DriverName') {
                    $sql = $sql . " order by T.DriverName " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'DriverID') {
                    $sql = $sql . " order by T.DriverID " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'Notes') {
                    $sql = $sql . " order by T.Notes " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'LoadNumber') {
                    $sql = $sql . " order by T.LoadNumber " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'LoadQuantity') {
                    $sql = $sql . " order by T.LoadQuantity " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'TrailerNumber') {
                    $sql = $sql . " order by T.TrailerNumber " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'Status') {
                    $sql = $sql . " order by T.Status " . $order_by_type . " ";
                }
        } else {
            $sql = $sql . " order by T.TruckID desc ";
        }
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
while($row = mysqli_fetch_assoc($query))
{
    if (!empty($row['ArrivalTime'])) {
        $arrival = new DateTime($row['ArrivalTime']);
        $arrival->modify('+30 minutes');
        $row['DepartureTime'] = $arrival->format('H:i');
    } else {
        $row['DepartureTime'] = null;
    }

    $row2  = array($row['FacilityName'],$row['LoadNumber'],$row['ArrivalType'],$row['ParkingLocationName'],$row['CarrierName'],$row['ArrivalDate'],$row['ArrivalTime'],$row['DepartureTime'],$row['LoadType'],$row['LoadQuantity'],$row['TruckReg'],$row['TrailerNumber'],$row['DriverName'],$row['DriverID'],$row['Notes'],$row['Status']);
    $rows[] = $row2;
}

$sheet_name = 'Truck Booking List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 