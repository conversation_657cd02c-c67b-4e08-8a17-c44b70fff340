ALTER TABLE `awsstg`.`disposition` ADD COLUMN `PickPathEligible` TINYINT NULL DEFAULT 0 AFTER `BulkRecoveryEditEligable`;
CREATE TABLE `awsstg`.`pick_configuration` (
  `ConfigurationID` INT NOT NULL AUTO_INCREMENT,
  `FacilityID` INT NULL DEFAULT NULL,
  `PickPathName` VARCHAR(100) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ConfigurationID`));

  CREATE TABLE `awsstg`.`pick_configuration_details` (
  `DetailID` INT NOT NULL AUTO_INCREMENT,
  `ConfigurationID` INT NULL DEFAULT NULL,
  `parttypeid` INT NULL DEFAULT NULL,
  `mpn` VARCHAR(100) NULL DEFAULT NULL,
  `FromDispositionID` INT NULL DEFAULT NULL,
  `ToDis<PERSON>ID` INT NULL DEFAULT NULL,
  `PickQuantity` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  `Status` VARCHAR(10) NULL DEFAULT NULL,
  PRIMARY KEY (`DetailID`));

  INSERT INTO `awsstg`.`tabs` (`TabID`, `TabName`, `TabLink`, `Description`, `AccountID`, `Status`, `Dashboard`, `Order`, `TabIcon`) VALUES ('171', 'Pick Tool', 'picktool', 'picktool', '1', '1', '0', '22', 'local_shipping');
  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('171', 'Pick Configuration', 'PickConfigurationDetailsList', 'PickConfigurationDetailsList', '', '1', '', '1', '0', '1', '0');
  


  CREATE TABLE `awsstg`.`pick_configuration_tracking` (
  `TrackID` INT NOT NULL AUTO_INCREMENT,
  `ConfigurationID` INT NULL DEFAULT NULL,
  `Action` TEXT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `ModuleName` VARCHAR(100) NULL DEFAULT NULL,
  PRIMARY KEY (`TrackID`));



ALTER TABLE `awsstg`.`pick_configuration` 
ADD COLUMN `parttypeid` INT NULL DEFAULT NULL AFTER `UpdatedBy`,
ADD COLUMN `FromDispositionID` INT NULL DEFAULT NULL AFTER `parttypeid`,
ADD COLUMN `ToDispositionID` INT NULL DEFAULT NULL AFTER `FromDispositionID`,
ADD COLUMN `Status` VARCHAR(10) NULL DEFAULT NULL AFTER `ToDispositionID`;

ALTER TABLE `awsstg`.`pick_configuration_details` 
ADD COLUMN `PickCompleted` INT NULL DEFAULT NULL COMMENT 'sofar completed count' AFTER `Status`;

ALTER TABLE `awsstg`.`users` ADD COLUMN `PickerController` TINYINT NULL DEFAULT 0 AFTER `TDRManagerController`;


CREATE TABLE `awsstg`.`pick_configuration_details_user_assignment` (
  `AssignID` INT NOT NULL AUTO_INCREMENT,
  `DetailID` INT NULL DEFAULT NULL,
  `UserId` INT NULL DEFAULT NULL,
  `UserName` VARCHAR(100) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`AssignID`));

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('171', 'Pick Path Assignment', 'PickPath', 'PickPath', '', '1', '', '1', '', '0', '2', '0');

ALTER TABLE `awsstg`.`pick_configuration_tracking` ADD COLUMN `DetailID` INT NULL DEFAULT NULL AFTER `ModuleName`;
INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('171', 'Process Pick Path', 'ProcessPickPath', 'ProcessPickPath', '', '1', '', '1', '', '0', '3', '0');




CREATE TABLE `awsstg`.`pick_configuration_details_custompallet_mapping` (
  `MappingID` INT NOT NULL AUTO_INCREMENT,
  `CustomPalletID` INT NULL DEFAULT NULL,
  `BinName` VARCHAR(200) NULL DEFAULT NULL,
  `UserID` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `DetailID` INT NULL DEFAULT NULL,
  PRIMARY KEY (`MappingID`));


  CREATE TABLE awsstg.`picktool_asset_movement` (
  `MovementID` int NOT NULL AUTO_INCREMENT,
  `AssetScanID` bigint DEFAULT NULL,
  `SerialNumber` varchar(100) DEFAULT NULL,
  `FromCustomPalletID` int DEFAULT NULL,
  `ToCustomPalletID` int DEFAULT NULL,
  `FacilityID` int DEFAULT NULL,
  `UniversalModelNumber` varchar(100) DEFAULT NULL,
  `part_type` varchar(100) DEFAULT NULL,
  `disposition_id` int DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `MovementType` text,
  `FromBinName` text,
  `ToBinName` text,  
  `ServerID` int DEFAULT NULL,
  `MediaID` int DEFAULT NULL,
  `MovementFrom` varchar(100) DEFAULT NULL,
  `MovementTo` varchar(100) DEFAULT NULL,
  `FromShippingContainerID` varchar(100) DEFAULT NULL,
  `ToShippingContainerID` varchar(100) DEFAULT NULL,
  `ConfigurationID` int DEFAULT NULL,
  `DetailID` int DEFAULT NULL,
  `event_id` varchar(45) DEFAULT NULL,
  `batch_event_flag` varchar(45) DEFAULT NULL,
  `UnserializedRecoveryRecordID` int DEFAULT NULL,
  PRIMARY KEY (`MovementID`)
) 

 ALTER TABLE `awsstg`.`pick_configuration` CHANGE COLUMN `Status` `Status` VARCHAR(15) NULL DEFAULT NULL ;

 ALTER TABLE `awsstg`.`pick_configuration_details` CHANGE COLUMN `Status` `Status` VARCHAR(15) NULL DEFAULT NULL ;


 CREATE TABLE awsstg.`pick_assign_disposition_eligibility` (
  `EligibilityID` int NOT NULL AUTO_INCREMENT,
  `FacilityID` int DEFAULT NULL,
  `DispositionFrom` int DEFAULT NULL,
  `DispositionTo` int DEFAULT NULL,  
  `Status` varchar(10) DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  `part_types` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`EligibilityID`),
  KEY `facilityindex_idx` (`FacilityID`),
  KEY `dispositionfromindex_idx1` (`DispositionFrom`),
  KEY `dispositiontoindex_idx1` (`DispositionTo`),
  CONSTRAINT `dispositionfromindex1` FOREIGN KEY (`DispositionFrom`) REFERENCES `disposition` (`disposition_id`),
  CONSTRAINT `dispositiontoindex1` FOREIGN KEY (`DispositionTo`) REFERENCES `disposition` (`disposition_id`),
  CONSTRAINT `facilityindex1` FOREIGN KEY (`FacilityID`) REFERENCES `facility` (`FacilityID`)
)


INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('171', 'Pick Disposition Eligibility', 'AssignDispositionIneligibilityList', 'AssignDispositionIneligibilityList', '', '1', '', '1', '', '0', '4', '0');


ALTER TABLE `awsdev`.`pick_configuration_details_user_assignment` 
ADD COLUMN `ConfigurationID` INT NULL DEFAULT NULL AFTER `CreatedBy`;

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('171', 'Pick Configuration Completed', 'PickConfigurationDetailsListCompleted', 'PickConfigurationDetailsListCompleted', '', '1', '', '1', '', '0', '5', '0');


ALTER TABLE `awsstg`.`picktool_asset_movement` 
ADD COLUMN `ToBinNameLocationID` INT NULL DEFAULT NULL AFTER `UnserializedRecoveryRecordID`,
ADD COLUMN `DetailStatus` VARCHAR(20) NULL DEFAULT NULL AFTER `ToBinNameLocationID`;