<?php
session_start();
include_once("../../config.php");
$data = $_SESSION['PickConfigurationDetailsxls'];
require_once("../../Truckyard/templates/xlsxwriter.class.php");
require_once("../../Truckyard/templates/xlsxwriterplus.class.php");
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "PickConfigurationDetailsList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$datatoday = array('Generated Date',$today);
$datahead = array('Pick Configuration Details List');
$header = array('Pick Path','Part Type','MPN','Pick Progress','Pick From Disposition','Assign Disposition','Pick Created Date','Detail Status','Configuration Status');

$facilityID = $_SESSION['user']['FacilityID'];
$orderBy = isset($data['OrderBy']) ? $data['OrderBy'] : 'CreatedDate';
$orderByType = isset($data['OrderByType']) ? $data['OrderByType'] : 'desc';

// Build the base query - include configurations with all details (no status restriction)
$query = "SELECT pc.PickPathName,
          COALESCE(pt.parttype, 'No Part Type') as parttype,
          COALESCE(pcd.mpn, 'No Details Added') as mpn,
          CONCAT(COALESCE(pcd.PickCompleted, 0), ' / ', COALESCE(pcd.PickQuantity, 0)) as PickProgress,
          COALESCE(fd.disposition, 'No From Disposition') as FromDisposition,
          COALESCE(td.disposition, 'No To Disposition') as ToDisposition,
          COALESCE(pcd.CreatedDate, pc.CreatedDate) as CreatedDate,
          COALESCE(pcd.Status, 'Pending') as DetailStatus,
          pc.Status as ConfigurationStatus
          FROM pick_configuration pc
          LEFT JOIN pick_configuration_details pcd ON pc.ConfigurationID = pcd.ConfigurationID
          LEFT JOIN parttype pt ON pcd.parttypeid = pt.parttypeid
          LEFT JOIN disposition fd ON pcd.FromDispositionID = fd.disposition_id
          LEFT JOIN disposition td ON pcd.ToDispositionID = td.disposition_id
          WHERE pc.FacilityID = '" . mysqli_real_escape_string($connectionlink, $facilityID) . "'
          AND pc.Status != 'Completed'";

// Add filters (following TruckList pattern)
if (isset($data['filter']) && !empty($data['filter'])) {
    foreach ($data['filter'] as $key => $value) {
        if ($value != '') {
            if ($key == 'PickPathName') {
                $query .= " AND pc.PickPathName LIKE '%" . mysqli_real_escape_string($connectionlink, $value) . "%'";
            }
            if ($key == 'parttype') {
                $query .= " AND pt.parttype LIKE '%" . mysqli_real_escape_string($connectionlink, $value) . "%'";
            }
            if ($key == 'mpn') {
                $query .= " AND pcd.mpn LIKE '%" . mysqli_real_escape_string($connectionlink, $value) . "%'";
            }
            if ($key == 'FromDisposition') {
                $query .= " AND fd.disposition LIKE '%" . mysqli_real_escape_string($connectionlink, $value) . "%'";
            }
            if ($key == 'ToDisposition') {
                $query .= " AND td.disposition LIKE '%" . mysqli_real_escape_string($connectionlink, $value) . "%'";
            }
            if ($key == 'DetailStatus') {
                $query .= " AND pcd.Status LIKE '%" . mysqli_real_escape_string($connectionlink, $value) . "%'";
            }
            if ($key == 'ConfigurationStatus') {
                $query .= " AND pc.Status LIKE '%" . mysqli_real_escape_string($connectionlink, $value) . "%'";
            }
            if ($key == 'CreatedDate') {
                $query .= " AND DATE(pcd.CreatedDate) = '" . mysqli_real_escape_string($connectionlink, $value) . "'";
            }
        }
    }
}

// Add ordering
$validOrderColumns = array('PickPathName', 'parttype', 'mpn', 'PickQuantity', 'FromDisposition', 'ToDisposition', 'CreatedDate', 'Status');
if (in_array($orderBy, $validOrderColumns)) {
    if ($orderBy == 'PickPathName') {
        $query .= " ORDER BY pc.PickPathName " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
    } elseif ($orderBy == 'parttype') {
        $query .= " ORDER BY pt.parttype " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
    } elseif ($orderBy == 'FromDisposition') {
        $query .= " ORDER BY fd.disposition " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
    } elseif ($orderBy == 'ToDisposition') {
        $query .= " ORDER BY td.disposition " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
    } else {
        $query .= " ORDER BY pcd." . $orderBy . " " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
    }
} else {
    $query .= " ORDER BY pcd.CreatedDate DESC";
}

$result = mysqli_query($connectionlink, $query);
$rows = array();

while($row = mysqli_fetch_assoc($result))
{
    // Format the created date
    if (!empty($row['CreatedDate'])) {
        $createdDate = new DateTime($row['CreatedDate']);
        $row['CreatedDate'] = $createdDate->format('m/d/Y H:i');
    }
    
    // Handle null MPN
    if (empty($row['mpn'])) {
        $row['mpn'] = 'N/A';
    }

    $row2 = array(
        $row['PickPathName'],
        $row['parttype'],
        $row['mpn'],
        $row['PickProgress'],
        $row['FromDisposition'],
        $row['ToDisposition'],
        $row['CreatedDate'],
        $row['DetailStatus'],
        $row['ConfigurationStatus']
    );
    $rows[] = $row2;
}

$sheet_name = 'Pick Configuration Details';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 8);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?>
