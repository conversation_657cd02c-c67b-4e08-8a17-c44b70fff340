<div class="row page" data-ng-controller="Truck">
    <div class="col-md-12">
        <article class="article">
            <md-card class="no-margin-h">                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Truck Booking</span>
                        <div flex></div>
                           <a href="#!/TruckList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i>Back to List
                            </a>
                    </div>
                </md-toolbar>                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <md-select name="FacilityID" ng-model="Truck.FacilityID" required ng-disabled="true">
                                        <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facilities">{{fac.FacilityName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Arrival Type</label>
                                    <md-select name="ArrivalType" ng-model="Truck.ArrivalType" required aria-label="select">
                                        <md-option value="Inbound"> Inbound </md-option>
                                        <md-option value="Outbound"> Outbound </md-option>
                                    </md-select>  
                                    <div class="error-sapce"> 
                                        <div ng-messages="material_signup_form.ArrivalType.$error" multiple ng-if='material_signup_form.ArrivalType.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Type</label>
                                    <md-select name="ParkTypeID" ng-model="Truck.ParkTypeID" required ng-change="onParkTypeChange()">
                                        <md-option value="{{ParkType.ParkTypeID}}" ng-repeat="ParkType in ParkTypes">{{ParkType.ParkTypeName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ParkTypeID.$error" multiple ng-if='material_signup_form.ParkTypeID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Location</label>
                                    <md-select name="ParkingLocationID" ng-model="Truck.ParkingLocationID" required ng-disabled="!Truck.ParkTypeID">
                                        <md-option value="{{ParkingLocation.ParkingLocationID}}" ng-repeat="ParkingLocation in FilteredParkingLocations">{{ParkingLocation.ParkingLocationName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ParkingLocationID.$error" multiple ng-if='material_signup_form.ParkingLocationID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                        <div ng-if="!Truck.ParkTypeID" style="color: #666; font-size: 12px; margin-top: 5px;">
                                            Please select a Parking Type first.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Carrier</label>
                                    <md-select name="CarrierID"
                                               ng-model="Truck.CarrierID"
                                               required
                                               ng-change="onCarrierChange()">
                                        <md-option ng-value="Carrier.CarrierID" ng-repeat="Carrier in Carriers">
                                            {{Carrier.CarrierName}}
                                        </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.CarrierID.$error" multiple ng-if='material_signup_form.CarrierID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Expected Arrival Date</label>
                                    <md-datepicker ng-model="Truck.ArrivalDate" required
                                    md-min-date="minDate"
                                    ng-change="onDateChange()"
                                    input-aria-describedby="datepicker-description"
                                    input-aria-labelledby="datepicker-header "></md-datepicker>
                                    <div class="error-space">
                                        <div ng-show="showDateError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                            Past dates are not allowed. Please select today's date or a future date.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Expected Arrival Time</label>
                                    <md-select name="ArrivalTime" ng-model="Truck.ArrivalTime" required ng-change="onTimeChange()">
                                        <md-option value="{{timeSlot.value}}" ng-repeat="timeSlot in filteredTimeSlots">{{timeSlot.display}}</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.ArrivalTime.$error" multiple ng-if='material_signup_form.ArrivalTime.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                        <div ng-show="showTimeError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                            Past times are not allowed for today's date. Please select a future time.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <!--  <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Departure Time</label>
                                    <input type="time" required name="DepartureTime" ng-model="Truck.DepartureTime" >
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.DepartureTime.$error" multiple ng-if='material_signup_form.DepartureTime.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                        </div>
                                    </div>
                                </md-input-container>
                            </div> -->
                             <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Load Type</label>
                                    <md-select name="LoadType" ng-model="Truck.LoadType" required aria-label="select">
                                        <md-option value="Pallet"> Pallet </md-option>
                                        <md-option value="Rack"> Rack </md-option>
                                        <md-option value="Mixed"> Mixed </md-option>
                                        <md-option value="Other"> Other </md-option>
                                    </md-select>  
                                     <div class="error-sapce"> 
                                        <div ng-messages="material_signup_form.LoadType.$error" multiple ng-if='material_signup_form.LoadType.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Load Number</label>
                                    <input type="text" name="LoadNumber" ng-model="Truck['LoadNumber']" required ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.LoadNumber.$error" multiple ng-if='material_signup_form.LoadNumber.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 100.</div>
                                        <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Load Quantity *</label>
                                    <input type="number" name="LoadQuantity" ng-model="Truck['LoadQuantity']" required min="1" step="1" />
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.LoadQuantity.$error" multiple ng-if='material_signup_form.LoadQuantity.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="min">Minimum value is 1.</div>
                                            <div ng-message="number">Please enter a valid number.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Vehicle Type</label>
                                    <md-select name="TruckTypeID" ng-model="Truck.TruckTypeID" required>
                                        <md-option value="{{TruckType.TruckTypeID}}" ng-repeat="TruckType in TruckTypes">{{TruckType.TruckTypeName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.TruckTypeID.$error" multiple ng-if='material_signup_form.TruckTypeID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-12" style="margin-bottom: 5px;">
                                <small style="color: #666; font-style: italic;">Either Truck Reg or Trailer Number is required (at least one field must be filled)</small>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Truck Reg</label>
                                    <input type="text" name="TruckReg" ng-model="Truck['TruckReg']" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" ng-change="validateVehicleIdentification()" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.TruckReg.$error" multiple ng-if='material_signup_form.TruckReg.$dirty'>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 100.</div>
                                        <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                    </div>
                                    <div ng-if="showVehicleIdentificationError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                        Either Truck Reg or Trailer Number is required.
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-1" style="display: flex; align-items: center; justify-content: center; padding-top: 20px;">
                                <span style="font-weight: 500; color: #666; font-size: 14px;">OR</span>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Trailer Number</label>
                                    <input type="text" name="TrailerNumber" ng-model="Truck['TrailerNumber']" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" ng-change="validateVehicleIdentification()" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.TrailerNumber.$error" multiple ng-if='material_signup_form.TrailerNumber.$dirty'>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 100.</div>
                                        <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                    </div>
                                    <div ng-if="showVehicleIdentificationError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                        Either Truck Reg or Trailer Number is required.
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Driver/POC</label>
                                    <input type="text" name="DriverName" ng-model="Truck['DriverName']" ng-maxlength="100" ng-pattern="/^[a-zA-Z\s\.\-_]*$/" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.DriverName.$error" multiple ng-if='material_signup_form.DriverName.$dirty && Truck.DriverName'>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Driver ID</label>
                                    <input type="text" name="DriverID" ng-model="Truck['DriverID']" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.DriverID.$error" multiple ng-if='material_signup_form.DriverID.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Shipment Ticket ID's</label>
                                    <input type="text" name="ShipmentTicketID" ng-model="Truck['ShipmentTicketID']" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_,]+$/" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ShipmentTicketID.$error" multiple ng-if='material_signup_form.ShipmentTicketID.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens, underscores and commas are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Seal ID</label>
                                    <input type="text" name="SealID" ng-model="Truck['SealID']" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.SealID.$error" multiple ng-if='material_signup_form.SealID.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Classification Type</label>
                                    <md-select name="ClassificationType"
                                               ng-model="Truck.ClassificationType"
                                               required
                                               ng-change="onClassificationTypeChange()">
                                        <md-option value="All">All</md-option>
                                        <md-option value="UEEE">UEEE</md-option>
                                        <md-option value="WEEE">WEEE</md-option>
                                    </md-select>
                                     <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ClassificationType.$error" multiple ng-if='material_signup_form.ClassificationType.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                        <div ng-if="showWEEEClassificationError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                            WEEE classification requires waste collection eligibility for the selected carrier.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Waste Collection Permit</label>
                                    <input type="text"
                                       name="WasteCollectionPermit"
                                       ng-model="Truck.WasteCollectionPermit"
                                       ng-minlength="3"
                                       ng-maxlength="100"
                                       ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/"
                                       ng-disabled="!((Truck.ClassificationType === 'WEEE' || Truck.ClassificationType === 'All') && selectedCarrier && selectedCarrier.WasteCollectionEligible == 1)" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.WasteCollectionPermit.$error" multiple ng-if='material_signup_form.WasteCollectionPermit.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                        <div ng-if="!((Truck.ClassificationType === 'WEEE' || Truck.ClassificationType === 'All') && selectedCarrier && selectedCarrier.WasteCollectionEligible == 1)" style="color: #666; font-size: 12px; margin-top: 5px;">
                                            Available when WEEE/All classification is selected with an eligible carrier.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Notes</label>
                                    <input type="text" name="Notes" ng-model="Truck['Notes']" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_,]+$/" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.Notes.$error" multiple ng-if='material_signup_form.Notes.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens, underscores and commas are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="Truck.Status" required aria-label="select">
                                        <md-option value="Reserved"> Reserved </md-option>
                                        <md-option value="Requested"> Requested </md-option>
                                    </md-select>  
                                     <div class="error-sapce"> 
                                    <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/TruckList" style="text-decoration: none;">
                                    <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || showVehicleIdentificationError || showWEEEClassificationError" ng-click="TruckSave()">
                                <span ng-show="! Truck.busy">Save</span>
                                <span ng-show="Truck.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>