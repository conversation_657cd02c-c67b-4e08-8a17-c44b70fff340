<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['BinHistoryExcel'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');

// Get bin name for filename - we'll need to query it since it's not in tracking data
include_once("../../connection.php");
$obj1 = new Connection();
$connectionlink = Connection::DBConnect();

$binName = 'Unknown';
if (isset($data['CustomPalletID'])) {
    $query = "SELECT BinName FROM custompallet WHERE CustomPalletID = '" . mysqli_real_escape_string($connectionlink, $data['CustomPalletID']) . "'";
    $q = mysqli_query($connectionlink, $query);
    if ($q && mysqli_num_rows($q) > 0) {
        $row = mysqli_fetch_assoc($q);
        $binName = $row['BinName'];
    }
}

$filename = "BinHistory_" . preg_replace('/[^A-Za-z0-9_\-]/', '_', $binName) . "_" . date('Y-m-d') . ".xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');

$datatoday = array('Generated Date',$today);
$datahead = array('Bin History Report - ' . $binName);
$header = array('Date & Time', 'User', 'Action', 'Module Name');

$rows = array();

if (isset($data['HistoryData']) && is_array($data['HistoryData'])) {
    foreach ($data['HistoryData'] as $record) {
        $row = array();

        $row[] = $record['CreatedDate'] ?? '';
        $row[] = $record['CreatedByName'] ?? 'System';
        $row[] = $record['Action'] ?? '';
        $row[] = $record['ModuleName'] ?? 'N/A';

        $rows[] = $row;
    }
}

$sheet_name = 'Bin History';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 3);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);

foreach($rows as $row11) {
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
}

$writer->writeToStdOut();
exit(0);
?>
