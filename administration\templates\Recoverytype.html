
<div class="row page" data-ng-controller="Recoverytype">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Recovery Type Creation(s)</span>
                        <div flex></div>
                            <a href="#!/RecoverytypeList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a>
                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <md-select name="FacilityID" ng-model="Recoverytype.FacilityID" required ng-disabled="true">
                                        <md-option ng-repeat="facility in Facility | filter:{ FacilityStatus : '!InActive'}" value="{{facility.FacilityID}}"> {{facility.FacilityName}} </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Recovery Type</label>
                                    <input type="text" name="Recoverytype"  ng-model="Recoverytype['Recoverytype']"  required ng-maxlength="45" />
                                    <div class="error-sapce">
                                            <div ng-messages="material_signup_form.Recoverytype.$error" multiple ng-if='material_signup_form.Recoverytype.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 45.</div>
                                            </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="Description"  ng-model="Recoverytype['Description']"  required ng-maxlength="250" />
                                    <div class="error-sapce">
                                    <div ng-messages="material_signup_form.Description.$error" multiple ng-if='material_signup_form.Description.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>
                                    </div>
                                </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="Recoverytype.Status" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="2"> In active </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                    <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/RecoverytypeList" style="text-decoration: none;">
                                    <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="RecoverytypeSave()">
                                <span ng-show="! Recoverytype.busy">Save</span>
                                <span ng-show="Recoverytype.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>

            <md-card class="no-margin-h" ng-show="Recoverytype.Recoverytypeid">
                 <md-toolbar class="md-table-toolbar md-default"ng-init="AddPartTypePanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="AddPartTypePanel = !AddPartTypePanel">
                            <i class="material-icons md-primary" ng-show="AddPartTypePanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! AddPartTypePanel">keyboard_arrow_down</i>
                            <span>Add Part Type</span>
                            <div flex></div>
                            <!-- <span>Count: <strong class="text-success">{{UnserialRecoveryPartsCount}}</strong></span> -->
                        </div>
                    </md-toolbar>
                <!-- <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Add Part Type</span>
                        <div flex></div>
                    </div>
                </md-toolbar> -->

                <div class="row" ng-show="AddPartTypePanel">
                    <form>
                        <div class="col-md-12">
                             <div class="row">
                                <div class="col-md-7 pr_md0">
                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Part Type</label>
                                            <md-select name="PartType" ng-model="RType.RecoveryPartType" required aria-label="select">
                                                <md-option ng-repeat="PartType in PartTypes" value="{{PartType.parttypeid}}"> {{PartType.parttype}} </md-option>
                                            </md-select>
                                            <div class="error-sapce"></div>
                                        </md-input-container>
                                    </div>
                                   <!--  <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Serialized</label>
                                            <input type="text" name="serialized" ng-model="Recoverytype.serialized" ng-min="1" ng-max="100" disabled />ng-change="GetParttypeseriallized()"
                                            <div class="error-space">
                                                <div ng-messages="material_signup_form.serialized.$error" multiple ng-if='material_signup_form.serialized.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="min">Minimum 1.</div>
                                                    <div ng-message="max">Maximum 100.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div> -->
                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Evaluation Result</label>
                                            <md-select name="EvaluationResult" ng-model="RType.EvaluationResultID" required>
                                                <md-option value="{{ir.input_id}}" ng-repeat="ir in InputResults">{{ir.input}}</md-option>
                                            </md-select>
                                            <!--<md-select name="EvaluationResult" ng-model="Recoverytype.EvaluationResult" required>
                                                <md-option value="e1">Abc Evalution</md-option>
                                            </md-select>-->
                                        </md-input-container>
                                    </div>
                                    <div class="col-md-4">
                                      <md-input-container class="md-block">
                                          <label>Disposition</label>
                                          <md-select name="disposition" ng-model="RType.DispositionID" required>
                                              <md-option value="{{disp.disposition_id}}" ng-repeat="disp in Dispositions">{{disp.disposition}}</md-option>
                                          </md-select>
                                      </md-input-container>
                                    </div>
                                </div>
                                <div class="col-md-5 pl_md0">

                                    <!-- <div class="col-md-5">
                                        <md-input-container class="md-block mt-20" >
                                            <md-checkbox ng-model="Recoverytype.BreRequired" ng-true-value="'Yes'" ng-false-value="'No'" class="mb-0"> BRE Required </md-checkbox>
                                        </md-input-container>
                                    </div> -->
                                    <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Status</label>
                                            <md-select name="Statuspt" ng-model="RType.Status" required aria-label="select">
                                                <md-option value="1"> Active </md-option>
                                                <md-option value="2"> In Active </md-option>
                                            </md-select>
                                        </md-input-container>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="md-button md-raised md-primary" style=" margin-top: 10px; min-width: 60px;" ng-disabled="!RType.RecoveryPartType" ng-click="AddRecoveryParttypes($event)">Add</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-md-12">
                            <div class="col-md-12">
                                    <table md-table md-row-select  ng-show="RecoveryParttypemapping.length > 0">
                                        <thead md-head>
                                            <tr md-row class="bg-grey">
                                                <th md-column>Edit</th>
                                                <th md-column>Part Type</th>
                                                <th md-column>Evaluation Result</th>
                                                <th md-column>Disposition</th>
                                                <th md-column>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody md-body>
                                            <tr md-row ng-repeat="Rparttype in RecoveryParttypemapping">
                                              <td md-cell class="actionicons" style="min-width: 30px; width: 30px;">
                                                  <i class="material-icons text-danger edit" style="cursor: pointer;" ng-click="EditRecovertypeMapping(Rparttype)">edit</i>
                                              </td>
                                                <td md-cell>{{Rparttype.parttype}}</td>
                                                <td md-cell>{{Rparttype.input}}</td>
                                                <td md-cell>{{Rparttype.disposition}}</td>
                                                <td md-cell>{{Rparttype.StatusName}}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                            </div>
                        </div> -->

                        <div class="col-md-12">
                            <div class="row"  ng-show="ParttypetypeList">
                                <div class="col-md-12">
                                    <div class="col-md-12">
                                        <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                                <small>
                                                Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                                to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                    <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                                of <span style="font-weight:bold;">{{total}}</span>
                                                </small>
                                        </div>
                                        <div style="clear:both;"></div>
                                        <div class="table-responsive" style="overflow: auto;">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr class="th_sorting">
                                                        <th style="min-width: 40px;">Edit</th>

                                                        <th style="cursor:pointer;" ng-click="MakeOrderBy('parttype')" ng-class="{'orderby' : OrderBy == 'parttype'}">
                                                            <div>
                                                                Part Type<i class="fa fa-sort pull-right" ng-show="OrderBy != 'parttype'"></i>
                                                                <span ng-show="OrderBy == 'parttype'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th style="cursor:pointer;" ng-click="MakeOrderBy('serialized')" ng-class="{'orderby' : OrderBy == 'serialized'}">
                                                            <div>
                                                                Serialized<i class="fa fa-sort pull-right" ng-show="OrderBy != 'serialized'"></i>
                                                                <span ng-show="OrderBy == 'serialized'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>


                                                         <th style="cursor:pointer;" ng-click="MakeOrderBy('input')" ng-class="{'orderby' : OrderBy == 'input'}">
                                                            <div>
                                                                Evaluation Result<i class="fa fa-sort pull-right" ng-show="OrderBy != 'input'"></i>
                                                                <span ng-show="OrderBy == 'input'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                        <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">
                                                            <div>
                                                                Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>
                                                                <span ng-show="OrderBy == 'disposition'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th style="cursor:pointer;" ng-click="MakeOrderBy('StatusName')" ng-class="{'orderby' : OrderBy == 'StatusName'}">
                                                            <div>
                                                                Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'StatusName'"></i>
                                                                <span ng-show="OrderBy == 'StatusName'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                         <th>Delete</th>
                                                    </tr>

                                                    <tr class="errornone">
                                                        <td>&nbsp;</td>
                                                        <td>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="parttype" ng-model="filter_text[0].parttype" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="serialized" ng-model="filter_text[0].serialized" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="input" ng-model="filter_text[0].input" ng-change="MakeFilter()" aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()" aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                         <td>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="StatusName" ng-model="filter_text[0].StatusName" ng-change="MakeFilter()" aria-label="text" />
                                                            </md-input-container>
                                                        </td>

                                                        <td>&nbsp;</td>

                                                    </tr>
                                                </thead>

                                                <tbody ng-show="pagedItems.length > 0">
                                                    <tr ng-repeat="product in pagedItems">
                                                        <td style="text-align:center;">
                                                            <span ng-click="EditRecovertypeMapping(product,$event)"><i class="material-icons text-success edit">edit</i></span>
                                                        </td>
                                                        <td>
                                                            {{product.parttype}}
                                                        </td>
                                                         <td>
                                                            {{product.serialized}}
                                                        </td>
                                                        <td>
                                                            {{product.input}}
                                                        </td>
                                                        <td>
                                                            {{product.disposition}}
                                                        </td>
                                                        <td>
                                                            {{product.StatusName}}
                                                        </td>
                                                        <td>
                                                            <i class="material-icons text-danger" ng-click="DeleteMappedRecoveryParttype(product,$event)">delete</i>
                                                        </td>
                                                    </tr>
                                                </tbody>

                                                <tfoot>
                                                    <tr>
                                                        <td colspan="9">
                                                            <div>
                                                                <ul class="pagination">
                                                                    <li ng-class="prevPageDisabled()">
                                                                        <a href ng-click="firstPage()"><< First</a>
                                                                    </li>
                                                                    <li ng-class="prevPageDisabled()">
                                                                        <a href ng-click="prevPage()"><< Prev</a>
                                                                    </li>
                                                                    <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                        <a style="cursor:pointer;">{{n+1}}</a>
                                                                    </li>
                                                                    <li ng-class="nextPageDisabled()">
                                                                        <a href ng-click="nextPage()">Next >></a>
                                                                    </li>
                                                                    <li ng-class="nextPageDisabled()">
                                                                        <a href ng-click="lastPage()">Last >></a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </md-card>

                <!--Unserialized Section Form Start-->
                <md-card class="no-margin-h" style="display:none;">
                    <md-toolbar class="md-table-toolbar md-default"ng-init="UnserializedPanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="UnserializedPanel = !UnserializedPanel">
                            <i class="material-icons md-primary" ng-show="UnserializedPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! UnserializedPanel">keyboard_arrow_down</i>
                            <span>BOM Verification Required</span>
                            <div flex></div>
                            <!-- <span>Count: <strong class="text-success">{{UnserialRecoveryPartsCount}}</strong></span> -->
                        </div>
                    </md-toolbar>
                    <div class="row" ng-show="UnserializedPanel">


                        <form name="GeneralComponentForm" class="form-validation">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div class="col-md-4" ng-repeat="PartType in serialRecoverypart">
                                        <span style="display: flex;" class="mb-10">
                                            <md-checkbox ng-model="PartType.Added" ng-true-value="'1'" ng-false-value="'0'"  style="margin-bottom: 0px; width:25px !important" ng-change="CreateUnserialRecoverypart(PartType);"></md-checkbox>
                                            {{PartType.parttype}}
                                        </span>
                                    </div>

                                </div>
                            </div>
                        </form>
                    </div>
                </md-card>
                <!--Unserialized Section Form Close-->

                <!--Source Type Section Form Start-->
                <md-card class="no-margin-h" ng-show="Recoverytype.Recoverytypeid">
                    <md-toolbar class="md-table-toolbar md-default"ng-init="SourceTypePanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="SourceTypePanel = !SourceTypePanel">
                            <i class="material-icons md-primary" ng-show="SourceTypePanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! SourceTypePanel">keyboard_arrow_down</i>
                            <span>Recovery Complete Check Box Configuration</span>
                            <div flex></div>
                        </div>
                    </md-toolbar>
                    <div class="row" ng-show="SourceTypePanel">
                        <form name="SourceTypeForm" class="form-validation">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-7 pr_md0">                                        

                                        <div class="col-md-3">
                                            <md-input-container class="md-block">
                                                <label>Recovery Type</label>
                                                <input type="text" name="RecoveryType"  ng-model="Recoverytype['Recoverytype']"  required ng-disabled="true" />
                                                <div class="error-sapce">
                                                        <div ng-messages="SourceTypeForm.Recoverytypeid.$error" multiple ng-if='SourceTypeForm.Recoverytypeid.$dirty'>
                                                            <div ng-message="required">This is required.</div>
                                                            <div ng-message="minlength">Min length 3.</div>
                                                            <div ng-message="maxlength">Max length 45.</div>
                                                        </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-3">
                                            <md-input-container class="md-block">
                                                <label>Source Type</label>
                                                <md-select name="idCustomertype" ng-model="RType1.idCustomertype" required aria-label="select">
                                                    <md-option ng-repeat="sourceType in SourceTypes" value="{{sourceType.idCustomertype}}"> {{sourceType.Cumstomertype}} </md-option>
                                                </md-select>
                                                <div class="error-sapce"></div>
                                            </md-input-container>
                                        </div>
                                        <div class="col-md-3">
                                            <md-input-container class="md-block">
                                                <label>Recovery Complete Checkbox</label>
                                                <md-select name="RecoveryCompleted" ng-model="RType1.RecoveryCompleted" required>
                                                    <md-option value="Yes">Yes</md-option>                                                    
                                                </md-select>
                                            </md-input-container>
                                        </div>
                                        <div class="col-md-3">
                                            <md-input-container class="md-block">
                                                <label>Status</label>
                                                <md-select name="Status" ng-model="RType1.Status" required aria-label="select">
                                                    <md-option value="Active"> Active </md-option>
                                                    <md-option value="Inactive"> Inactive </md-option>
                                                </md-select>
                                            </md-input-container>
                                        </div>
                                    </div>
                                    <div class="col-md-5 pl_md0">
                                        <div class="col-md-3">
                                            <button type="button" class="md-button md-raised md-primary" style=" margin-top: 10px; min-width: 60px;" ng-disabled="SourceTypeForm.$invalid" ng-click="AddRecoveryCompleteCheckboxConfiguration($event)">Add</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="row" ng-show="RecoveryCompleteConfigurationList.length > 0">
                                    <div class="col-md-12">
                                        <div class="col-md-12">
                                            <div style="clear:both;"></div>
                                            <div class="table-responsive" style="overflow: auto;">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr class="th_sorting">
                                                            <th>Recovery Type</th>
                                                            <th>Source Type</th>
                                                            <th>Recovery Complete Checkbox Enabled</th>
                                                            <th>Status</th>                                                            
                                                            <th>Delete</th>
                                                        </tr>                                                        
                                                    </thead>
                                                    <tbody >
                                                        <tr ng-repeat="item in RecoveryCompleteConfigurationList">                                                            
                                                            <td>
                                                                {{item.Recoverytype}}
                                                            </td>
                                                            <td>
                                                                {{item.Cumstomertype}}
                                                            </td>
                                                            <td>
                                                                {{item.RecoveryCompleted}}
                                                            </td>
                                                            <td>
                                                                {{item.Status}}
                                                            </td>
                                                            <td>
                                                                <i class="material-icons text-danger" ng-click="DeleteRecoveryCompleteCheckboxConfiguration(item,$index,$event)">delete</i>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </md-card>
                <!--Source Type Section Form Close-->




                <!--Start Toplevel on recovery complete, move to destroyed-->
                <md-card class="no-margin-h" ng-show="Recoverytype.Recoverytypeid && Recoverytype.Recoverytype == 'Assembly'">
                    <md-toolbar class="md-table-toolbar md-default"ng-init="DestroyTopLevelPanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="DestroyTopLevelPanel = !DestroyTopLevelPanel">
                            <i class="material-icons md-primary" ng-show="DestroyTopLevelPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! DestroyTopLevelPanel">keyboard_arrow_down</i>
                            <span>Destroy Top Level on Recovery Complete Configuration</span>
                            <div flex></div>
                        </div>
                    </md-toolbar>
                    <div class="row" ng-show="DestroyTopLevelPanel" style="background-color: #f5f5f5; margin: 0; padding: 10px;">
                        <div class="col-md-12">
                            <div class="alert alert-info" style="margin-bottom: 15px;">
                                <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">info</i>
                                <strong>Note:</strong> Top level Serial will be updated to Destroyed Disposition and removed from the BIN.
                            </div>
                        </div>
                    </div>
                    <div class="row" ng-show="DestroyTopLevelPanel">
                        <form name="DestroyTopLevelForm" class="form-validation">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-7 pr_md0">

                                        <div class="col-md-3">
                                            <md-input-container class="md-block">
                                                <label>Recovery Type</label>
                                                <input type="text" name="RecoveryType"  ng-model="Recoverytype['Recoverytype']"  required ng-disabled="true" />
                                                <div class="error-sapce">
                                                        <div ng-messages="DestroyTopLevelForm.RecoveryType.$error" multiple ng-if='DestroyTopLevelForm.RecoveryType.$dirty'>
                                                            <div ng-message="required">This is required.</div>
                                                            <div ng-message="minlength">Min length 3.</div>
                                                            <div ng-message="maxlength">Max length 45.</div>
                                                        </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-3">
                                            <md-input-container class="md-block">
                                                <label>Part Type</label>
                                                <md-select name="parttypeid" ng-model="RType2.parttypeid" required aria-label="select">
                                                    <md-option ng-repeat="partType in PartTypes" value="{{partType.parttypeid}}"> {{partType.parttype}} </md-option>
                                                </md-select>
                                                <div class="error-sapce"></div>
                                            </md-input-container>
                                        </div>
                                        <div class="col-md-3">
                                            <md-input-container class="md-block">
                                                <label>Material Type</label>
                                                <md-select name="MaterialType" ng-model="RType2.MaterialType" required>
                                                    <md-option ng-repeat="materialType in MaterialTypes" value="{{materialType.MaterialType}}">{{materialType.MaterialType}}</md-option>                                                    
                                                </md-select>
                                            </md-input-container>
                                        </div>
                                        <div class="col-md-3">
                                            <md-input-container class="md-block">
                                                <label>Status</label>
                                                <md-select name="Status" ng-model="RType2.Status" required aria-label="select">
                                                    <md-option value="Active"> Active </md-option>
                                                    <md-option value="Inactive"> Inactive </md-option>
                                                </md-select>
                                            </md-input-container>
                                        </div>
                                    </div>
                                    <div class="col-md-5 pl_md0">
                                        <div class="col-md-3">
                                            <button type="button" class="md-button md-raised md-primary" style=" margin-top: 10px; min-width: 60px;" ng-disabled="DestroyTopLevelForm.$invalid" ng-click="AddRecoveryCompleteDestroyedConfiguration($event)">Add</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="row" ng-show="RecoveryCompleteDestroyedConfigurationList.length > 0">
                                    <div class="col-md-12">
                                        <div class="col-md-12">
                                            <div style="clear:both;"></div>
                                            <div class="table-responsive" style="overflow: auto;">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr class="th_sorting">
                                                            <th>Recovery Type</th>
                                                            <th>Part Type</th>
                                                            <th>Material Type</th>
                                                            <th>Status</th>
                                                            <th>Delete</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody >
                                                        <tr ng-repeat="item in RecoveryCompleteDestroyedConfigurationList">
                                                            <td>
                                                                {{item.Recoverytype}}
                                                            </td>
                                                            <td>
                                                                {{item.parttype}}
                                                            </td>
                                                            <td>
                                                                {{item.MaterialType}}
                                                            </td>
                                                            <td>
                                                                {{item.Status}}
                                                            </td>
                                                            <td>
                                                                <i class="material-icons text-danger" ng-click="DeleteRecoveryCompleteDestroyedConfiguration(item,$index,$event)">delete</i>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </md-card>
                <!--End Toplevel on recovery complete, move to destroyed-->

        </article>
    </div>
</div>
