<?php
session_start();
include_once("../database/Pick.class.php");
$obj = new PickClass();

if($_POST['ajax'] == "PickConfigurationSave"){
	$result = $obj->PickConfigurationSave($_POST);
	echo $result;
}



if($_POST['ajax'] == "SaveConfigurationWithDetail"){
	$result = $obj->SaveConfigurationWithDetail($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPickConfigurationDetails"){
	$result = $obj->GetPickConfigurationDetails($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPartTypes"){
	$result = $obj->GetPartTypes($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetEligiblePartTypes"){
	$result = $obj->GetEligiblePartTypes($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetEligibleFromDispositions"){
	$result = $obj->GetEligibleFromDispositions($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPickEligibleDispositions"){
	$result = $obj->GetPickEligibleDispositions($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetAllDispositions"){
	$result = $obj->GetAllDispositions($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetEligibleAssignDispositions"){
	$result = $obj->GetEligibleAssignDispositions($_POST);
	echo $result;
}

if($_POST['ajax'] == "SaveConfigurationDetail"){
	$result = $obj->SaveConfigurationDetail($_POST);
	echo $result;
}

if($_POST['ajax'] == "DeleteConfigurationDetail"){
	$result = $obj->DeleteConfigurationDetail($_POST);
	echo $result;
}

if($_POST['ajax'] == "CloseConfiguration"){
	$result = $obj->CloseConfiguration($_POST);
	echo $result;
}

if($_POST['ajax'] == "CleanupMPN"){
	$result = $obj->CleanupMPN($_POST);
	echo $result;
}

if($_POST['ajax'] == "ValidateMPN"){
	$result = $obj->ValidateMPN($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPickConfigurationDetailsList"){
	$result = $obj->GetPickConfigurationDetailsList($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPickConfigurationDetailsListCompleted"){
	$result = $obj->GetPickConfigurationDetailsListCompleted($_POST);
	echo $result;
}

if($_POST['ajax'] == "ExportPickConfigurationDetailsCompleted"){
	$result = $obj->ExportPickConfigurationDetailsCompleted($_POST);
	echo $result;
}

if($_POST['ajax'] == "ExportPickConfigurationDetails"){
	$result = $obj->ExportPickConfigurationDetails($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPickPathList"){
	$result = $obj->GetPickPathList($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetPickPathListGrouped"){
	$result = $obj->GetPickPathListGrouped($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetAvailableUsers"){
	$result = $obj->GetAvailableUsers($_POST);
	echo $result;
}



if($_POST['ajax'] == "AssignUserToConfiguration"){
	$result = $obj->AssignUserToConfiguration($_POST);
	echo $result;
}

if($_POST['ajax'] == "RemoveUserFromConfiguration"){
	$result = $obj->RemoveUserFromConfiguration($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetConfigurationUserAssignments"){
	$result = $obj->GetConfigurationUserAssignments($_POST);
	echo $result;
}

if($_GET['ajax'] == "GetPickDetailTrackingHistory"){
	$result = $obj->GetPickDetailTrackingHistory($_GET);
	echo $result;
}

// ProcessPickPath endpoints
if($_POST['ajax'] == "GetConfigurationsForFacility"){
	$result = $obj->GetConfigurationsForFacility($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetUserAssignedConfigurationDetails"){
	$result = $obj->GetUserAssignedConfigurationDetails($_POST);
	echo $result;
}

if($_POST['ajax'] == "MapBinToConfigurationDetail"){
	$result = $obj->MapBinToConfigurationDetail($_POST);
	echo $result;
}

if($_POST['ajax'] == "ProcessSerialNumber"){
	$result = $obj->ProcessSerialNumber($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetRecentTransactions"){
	$result = $obj->GetRecentTransactions($_POST);
	echo $result;
}

if($_POST['ajax'] == "ProcessSourceBin"){
	$result = $obj->ProcessSourceBin($_POST);
	echo $result;
}

// Bin Management Actions for ProcessPickPath
if($_POST['ajax'] == "CreateBin") {
	$result = $obj->CreateBin($_POST);
	echo $result;
}

if($_POST['ajax'] == "MoveBinToNewLocationGroup") {
	$result = $obj->MoveBinToNewLocationGroup($_POST);
	echo $result;
}

if($_POST['ajax'] == "CloseBin") {
	$result = $obj->CloseBin($_POST);
	echo $result;
}

if($_POST['ajax'] == "ConsolidateBin") {
	$result = $obj->ConsolidateBin($_POST);
	echo $result;
}

if($_POST['ajax'] == "NestToBin") {
	$result = $obj->NestToBin($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetBinPackageTypes") {
	$result = $obj->GetBinPackageTypes($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetSessionFacility") {
	$result = $obj->GetSessionFacility($_POST);
	echo $result;
}

if($_POST['ajax'] == "GenerateBinName") {
	$result = $obj->GenerateBinName($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetBinDetails") {
	$result = $obj->GetBinDetails($_POST);
	echo $result;
}

if($_POST['ajax'] == "GeneratePickPathName"){
	$result = $obj->GeneratePickPathName($_POST);
	echo $result;
}

// Assign Disposition Ineligibility endpoints
if($_POST['ajax'] == "ManageAssignDispositionIneligibility"){
	$result = $obj->ManageAssignDispositionIneligibility($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetAssignDispositionIneligibilityDetails"){
	$result = $obj->GetAssignDispositionIneligibilityDetails($_POST);
	echo $result;
}

if($_POST['ajax'] == "GetAssignDispositionIneligibilityList"){
	$result = $obj->GetAssignDispositionIneligibilityList($_POST);
	echo $result;
}

if($_POST['ajax'] == "DeleteAssignDispositionIneligibility"){
	$result = $obj->DeleteAssignDispositionIneligibility($_POST);
	echo $result;
}

if($_POST['ajax'] == "SearchMPNLocations"){
	$result = $obj->SearchMPNLocations($_POST);
	echo $result;
}



?>