<div class="row page" data-ng-controller="TruckBookingCalender">
    <style>
        .complete{ background-color: #b4dba4;}
        .noshow{ background-color: #f0908c;}
        .failed{ background-color: #dc3545; color: white;}
        .reserved{ background-color: #cecece;}
        .requested{ background-color: #a9c5ec;}
        .inprogress{ background-color: #ffe188;}
        .arrived{ background-color: #32CD32;}
        td,th{ text-align: center; line-height: 34px !important;}

        /* Hover effects for clickable cells (only Arrived and In Progress) */
        td.inprogress:hover, td.arrived:hover {
            opacity: 0.8;
            transform: scale(1.02);
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Non-clickable cells styling */
        td.complete, td.noshow, td.failed, td.reserved, td.requested {
            opacity: 0.7;
        }
    </style>
    <div class="col-md-12">
        <article class="article">
            <md-card class="no-margin-h">
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Truck Booking Calender</span>
                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Date</label>
                                    <md-datepicker ng-model="Calendar.SelectedDate" required ng-change="onDateChange()"
                                        input-aria-describedby="datepicker-description"
                                        input-aria-labelledby="datepicker-header "></md-datepicker>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Type</label>
                                    <md-select name="ParkTypeID" ng-model="Calendar.ParkTypeID" required ng-change="onParkTypeChange()">
                                        <md-option value="{{ParkType.ParkTypeID}}"
                                            ng-repeat="ParkType in ParkTypes">{{ParkType.ParkTypeName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ParkTypeID.$error" multiple
                                            ng-if='material_signup_form.ParkTypeID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3" style="padding-top: 20px;">
                                <span ng-if="!Calendar.SelectedDate || !Calendar.ParkTypeID" style="color: #666; font-style: italic;">
                                    Please select both Date and Parking Type to view the calendar
                                </span>
                                <span ng-if="Calendar.SelectedDate && Calendar.ParkTypeID && ParkingLocations.length === 0 && !isLoading" style="color: #f44336;">
                                    No parking locations found for selected parking type
                                </span>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row mt-10" ng-if="Calendar.SelectedDate && Calendar.ParkTypeID">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div class="table-container table-responsive" style="overflow: auto; max-height: 70vh;">
                                <table class="table table-bordered table-condensed" ng-if="ParkingLocations.length > 0" style="font-size: 12px;">

                                    <thead style="position: sticky; top: 0; background: white; z-index: 10;">
                                        <tr class="bg-grey">
                                            <th style="min-width: 60px; padding: 8px 4px; position: sticky; left: 0; background: #f5f5f5; z-index: 11;">Time</th>
                                            <th ng-repeat="location in ParkingLocations" style="min-width: 100px; padding: 8px 4px; text-align: center;">
                                                {{location.ParkingLocationName}}
                                            </th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                        <tr ng-repeat="timeSlot in TimeSlots" style="height: 30px;">
                                            <td style="padding: 4px; position: sticky; left: 0; background: white; z-index: 5; border-right: 2px solid #ddd;"><strong>{{timeSlot.display}}</strong></td>
                                            <td ng-repeat="location in ParkingLocations"
                                                class="{{getStatusClass(timeSlot.value, location.ParkingLocationID)}}"
                                                title="{{getStatusTooltip(timeSlot.value, location.ParkingLocationID)}}"
                                                style="padding: 4px 2px; text-align: center; font-size: 10px; {{getCellCursor(timeSlot.value, location.ParkingLocationID)}}"
                                                ng-click="onCellClick(timeSlot.value, location.ParkingLocationID)">
                                                {{getStatusText(timeSlot.value, location.ParkingLocationID)}}
                                            </td>
                                        </tr>
                                    </tbody>

                                </table>

                                <div ng-if="ParkingLocations.length === 0 && !isLoading && Calendar.ParkTypeID" style="text-align: center; padding: 40px; color: #666;">
                                    <i class="material-icons" style="font-size: 48px; margin-bottom: 10px;">info</i>
                                    <p>No parking locations found for the selected parking type.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Legend -->
                <div class="row mt-10" ng-if="Calendar.SelectedDate && Calendar.ParkTypeID && ParkingLocations.length > 0">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <h4>Status Legend:</h4>
                            <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 10px;">
                                <div style="display: flex; align-items: center;">
                                    <div class="complete" style="width: 20px; height: 20px; margin-right: 5px; border: 1px solid #ccc;"></div>
                                    <span>Complete <small style="color: #666;">(Not clickable)</small></span>
                                </div>
                                <div style="display: flex; align-items: center;">
                                    <div class="noshow" style="width: 20px; height: 20px; margin-right: 5px; border: 1px solid #ccc;"></div>
                                    <span>No Show <small style="color: #666;">(Not clickable)</small></span>
                                </div>
                                <div style="display: flex; align-items: center;">
                                    <div class="failed" style="width: 20px; height: 20px; margin-right: 5px; border: 1px solid #ccc;"></div>
                                    <span>Failed <small style="color: #666;">(Not clickable)</small></span>
                                </div>
                                <div style="display: flex; align-items: center;">
                                    <div class="reserved" style="width: 20px; height: 20px; margin-right: 5px; border: 1px solid #ccc;"></div>
                                    <span>Reserved <small style="color: #666;">(Not clickable)</small></span>
                                </div>
                                <div style="display: flex; align-items: center;">
                                    <div class="requested" style="width: 20px; height: 20px; margin-right: 5px; border: 1px solid #ccc;"></div>
                                    <span>Requested <small style="color: #666;">(Not clickable)</small></span>
                                </div>
                                <div style="display: flex; align-items: center;">
                                    <div class="inprogress" style="width: 20px; height: 20px; margin-right: 5px; border: 1px solid #ccc;"></div>
                                    <span>In Progress <small style="color: #007bff;">✓ Clickable</small></span>
                                </div>
                                <div style="display: flex; align-items: center;">
                                    <div class="arrived" style="width: 20px; height: 20px; margin-right: 5px; border: 1px solid #ccc;"></div>
                                    <span>Arrived <small style="color: #007bff;">✓ Clickable</small></span>
                                </div>
                            </div>
                            <div style="margin-top: 10px; padding: 10px; background-color: #e3f2fd; border-radius: 4px; border-left: 4px solid #2196f3;">
                                <i class="material-icons" style="vertical-align: middle; margin-right: 5px; color: #2196f3;">info</i>
                                <strong>Click on "Arrived" or "In Progress" bookings</strong> to open Trailer Dock/Release page with auto-populated details.
                            </div>
                        </div>
                    </div>
                </div>

            </md-card>
        </article>
    </div>
</div>