const Joi = require('joi');

// Validation schemas
const schemas = {
    // Truck validation
    truck: Joi.object({
        truckNumber: Joi.string().max(50).required(),
        driverName: Joi.string().max(100).required(),
        licensePlate: Joi.string().max(20).required(),
        truckType: Joi.string().valid('pickup', 'delivery', 'semi', 'flatbed').required(),
        status: Joi.string().valid('active', 'maintenance', 'inactive').default('active'),
        notes: Joi.string().max(1000).allow('', null)
    }),

    // Truck booking validation
    truckBooking: Joi.object({
        FacilityID: Joi.number().integer().positive().required(),
        ArrivalType: Joi.string().valid('Inbound', 'Outbound').required(),
        ParkingLocationID: Joi.number().integer().positive().required(),
        CarrierID: Joi.number().integer().positive().required(),
        ArrivalDate: Joi.date().iso().required(),
        ArrivalTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
        LoadType: Joi.string().valid('Pallet', 'Rack').required(),
        LoadNumber: Joi.string().max(50).required(),
        TruckTypeID: Joi.number().integer().positive().required(),
        TruckReg: Joi.string().max(50).required(),
        TrailerNumber: Joi.string().max(50).required(),
        DriverName: Joi.string().max(50).required(),
        DriverID: Joi.string().max(50).allow('', null),
        ShipmentTicketID: Joi.string().max(50).allow('', null),
        ClassificationType: Joi.string().valid('All', 'UEEE', 'WEEE').required(),
        WasteCollectionPermit: Joi.string().max(250).allow('', null),
        Notes: Joi.string().max(250).allow('', null),
        Status: Joi.string().valid('Reserved', 'Requested').required()
    }),

    // Update truck booking
    updateTruckBooking: Joi.object({
        TruckID: Joi.number().integer().positive().required(),
        FacilityID: Joi.number().integer().positive().required(),
        ArrivalType: Joi.string().valid('Inbound', 'Outbound').required(),
        ParkingLocationID: Joi.number().integer().positive().required(),
        CarrierID: Joi.number().integer().positive().required(),
        ArrivalDate: Joi.date().iso().required(),
        ArrivalTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
        LoadType: Joi.string().valid('Pallet', 'Rack').required(),
        LoadNumber: Joi.string().max(50).required(),
        TruckTypeID: Joi.number().integer().positive().required(),
        TruckReg: Joi.string().max(50).required(),
        TrailerNumber: Joi.string().max(50).required(),
        DriverName: Joi.string().max(50).required(),
        DriverID: Joi.string().max(50).allow('', null),
        ShipmentTicketID: Joi.string().max(50).allow('', null),
        ClassificationType: Joi.string().valid('All', 'UEEE', 'WEEE').required(),
        WasteCollectionPermit: Joi.string().max(250).allow('', null),
        Notes: Joi.string().max(250).allow('', null),
        Status: Joi.string().valid('Reserved', 'Requested').required()
    }),

    // ID validation
    id: Joi.object({
        id: Joi.number().integer().positive().required()
    }),

    // Status change validation
    statusChange: Joi.object({
        TruckID: Joi.number().integer().positive().required(),
        Status: Joi.string().valid('Reserved', 'Requested', 'Confirmed', 'Cancelled').required()
    })
};

// Validation middleware factory
const validate = (schema) => {
    return (req, res, next) => {
        const { error, value } = schemas[schema].validate(req.body);
        
        if (error) {
            const errorMessage = error.details.map(detail => detail.message).join(', ');
            return res.status(400).json({
                Success: false,
                Result: `Validation error: ${errorMessage}`
            });
        }
        
        // Replace req.body with validated and sanitized data
        req.body = value;
        next();
    };
};

// Validate query parameters
const validateQuery = (schema) => {
    return (req, res, next) => {
        const { error, value } = schemas[schema].validate(req.query);
        
        if (error) {
            const errorMessage = error.details.map(detail => detail.message).join(', ');
            return res.status(400).json({
                Success: false,
                Result: `Query validation error: ${errorMessage}`
            });
        }
        
        req.query = value;
        next();
    };
};

// Validate URL parameters
const validateParams = (schema) => {
    return (req, res, next) => {
        const { error, value } = schemas[schema].validate(req.params);
        
        if (error) {
            const errorMessage = error.details.map(detail => detail.message).join(', ');
            return res.status(400).json({
                Success: false,
                Result: `Parameter validation error: ${errorMessage}`
            });
        }
        
        req.params = value;
        next();
    };
};

module.exports = {
    validate,
    validateQuery,
    validateParams,
    schemas
};
