<div ng-controller="ProcessPickPath" class="page">
    <style>
        /* Status color coding */
        .pending { background-color: #ffe188; }
        .inprogress { background-color: #b4dba4; }
        .inactive { background-color: #f0908c; }
        .completed { background-color: #28a745; color: white; }

        /* Status cell styling */
        .status-cell {
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: 500;
            min-width: 100px;
            display: inline-block;
        }

        /* Configuration selection section */
        .config-selection {
            background: #f5f5f5;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 6px;
            border-left: 4px solid #4caf50;
        }

        /* Actions Dropdown Styling */
        .actionicons .md-button {
            min-width: auto;
            margin: 0;
            padding: 8px 12px;
            line-height: 1;
            border-radius: 4px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
        }

        .actionicons .dropdown-text {
            font-size: 14px;
            color: #333;
            margin-right: 4px;
        }

        .actionicons .material-icons {
            font-size: 18px;
            color: #666;
        }

        .actionicons .md-button:hover {
            background-color: #e9e9e9;
        }
    </style>

    <!-- Create Bin Modal -->
    <script type="text/ng-template" id="createBinModal.html">
        <div style="max-width:800px">
            <md-toolbar>
                <div class="md-toolbar-tools">
                    <h2>Create Bin</h2>
                    <span flex></span>
                    <md-button class="md-icon-button" ng-click="cancel()">
                        <md-icon class="material-icons">close</md-icon>
                    </md-button>
                </div>
            </md-toolbar>

            <md-dialog-content>
                <div class="md-dialog-content">
                    <div class="form-horizontal verification-form">
                        <form name="createBinForm">

                            <!-- First Row: Bin Type, Bin Name -->
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Bin Type</label>
                                    <md-select name="idPackage" ng-model="createBinData.idPackage" required aria-label="select" ng-change="onBinTypeChange()">
                                        <md-option ng-repeat="packageType in PackageTypes" value="{{packageType.idPackage}}"> {{packageType.packageName}} </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="createBinForm.idPackage.$error" multiple ng-if='createBinForm.idPackage.$dirty'>
                                            <div ng-message="required">Bin Type is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Bin Name</label>
                                    <div style="position: relative;">
                                        <input name="BinName" ng-model="createBinData.BinName" value="" required ng-minlength="6" style="padding-right: 40px;" />
                                        <md-button class="md-icon-button" ng-click="AutoGenerateBinNameProcessPickPath()"
                                                   style="position: absolute; right: 0; top: 50%; transform: translateY(-50%); margin: 0; width: 32px; height: 32px;"
                                                   aria-label="Auto Generate Bin Name">
                                            <md-icon>refresh</md-icon>
                                        </md-button>
                                    </div>
                                    <div class="error-space">
                                        <div ng-messages="createBinForm.BinName.$error" multiple ng-if='createBinForm.BinName.$dirty'>
                                            <div ng-message="required">Bin Name is required.</div>
                                            <div ng-message="minlength">Min length 6.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Second Row: Facility, Location Type -->
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <md-select name="FacilityID" ng-model="createBinData.FacilityID" required aria-label="select" ng-disabled="true">
                                        <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="createBinForm.FacilityID.$error" multiple ng-if='createBinForm.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Location Type</label>
                                    <md-select name="LocationType" ng-model="createBinData.LocationType" required aria-label="select" ng-disabled="true">
                                        <md-option value="WIP">WIP</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="createBinForm.LocationType.$error" multiple ng-if='createBinForm.LocationType.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Third Row: Location Group, Disposition -->
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <div class="autocomplete" style="padding:0px;">
                                        <md-autocomplete flex style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                            md-input-name="LocationGroup"
                                            md-input-maxlength="100"
                                            md-no-cache="noCache"
                                            md-search-text-change="LocationGroupChange(createBinData.LocationGroup)"
                                            md-search-text="createBinData.LocationGroup"
                                            md-items="item in queryLocationGroupSearch(createBinData.LocationGroup)"
                                            md-item-text="item.GroupName"
                                            md-selected-item-change="selectedLocationGroupChange(item)"
                                            md-min-length="0"
                                            ng-model-options='{ debounce: 1000 }'
                                            md-escape-options="clear"
                                            md-floating-label="Location Group *"
                                            md-require-match="false"
                                            required
                                            >
                                            <md-item-template>
                                                <span md-highlight-text="createBinData.LocationGroup" md-highlight-flags="^i">{{item.GroupName}}</span>
                                            </md-item-template>
                                            <md-not-found>
                                                No Location Groups matching "{{createBinData.LocationGroup}}" were found.
                                            </md-not-found>
                                            <div ng-messages="createBinForm.LocationGroup.$error" ng-if="createBinForm.LocationGroup.$touched">
                                                <div ng-message="required">Location Group is required.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </md-autocomplete>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Disposition</label>
                                    <input type="text" name="Disposition" ng-model="createBinData.Disposition" ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <!-- Fourth Row: Notes -->
                            <div class="col-md-12">
                                <md-input-container class="md-block">
                                    <label>Notes</label>
                                    <input type="text" name="Notes" ng-model="createBinData.Notes" ng-maxlength="250" />
                                    <div class="error-space">
                                        <div ng-messages="createBinForm.Notes.$error" multiple ng-if='createBinForm.Notes.$dirty'>
                                            <div ng-message="maxlength">Max length 250.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                        </form>
                    </div>

                    <div class="col-md-12 text-center mb-10 mt-10">
                        <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Cancel</button>
                        <button type="button" class="md-button md-raised btn-w-md md-primary" ng-click="createBin()" ng-disabled="createBinForm.$invalid || createBinBusy">
                            <span ng-show="!createBinBusy">Create Bin</span>
                            <span ng-show="createBinBusy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular></span>
                        </button>
                    </div>
                </div>
            </md-dialog-content>
        </div>
    </script>

    <!-- Move Bin Modal -->
    <script type="text/ng-template" id="moveBinModal.html">
        <div style="max-width:900px">
            <md-toolbar>
                <div class="md-toolbar-tools">
                <h2>Move & Scan New BIN</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="cancel()">
                    <md-icon class="material-icons">close</md-icon>
                </md-button>
                </div>
            </md-toolbar>

            <md-dialog-content>
                <div class="md-dialog-content">
                    <div class="form-horizontal verification-form">
                        <form name="tpvForm">
                            <div class="col-md-6">
                                <md-input-container class="md-block md-input-has-value">
                                    <label>Current Bin Location</label>
                                    <p class="static_value">
                                        <strong>{{CurrentPallet.CurrentLocationGroup || 'Not Assigned'}}</strong>
                                    </p>
                                </md-input-container>
                            </div>

                            <div class="col-md-6">
                                <md-input-container class="md-block md-input-has-value">
                                    <label>Location Type</label>
                                    <p class="static_value">
                                        <strong>{{CurrentPallet.LocationType || 'WIP'}}</strong>
                                    </p>
                                </md-input-container>
                            </div>

                            <div class="col-md-8">
                                <md-input-container class="md-block md-input-has-value">
                                    <label>Current Bin</label>
                                    <p class="static_value">
                                        <strong>{{CurrentPallet.BinName}}</strong>
                                    </p>
                                </md-input-container>
                            </div>

                            <div class="col-md-12" >
                                <div class="autocomplete" style="padding:0px;">
                                    <md-autocomplete flex style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                        md-input-name="NewLocationGroup"
                                        md-input-maxlength="100"
                                        md-no-cache="noCache"
                                        md-search-text-change="LocationChange1(confirmDetails.NewLocationGroup)"
                                        md-search-text="confirmDetails.NewLocationGroup"
                                        md-items="item in queryLocationSearch1(confirmDetails.NewLocationGroup)"
                                        md-item-text="item.GroupName"
                                        md-selected-item-change="selectedLocationChange1(item)"
                                        md-min-length="0"
                                        ng-model-options='{ debounce: 1000 }'
                                        md-escape-options="clear"
                                        md-floating-label="New Location Group *"
                                        md-require-match="false"
                                        required
                                        >
                                        <md-item-template>
                                            <span md-highlight-text="confirmDetails.NewLocationGroup" md-highlight-flags="^i">{{item.GroupName}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            No Location Groups matching "{{confirmDetails.NewLocationGroup}}" were found.
                                        </md-not-found>
                                        <div ng-messages="tpvForm.NewLocationGroup.$error" ng-if="tpvForm.NewLocationGroup.$touched">
                                            <div ng-message="required">New Location Group is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-autocomplete>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="col-md-12 text-center mb-10 mt-10">
                        <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                        <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="MoveBinToNewLocationGroup($event)" ng-disabled="tpvForm.$invalid">Continue</button>
                    </div>
                <md-dialog-content>
            </div>
        </md-dialog-content>
    </script>

    <!-- Consolidate Bin Modal -->
    <script type="text/ng-template" id="consolidateBinModal.html">
        <div style="max-width:900px">
            <md-toolbar>
                <div class="md-toolbar-tools">
                <h2>Consolidate BIN</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="cancel()">
                    <md-icon class="material-icons">close</md-icon>
                </md-button>
                </div>
            </md-toolbar>

            <md-dialog-content>
                <div class="md-dialog-content">
                    <div class="form-horizontal verification-form">
                        <form name="consolidateBinForm">
                            <div class="col-md-12">
                                <md-input-container class="md-block md-input-has-value">
                                    <label>From Bin</label>
                                    <p class="static_value">
                                        <strong>{{CurrentCustomPalletPallet.BinName}}</strong>
                                    </p>
                                </md-input-container>
                            </div>
                            <div class="col-md-12">
                                <md-input-container class="md-block">
                                    <label>To BIN</label>
                                    <input required name="ToBinName" id="newbin" ng-model="confirmDetails4.ToBinName" ng-maxlength="200" type="text" autocomplete="off" ng-change="confirmDetails4.ToShipmentContainer = 'n/a'">
                                    <div ng-messages="consolidateBinForm.ToBinName.$error" multiple ng-if='consolidateBinForm.ToBinName.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="maxlength">Max length 200.</div>
                                    </div>
                                </md-input-container>
                            </div>
                        </form>
                    </div>

                    <div class="col-md-12 text-center mb-10 mt-10">
                        <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                        <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="ConsolidateBin($event)" ng-disabled="consolidateBinForm.$invalid">Consolidate</button>
                    </div>
                <md-dialog-content>
            </div>
        </md-dialog-content>
    </script>

    <!-- Nest to Bin Modal -->
    <script type="text/ng-template" id="nestToBinModal.html">
        <div style="max-width:600px">
            <md-toolbar>
                <div class="md-toolbar-tools">
                    <h2>Nest to Bin</h2>
                    <span flex></span>
                    <md-button class="md-icon-button" ng-click="cancel()">
                        <md-icon class="material-icons">close</md-icon>
                    </md-button>
                </div>
            </md-toolbar>
            <md-dialog-content>
                <div class="md-dialog-content">
                    <form name="nestToBinForm" novalidate>
                        <div class="row">
                            <div class="col-md-12">
                                <h4>Current Bin: {{nestToBinData.BinName}}</h4>
                                <p>Select a parent bin to nest this bin under:</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <md-input-container class="md-block">
                                    <label>Parent Bin *</label>
                                    <input required name="parentBin" ng-model="nestToBinData.parentBin" ng-maxlength="500" type="text" placeholder="Enter Parent Bin Name">
                                    <div ng-messages="nestToBinForm.parentBin.$error" multiple ng-if='nestToBinForm.parentBin.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="maxlength">Max length 500.</div>
                                    </div>
                                </md-input-container>
                            </div>
                        </div>
                    </form>

                    <div class="col-md-12 text-center mb-10 mt-10">
                        <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Cancel</button>
                        <button type="button" class="md-button md-raised btn-w-md md-primary" ng-click="nestToBin()" ng-disabled="nestToBinForm.$invalid || nestToBinBusy || !nestToBinData.parentBin">
                            <span ng-show="!nestToBinBusy">Nest to Bin</span>
                            <span ng-show="nestToBinBusy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular></span>
                        </button>
                    </div>
                </div>
            </md-dialog-content>
        </div>
    </script>

    <!-- Close Bin Modal -->
    <script type="text/ng-template" id="closeBinModal.html">
        <div class="panel panel-default">
            <div class="panel-heading">
                TPVR for Closing Bin ({{CurrentBin.BinName}})
            </div>
            <div class="panel-body">
                <div class="form-horizontal verification-form">
                    <form name="closeBinForm">
                        <md-input-container class="md-block">
                            <label>Controller</label>
                            <input required name="AuditController" id="AuditController" ng-model="confirmDetails.AuditController" ng-maxlength="100" type="text" ng-enter="FocusNextField('password','0')">
                            <div ng-messages="closeBinForm.AuditController.$error" multiple ng-if='closeBinForm.AuditController.$dirty'>
                                <div ng-message="required">This is required.</div>
                                <div ng-message="maxlength">Max length 100.</div>
                            </div>
                        </md-input-container>
                        <md-input-container class="md-block">
                            <label>Password</label>
                            <input required name="Password" id="password" ng-model="confirmDetails.Password" ng-maxlength="50" type="password" ng-enter="FocusNextField('SealID','0')">
                            <div ng-messages="closeBinForm.Password.$error" multiple ng-if='closeBinForm.Password.$dirty'>
                                <div ng-message="required">This is required.</div>
                                <div ng-message="maxlength">Max length 50.</div>
                            </div>
                        </md-input-container>

                        <md-input-container class="md-block">
                            <label>Seal ID</label>
                            <input required name="SealID" id="SealID" ng-model="confirmDetails.NewSealID" ng-maxlength="100" type="text" ng-enter="FocusNextField('BinWeight','0')">
                            <div ng-messages="closeBinForm.SealID.$error" multiple ng-if='closeBinForm.SealID.$dirty'>
                                <div ng-message="required">This is required.</div>
                                <div ng-message="maxlength">Max length 100.</div>
                            </div>
                        </md-input-container>

                        <md-input-container class="md-block">
                            <label>Weight</label>
                            <input required name="BinWeight" id="BinWeight" ng-model="confirmDetails.BinWeight" ng-max="999999" ng-min="0" type="number" ng-enter="FocusNextField('ShippingID','0')">
                            <div ng-messages="closeBinForm.BinWeight.$error" multiple ng-if='closeBinForm.BinWeight.$dirty'>
                                <div ng-message="required">This is required.</div>
                                <div ng-message="max">Maximum value is 999999.</div>
                                <div ng-message="min">Minimum value is 0.</div>
                            </div>
                        </md-input-container>

                        <md-input-container class="md-block">
                            <label>Add to Shipment</label>
                            <input name="ShippingID" id="ShippingID" ng-model="confirmDetails.ShippingID" ng-maxlength="50" type="text" ng-enter="FocusNextField('OutboundLocationGroup','0')" ng-change="onShippingIDChange()">
                            <div ng-messages="closeBinForm.ShippingID.$error" multiple ng-if='closeBinForm.ShippingID.$dirty'>
                                <div ng-message="maxlength">Max length 50.</div>
                            </div>
                        </md-input-container>

                        <md-input-container class="md-block">
                            <div class="autocomplete" style="padding:0px;">
                                <md-autocomplete flex id="OutboundLocationGroup" style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                    md-input-name="OutboundLocationGroup"
                                    md-input-maxlength="100"
                                    md-no-cache="noCache"
                                    md-search-text-change="LocationChangeCloseBin(confirmDetails.OutboundLocationGroup)"
                                    md-search-text="confirmDetails.OutboundLocationGroup"
                                    md-items="item in queryLocationSearchCloseBin(confirmDetails.OutboundLocationGroup)"
                                    md-item-text="item.GroupName"
                                    md-selected-item-change="selectedLocationChangeCloseBin(item)"
                                    md-min-length="0"
                                    ng-model-options='{ debounce: 1000 }'
                                    md-escape-options="clear"
                                    md-floating-label="Outbound Location Group"
                                    ng-required="confirmDetails.ShippingID && confirmDetails.ShippingID.trim() !== ''"
                                    ng-enter="hide()"
                                    >
                                    <md-item-template>
                                        <span md-highlight-text="confirmDetails.OutboundLocationGroup" md-highlight-flags="^i">{{item.GroupName}}</span>
                                    </md-item-template>
                                    <md-not-found>
                                        No Records matching "{{confirmDetails.OutboundLocationGroup}}" were found.
                                    </md-not-found>
                                    <div ng-messages="closeBinForm.OutboundLocationGroup.$error" ng-if="closeBinForm.OutboundLocationGroup.$touched">
                                        <div ng-message="required">Outbound Location Group is required when Shipping ID is provided.</div>
                                        <div ng-message="maxlength">Max length 100.</div>
                                    </div>
                                </md-autocomplete>
                            </div>
                        </md-input-container>
                    </form>
                </div>

            </div>

            <div class="panel-footer text-center">
                <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="closeBinForm.$invalid">Continue</button>
            </div>
        </div>
    </script>

    <div class="row ui-section mb-0">
        <div class="col-md-12">
            <article class="article">
                <div class="body_inner_content">
                    <!-- Configuration Selection Section -->
                    <md-card class="no-margin-h pt-0">
                        <md-toolbar class="md-table-toolbar md-default">
                            <div class="md-toolbar-tools">
                                <i class="material-icons md-primary">settings</i>
                                <span>Process Pick Path - Configuration Selection</span>
                                <div flex></div>
                            </div>
                        </md-toolbar>

                        <md-card-content>
                            <div class="config-selection">
                                <div class="row">
                                    <div class="col-md-6">
                                        <md-input-container class="md-block">
                                            <label>Select Pick Configuration *</label>
                                            <md-select ng-model="selectedConfiguration" ng-change="loadConfigurationDetails()" required>
                                                <md-option ng-value="config" ng-repeat="config in configurations">
                                                    {{config.PickPathName}}
                                                </md-option>
                                            </md-select>
                                        </md-input-container>
                                    </div>
                                    <div class="col-md-6" ng-if="selectedConfiguration">
                                        <div style="padding-top: 20px;">
                                            <p style="margin: 0; color: #2e7d32;">
                                                <strong>Selected:</strong> {{selectedConfiguration.PickPathName}}
                                            </p>
                                            <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">
                                                Created: {{selectedConfiguration.CreatedDate | date:'MM/dd/yyyy HH:mm'}}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </md-card-content>
                    </md-card>





                    <!-- Configuration Details Section -->
                    <md-card class="no-margin-h pt-0" ng-if="selectedConfiguration">
                        <md-toolbar class="md-table-toolbar md-default" ng-init="ProcessPickPathList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">
                                <i ng-click="ProcessPickPathList = !ProcessPickPathList" class="material-icons md-primary" ng-show="ProcessPickPathList">keyboard_arrow_up</i>
                                <i ng-click="ProcessPickPathList = !ProcessPickPathList" class="material-icons md-primary" ng-show="!ProcessPickPathList">keyboard_arrow_down</i>
                                <span ng-click="ProcessPickPathList = !ProcessPickPathList">Pick Configuration Details - Assigned to Me</span>
                                <div flex></div>
                                <span ng-if="pagedItems.length > 0" style="font-size: 14px; color: #666;">
                                    {{pagedItems.length}} item(s) assigned to you
                                </span>
                            </div>
                        </md-toolbar>

                        <div class="callout callout-info" ng-show="!busy && selectedConfiguration && pagedItems.length == 0">
                            <p>No configuration details assigned to you for this configuration</p>
                        </div>

                        <div class="callout callout-warning" ng-show="!selectedConfiguration">
                            <p>Please select a configuration to view details</p>
                        </div>

                        <md-card-content ng-show="ProcessPickPathList && pagedItems.length > 0">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Part Type</th>
                                            <th>Pick From Disposition</th>
                                            <th>Assign Disposition</th>
                                            <th>MPN</th>
                                            <th>Pick Process</th>
                                            <th>Pick to Bin</th>
                                            <th>Bin Count</th>
                                            <th>Action</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody ng-show="pagedItems.length > 0">
                                        <tr ng-repeat="detail in pagedItems">
                                            <td>{{detail.parttype}}</td>
                                            <td>{{detail.FromDisposition}}</td>
                                            <td>{{detail.ToDisposition}}</td>
                                            <td>
                                                <span ng-if="!detail.mpn" style="color: #999;">N/A</span>
                                                <span ng-if="detail.mpn"
                                                      ng-click="searchMPNLocations(detail.mpn, detail.FromDispositionID, detail.parttypeid)"
                                                      style="color: #1976d2; cursor: pointer; text-decoration: underline; font-weight: 500;"
                                                      ng-class="{'text-muted': mpnLocationBusy && selectedMPN === detail.mpn}"
                                                      title="Click to find locations for this MPN">
                                                    <span ng-show="!mpnLocationBusy || selectedMPN !== detail.mpn">{{detail.mpn}}</span>
                                                    <span ng-show="mpnLocationBusy && selectedMPN === detail.mpn">
                                                        <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="14px" style="vertical-align: middle;"></md-progress-circular>
                                                        {{detail.mpn}}
                                                    </span>
                                                </span>
                                            </td>
                                            <td>{{detail.PickCompleted || 0}} / {{detail.PickQuantity}}</td>
                                            <td>
                                                <md-input-container class="md-block md-no-float includedsearch tdinput">
                                                    <input type="text"
                                                           ng-model="detail.BinName"
                                                           ng-enter="mapBinToDetail(detail)"
                                                           ng-disabled="detail.IsMapped">
                                                    <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right"
                                                               ng-click="mapBinToDetail(detail)"
                                                               ng-disabled="!detail.BinName || detail.IsMapped || detail.busy">
                                                        <span ng-show="!detail.busy">Go</span>
                                                        <span ng-show="detail.busy">
                                                            <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="16px"></md-progress-circular>
                                                        </span>
                                                    </md-button>
                                                </md-input-container>
                                            </td>
                                            <td class="text-center">
                                                <span ng-if="detail.MappedBinName">{{detail.AssetsCount || 0}} / {{detail.MaximumAssets || 0}}</span>
                                                <span ng-if="!detail.MappedBinName">-</span>
                                            </td>
                                            <td class="text-center actionicons">
                                                <!-- Actions Dropdown Menu -->
                                                <md-menu md-position-mode="target-right target">
                                                    <!-- Actions Menu Button -->
                                                    <md-button aria-label="Actions" ng-click="$mdMenu.open($event)" style="min-width: 80px; padding: 6px 12px;">
                                                        Actions <md-icon class="material-icons" style="font-size: 16px;">arrow_drop_down</md-icon>
                                                    </md-button>

                                                    <!-- Menu Content -->
                                                    <md-menu-content>
                                                        <!-- Create Bin Action -->
                                                        <md-menu-item>
                                                            <md-button ng-click="CreateBin(detail,$event)"
                                                                       ng-disabled="detail.MappedBinName">
                                                                Create Bin
                                                            </md-button>
                                                        </md-menu-item>

                                                        <!-- Move Bin Action -->
                                                        <md-menu-item>
                                                            <md-button ng-click="MoveBin(detail,$event)"
                                                                       ng-disabled="!detail.MappedBinName">
                                                                Move Bin
                                                            </md-button>
                                                        </md-menu-item>

                                                        <!-- Close Bin Action -->
                                                        <md-menu-item ng-show="detail.MappedBinName && (!detail.SealID || detail.SealID == '' || detail.SealID == null)">
                                                            <md-button ng-click="CloseBin(detail,$event)"
                                                                       ng-disabled="!detail.MappedBinName">
                                                                Close Bin
                                                            </md-button>
                                                        </md-menu-item>

                                                        <!-- Consolidate Bin Action -->
                                                        <md-menu-item>
                                                            <md-button ng-click="ConsolidateBin(detail,$event)"
                                                                       ng-disabled="!detail.MappedBinName">
                                                                Consolidate Bin
                                                            </md-button>
                                                        </md-menu-item>

                                                        <!-- Nest to Bin Action -->
                                                        <md-menu-item>
                                                            <md-button ng-click="NestToBin(detail,$event)"
                                                                       ng-disabled="!detail.MappedBinName">
                                                                Nest to Bin
                                                            </md-button>
                                                        </md-menu-item>

                                                        <!-- Print Action -->
                                                        <md-menu-item>
                                                            <md-button ng-click="PrintBinLabel(detail.CustomPalletID)"
                                                                       class="print-action"
                                                                       ng-disabled="!detail.MappedBinName">
                                                                Print
                                                            </md-button>
                                                        </md-menu-item>
                                                    </md-menu-content>
                                                </md-menu>
                                            </td>
                                            <td>
                                                <span class="status-cell" ng-class="{'pending': detail.Status === 'Pending', 'inprogress': detail.Status === 'In Progress', 'inactive': detail.Status === 'Inactive', 'completed': detail.Status === 'Completed'}">
                                                    {{detail.Status}}
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Loading indicator -->
                            <div ng-show="busy" class="text-center" style="padding: 20px;">
                                <md-progress-circular md-mode="indeterminate"></md-progress-circular>
                                <p>Loading configuration details...</p>
                            </div>
                        </md-card-content>
                    </md-card>

                    <!-- Pick Process Input Section -->
                    <md-card class="no-margin-h pt-0" ng-if="selectedConfiguration && pagedItems.length > 0">
                        <md-toolbar class="md-table-toolbar md-default" ng-init="PickProcessInputList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">
                                <i ng-click="PickProcessInputList = !PickProcessInputList" class="material-icons md-primary" ng-show="PickProcessInputList">keyboard_arrow_up</i>
                                <i ng-click="PickProcessInputList = !PickProcessInputList" class="material-icons md-primary" ng-show="!PickProcessInputList">keyboard_arrow_down</i>
                                <span ng-click="PickProcessInputList = !PickProcessInputList">Pick Process Input</span>
                                <div flex></div>
                            </div>
                        </md-toolbar>
                        <md-card-content ng-show="PickProcessInputList">
                            <div class="row">
                                <div class="col-md-6">
                                    <md-input-container class="md-block includedsearch" style="margin-bottom: 15px;">
                                        <label>Source BIN</label>
                                        <input type="text"
                                               ng-model="pickInput.sourceBinName"
                                               ng-enter="processSourceBin()"
                                               ng-disabled="serialNumberBusy">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right"
                                                   ng-click="processSourceBin()"
                                                   ng-disabled="!pickInput.sourceBinName || sourceBinBusy || serialNumberBusy"
                                                   style="bottom: 6px;">
                                            <span ng-show="!sourceBinBusy">Go</span>
                                            <span ng-show="sourceBinBusy">
                                                <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="16px"></md-progress-circular>
                                            </span>
                                        </md-button>
                                    </md-input-container>
                                </div>
                                <div class="col-md-6">
                                    <md-input-container class="md-block includedsearch" style="margin-bottom: 15px;">
                                        <label>Serial Number</label>
                                        <input type="text"
                                               ng-model="pickInput.serialNumber"
                                               ng-enter="processSerialNumber()"
                                               ng-disabled="sourceBinBusy">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right"
                                                   ng-click="processSerialNumber()"
                                                   ng-disabled="!pickInput.serialNumber || pickInput.serialNumber.trim() === '' || serialNumberBusy || sourceBinBusy"
                                                   style="bottom: 6px;">
                                            <span ng-show="!serialNumberBusy">Go</span>
                                            <span ng-show="serialNumberBusy">
                                                <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="16px"></md-progress-circular>
                                            </span>
                                        </md-button>
                                    </md-input-container>
                                </div>
                            </div>
                        </md-card-content>
                    </md-card>

                    <!-- MPN Location Suggestions Section -->
                    <md-card class="no-margin-h pt-0" ng-if="selectedConfiguration && pagedItems.length > 0">
                        <md-toolbar class="md-table-toolbar md-default" ng-init="MPNLocationSuggestionsList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">
                                <i ng-click="MPNLocationSuggestionsList = !MPNLocationSuggestionsList" class="material-icons md-primary" ng-show="MPNLocationSuggestionsList">keyboard_arrow_up</i>
                                <i ng-click="MPNLocationSuggestionsList = !MPNLocationSuggestionsList" class="material-icons md-primary" ng-show="!MPNLocationSuggestionsList">keyboard_arrow_down</i>
                                <span ng-click="MPNLocationSuggestionsList = !MPNLocationSuggestionsList">MPN Location Suggestions</span>
                                <div flex></div>
                                <span ng-if="mpnLocationSuggestions.length > 0" style="font-size: 14px; color: #666;">
                                    {{mpnLocationSuggestions.length}} location(s) found
                                </span>
                            </div>
                        </md-toolbar>
                        <md-card-content ng-show="MPNLocationSuggestionsList">
                            <div class="row">
                                <div class="col-md-12">
                                    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">
                                        <md-icon class="material-icons" style="vertical-align: middle; margin-right: 8px; color: #1976d2;">info</md-icon>
                                        Click on any MPN in the table above to find locations where serials exist in your system.
                                    </p>
                                </div>
                            </div>

                            <!-- Loading indicator -->
                            <div ng-show="mpnLocationBusy" class="text-center" style="padding: 20px;">
                                <md-progress-circular md-mode="indeterminate"></md-progress-circular>
                                <p>Searching for {{selectedMPN}} locations...</p>
                            </div>

                            <!-- No results message -->
                            <div ng-show="!mpnLocationBusy && mpnLocationSuggestions.length === 0 && selectedMPN" class="text-center" style="padding: 20px;">
                                <md-icon class="material-icons" style="font-size: 48px; opacity: 0.5; color: #999;">location_off</md-icon>
                                <p style="margin-top: 15px; color: #666;">No valid locations found for MPN: {{selectedMPN}}</p>
                                <p style="margin-top: 5px; color: #999; font-size: 14px;">
                                    Locations must match disposition, part type, and have valid serial status.
                                </p>
                            </div>

                            <!-- Results table -->
                            <div ng-show="!mpnLocationBusy && mpnLocationSuggestions.length > 0">
                                <div style="margin-bottom: 15px;">
                                    <h4 style="margin: 0; color: #2e7d32;">
                                        <md-icon class="material-icons" style="vertical-align: middle; margin-right: 8px;">location_on</md-icon>
                                        Locations for MPN: {{selectedMPN}}
                                    </h4>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">
                                        Showing locations with matching disposition ({{selectedFromDisposition}}) and part type
                                    </p>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Location Name</th>
                                                <th>Bin Name</th>
                                                <th>Available Quantity</th>
                                                <th>Disposition</th>
                                                <th>Part Type</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr ng-repeat="location in mpnLocationSuggestions">
                                                <td>
                                                    <strong>{{location.LocationName || 'No Location Group'}}</strong>
                                                </td>
                                                <td>
                                                    <span style="font-family: monospace; background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">
                                                        {{location.BinName}}
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span style="font-weight: 600; color: #2e7d32;">{{location.AvailableQuantity}}</span>
                                                </td>
                                                <td>{{location.DispositionName}}</td>
                                                <td>{{location.PartTypeName}}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </md-card-content>
                    </md-card>

                    <!-- Recent Transactions Section -->
                    <md-card class="no-margin-h pt-0" ng-if="selectedConfiguration && recentTransactions.length > 0">
                        <md-toolbar class="md-table-toolbar md-default" ng-init="RecentTransactionsList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">
                                <i ng-click="RecentTransactionsList = !RecentTransactionsList" class="material-icons md-primary" ng-show="RecentTransactionsList">keyboard_arrow_up</i>
                                <i ng-click="RecentTransactionsList = !RecentTransactionsList" class="material-icons md-primary" ng-show="!RecentTransactionsList">keyboard_arrow_down</i>
                                <span ng-click="RecentTransactionsList = !RecentTransactionsList">Recent Transactions</span>
                                <div flex></div>
                                <span ng-if="recentTransactions.length > 0" style="font-size: 14px; color: #666;">
                                    {{recentTransactions.length}} recent transaction(s)
                                </span>
                            </div>
                        </md-toolbar>
                        <md-card-content ng-show="RecentTransactionsList && recentTransactions.length > 0">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Serial Number</th>
                                            <th>MPN</th>
                                            <th>From Bin</th>
                                            <th>To Bin</th>
                                            <th>Date/Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="transaction in recentTransactions">
                                            <td>{{transaction.SerialNumber}}</td>
                                            <td>{{transaction.UniversalModelNumber || 'N/A'}}</td>
                                            <td>{{transaction.FromBinName || 'N/A'}}</td>
                                            <td>{{transaction.ToBinName}}</td>
                                            <td>{{transaction.CreatedDate | date:'MM/dd/yyyy HH:mm:ss'}}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </md-card-content>
                    </md-card>
                </div>
            </article>
        </div>
    </div>
</div>