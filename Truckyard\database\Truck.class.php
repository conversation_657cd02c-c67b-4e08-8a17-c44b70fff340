<?php
session_start();
include_once("admin.class.php");
include_once("../../common_functions.php");
include_once("../../excel_reader/SimpleXLSX.php");
include_once("../../Truckyard/templates/xlsxwriter.class.php");
include_once("../../Truckyard/templates/xlsxwriterplus.class.php");
use Aws\S3\S3Client;
use Aws\S3\Exception\S3Exception;

class TruckClass extends CommonClass
{
 
	public function GetParkingLocations($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
		/*	if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Workflow')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Workflow Page';
				return json_encode($json);
			}*/
			$query = "select * from ParkingLocation where Status = 'Active' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'";

			// Filter by ParkTypeID if provided
			if (isset($data['ParkTypeID']) && !empty($data['ParkTypeID'])) {
				$query .= " AND ParkTypeID = '" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeID']) . "'";
			}

			$query .= " order by `ParkingLocationName`";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result =  array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Parking Location Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetParkTypes($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			$query = "select * from ParkType where Status = 'Active' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "' order by `ParkTypeName`";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result =  array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Park Type Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetCalendarData($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			$query = "SELECT T.*, PL.ParkingLocationName, C.CarrierName
					  FROM Truck T
					  LEFT JOIN ParkingLocation PL ON T.ParkingLocationID = PL.ParkingLocationID
					  LEFT JOIN Carrier C ON T.CarrierID = C.CarrierID
					  WHERE T.ArrivalDate = '" . mysqli_real_escape_string($this->connectionlink, $data['Date']) . "'";

			// Filter by ParkTypeID if provided
			if (isset($data['ParkTypeID']) && !empty($data['ParkTypeID'])) {
				$query .= " AND T.ParkTypeID = '" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeID']) . "'";
			}

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$result = array();
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				while ($row = mysqli_fetch_assoc($q)) {
					// Create a key based on time and location for easy lookup
					$key = $row['ArrivalTime'] . '_' . $row['ParkingLocationID'];
					$result[$key] = array(
						'TruckID' => $row['TruckID'],
						'TruckReg' => $row['TruckReg'],
						'TrailerNumber' => $row['TrailerNumber'],
						'DriverName' => $row['DriverName'],
						'CarrierName' => $row['CarrierName'],
						'ParkingLocationName' => $row['ParkingLocationName'],
						'ArrivalTime' => $row['ArrivalTime'],
						'Status' => $row['Status'],
						'LoadNumber' => $row['LoadNumber'],
						'LoadType' => $row['LoadType']
					);
				}
			}

			$json['Success'] = true;
			$json['Result'] = $result;
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetTruckTypes($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => array()
		);
		try {
			$query = "SELECT TruckTypeID, TruckTypeName, Description, Status
					  FROM TruckType
					  WHERE Status = 'Active' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'
					  ORDER BY TruckTypeName ASC";

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$truckTypes = array();
			while ($row = mysqli_fetch_assoc($q)) {
				$truckTypes[] = $row;
			}

			$json['Success'] = true;
			$json['Result'] = $truckTypes;

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetCarriers($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => array()
		);
		try {
			$query = "SELECT CarrierID, CarrierName, StatusID, WasteCollectionEligible, WasteCollectionPermit
					  FROM Carrier
					  WHERE StatusID = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."'
					  ORDER BY CarrierName ASC";

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$carriers = array();
			while ($row = mysqli_fetch_assoc($q)) {
				$carriers[] = $row;
			}

			$json['Success'] = true;
			$json['Result'] = $carriers;

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function TruckSave($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		$chkdt = $data['ArrivalDate'];
		$chkdtarr=explode("GMT",$chkdt);
		$newdt= strtotime($chkdtarr[0]);
		$data['ArrivalDate'] = date("Y-m-d",$newdt);

		// Arrival Time
		$chkdt = $data['ArrivalTime'];
		$chkdtarr = explode("GMT", $chkdt);
		$newdt = strtotime($chkdtarr[0]);
		$data['ArrivalTime'] = date("H:i:s", $newdt); // <-- Just time

		// Departure Time
		/*$chkdt = $data['DepartureTime'];
		$chkdtarr = explode("GMT", $chkdt);
		$newdt = strtotime($chkdtarr[0]);
		$data['DepartureTime'] = date("H:i:s", $newdt); // <-- Just time*/


		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recovery type')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Recoverytype Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Recovery type')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Recoverytype Page';
			return json_encode($json);
		}*/

		// Validate that either TruckReg or TrailerNumber is provided
		$truckReg = trim($data['TruckReg'] ?? '');
		$trailerNumber = trim($data['TrailerNumber'] ?? '');

		if (empty($truckReg) && empty($trailerNumber)) {
			$json['Success'] = false;
			$json['Result'] = 'Either Truck Registration or Trailer Number is required.';
			return json_encode($json);
		}

		// Validate parking location availability for the specified date and time
		if (!empty($data['ParkingLocationID']) && !empty($data['ArrivalDate']) && !empty($data['ArrivalTime'])) {
			$checkQuery = "SELECT TruckID, TruckReg, TrailerNumber FROM Truck
						   WHERE ParkingLocationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "'
						   AND ArrivalDate = '" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalDate']) . "'
						   AND ArrivalTime = '" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalTime']) . "'";

			// If updating existing truck, exclude the current truck from the check
			if (!empty($data['TruckID'])) {
				$checkQuery .= " AND TruckID != '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";
			}

			$checkResult = mysqli_query($this->connectionlink, $checkQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = 'Database error during validation: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($checkResult) > 0) {
				$existingBooking = mysqli_fetch_assoc($checkResult);
				$existingVehicle = !empty($existingBooking['TruckReg']) ?
								   'Truck: ' . $existingBooking['TruckReg'] :
								   'Trailer: ' . $existingBooking['TrailerNumber'];

				$json['Success'] = false;
				$json['Result'] = 'This parking location is already booked for the selected date and time by ' . $existingVehicle . '. Please choose a different location or time.';
				return json_encode($json);
			}
		}

		// Get original data BEFORE updating for change tracking (only for updates)
		$originalData = null;
		if ($data['TruckID'] != '') {
			// Use simple query like GetTruckDetails does
			$originalQuery = "SELECT * FROM Truck WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";
			$originalResult = mysqli_query($this->connectionlink, $originalQuery);
			if ($originalResult && mysqli_num_rows($originalResult) > 0) {
				$originalData = mysqli_fetch_assoc($originalResult);
			}
		}

		//return json_encode($json);
		if ($data['TruckID'] == '') { //If New Class
			$query = "insert into Truck (FacilityID,ArrivalType,ParkTypeID,ParkingLocationID,CarrierID,ArrivalDate,ArrivalTime,LoadType,LoadNumber,LoadQuantity,TruckTypeID,TruckReg,TrailerNumber,DriverName,DriverID,ShipmentTicketID,SealID,ClassificationType,WasteCollectionPermit,Notes,Status,CreatedDate,CreatedBy) values ('" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalType']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['CarrierID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalDate']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalTime']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['LoadType']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['LoadNumber']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['LoadQuantity']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['TruckReg']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['TrailerNumber']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['DriverName']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['DriverID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ShipmentTicketID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['SealID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ClassificationType']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['WasteCollectionPermit']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Notes']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',NOW(),'" . $_SESSION['user']['UserId'] . "')";
		} else {
			$query = "update Truck set FacilityID='" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "',ArrivalType='" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalType']) . "',ParkTypeID='" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeID']) . "',ParkingLocationID='" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "',CarrierID='" . mysqli_real_escape_string($this->connectionlink, $data['CarrierID']) . "',ArrivalDate='" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalDate']) . "',ArrivalTime='" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalTime']) . "',LoadType='" . mysqli_real_escape_string($this->connectionlink, $data['LoadType']) . "',LoadNumber='" . mysqli_real_escape_string($this->connectionlink, $data['LoadNumber']) . "',LoadQuantity='" . mysqli_real_escape_string($this->connectionlink, $data['LoadQuantity']) . "',TruckTypeID='" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "',TruckReg='" . mysqli_real_escape_string($this->connectionlink, $data['TruckReg']) . "',TrailerNumber='" . mysqli_real_escape_string($this->connectionlink, $data['TrailerNumber']) . "',DriverName='" . mysqli_real_escape_string($this->connectionlink, $data['DriverName']) . "',DriverID='" . mysqli_real_escape_string($this->connectionlink, $data['DriverID']) . "',ShipmentTicketID='" . mysqli_real_escape_string($this->connectionlink, $data['ShipmentTicketID']) . "',SealID='" . mysqli_real_escape_string($this->connectionlink, $data['SealID']) . "',ClassificationType='" . mysqli_real_escape_string($this->connectionlink, $data['ClassificationType']) . "',WasteCollectionPermit='" . mysqli_real_escape_string($this->connectionlink, $data['WasteCollectionPermit']) . "',Notes='" . mysqli_real_escape_string($this->connectionlink, $data['Notes']) . "',Status='" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',UpdatedDate =NOW(),UpdatedBy='" . $_SESSION['user']['UserId'] . "' where TruckID='" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";
		}
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		if ($data['TruckID'] == '') {
			$insert_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = "New Truck created";
			$json['TruckID'] = $insert_id;

			// Log truck creation in tracking
			// Get parking location name for new booking
			$parkingLocationName = 'N/A';
			if (!empty($data['ParkingLocationID'])) {
				$locationQuery = "SELECT ParkingLocationName FROM ParkingLocation WHERE ParkingLocationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "'";
				$locationResult = mysqli_query($this->connectionlink, $locationQuery);
				if ($locationResult && mysqli_num_rows($locationResult) > 0) {
					$locationRow = mysqli_fetch_assoc($locationResult);
					$parkingLocationName = $locationRow['ParkingLocationName'];
				}
			}

			$trackingAction = 'New Truck Booking Created | Truck Reg: ' . ($data['TruckReg'] ?: 'N/A') .
							  ' | Trailer: ' . ($data['TrailerNumber'] ?: 'N/A') .
							  ' | Driver: ' . ($data['DriverName'] ?: 'N/A') .
							  ' | Load #: ' . ($data['LoadNumber'] ?: 'N/A') .
							  ' | Load Qty: ' . ($data['LoadQuantity'] ?: 'N/A') .
							  ' | Parking Location: ' . $parkingLocationName .
							  ' | Arrival Date: ' . $data['ArrivalDate'] .
							  ' | Arrival Time: ' . $data['ArrivalTime'] .
							  ' | Status: ' . $data['Status'];
			$this->logTruckTracking($insert_id, $trackingAction, 'Truck Booking');

		} else {
			$json['Success'] = true;
			$json['Result'] = "Truck modified";

			// Track changes using the original data retrieved before update
			if ($originalData) {
				$changes = array();

				// Check key fields for changes
				$fieldsToCheck = array(
					'TruckReg' => 'Truck Registration',
					'TrailerNumber' => 'Trailer Number',
					'DriverName' => 'Driver Name',
					'DriverID' => 'Driver ID',
					'LoadNumber' => 'Load Number',
					'LoadType' => 'Load Type',
					'LoadQuantity' => 'Load Quantity',
					'ArrivalDate' => 'Arrival Date',
					'ArrivalTime' => 'Arrival Time',
					'Status' => 'Status',
					'Notes' => 'Notes'
				);

				foreach ($fieldsToCheck as $field => $displayName) {
					$oldValue = isset($originalData[$field]) ? trim((string)$originalData[$field]) : '';
					$newValue = isset($data[$field]) ? trim((string)$data[$field]) : '';

					if ($oldValue !== $newValue) {
						$oldDisplay = !empty($oldValue) ? $oldValue : 'Empty';
						$newDisplay = !empty($newValue) ? $newValue : 'Empty';
						$changes[] = $displayName . ' changed from "' . $oldDisplay . '" to "' . $newDisplay . '"';
					}
				}

				// Check ID-based fields (Facility, Parking Location, Carrier)
				$idFieldsToCheck = array(
					'FacilityID' => array('name' => 'Facility', 'table' => 'Facility', 'nameField' => 'FacilityName'),
					'ParkingLocationID' => array('name' => 'Parking Location', 'table' => 'ParkingLocation', 'nameField' => 'ParkingLocationName'),
					'CarrierID' => array('name' => 'Carrier', 'table' => 'Carrier', 'nameField' => 'CarrierName')
				);

				foreach ($idFieldsToCheck as $field => $config) {
					$oldID = isset($originalData[$field]) ? trim((string)$originalData[$field]) : '';
					$newID = isset($data[$field]) ? trim((string)$data[$field]) : '';

					if ($oldID !== $newID) {
						// Get old name
						$oldName = 'Empty';
						if (!empty($oldID)) {
							$nameQuery = "SELECT " . $config['nameField'] . " FROM " . $config['table'] . " WHERE " . $field . " = '" . mysqli_real_escape_string($this->connectionlink, $oldID) . "'";
							$nameResult = mysqli_query($this->connectionlink, $nameQuery);
							if ($nameResult && mysqli_num_rows($nameResult) > 0) {
								$nameRow = mysqli_fetch_assoc($nameResult);
								$oldName = $nameRow[$config['nameField']];
							}
						}

						// Get new name
						$newName = 'Empty';
						if (!empty($newID)) {
							$nameQuery = "SELECT " . $config['nameField'] . " FROM " . $config['table'] . " WHERE " . $field . " = '" . mysqli_real_escape_string($this->connectionlink, $newID) . "'";
							$nameResult = mysqli_query($this->connectionlink, $nameQuery);
							if ($nameResult && mysqli_num_rows($nameResult) > 0) {
								$nameRow = mysqli_fetch_assoc($nameResult);
								$newName = $nameRow[$config['nameField']];
							}
						}

						$changes[] = $config['name'] . ' changed from "' . $oldName . '" to "' . $newName . '"';
					}
				}

				if (!empty($changes)) {
					$trackingAction = 'Truck Booking Modified: ' . implode(' | ', $changes);
					$this->logTruckTracking($data['TruckID'], $trackingAction, 'Truck Booking');
				}
			} else {
				// Fallback tracking if original data can't be retrieved
				$vehicleInfo = !empty($data['TruckReg']) ? 'Truck: ' . $data['TruckReg'] : 'Trailer: ' . ($data['TrailerNumber'] ?: 'N/A');
				$trackingAction = 'Truck Booking Modified for ' . $vehicleInfo;
				$this->logTruckTracking($data['TruckID'], $trackingAction, 'Truck Booking');
			}
		}
		return json_encode($json);
	}

	public function GetTruckDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Recoverytype Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Recoverytype Page';
			return json_encode($json);
		}*/

		//return json_encode($json);
		$query = "select * from Truck where TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "' ORDER BY TruckID";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$row['LoadQuantity'] = (int)$row['LoadQuantity'];
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Truck ID";
		}
		return json_encode($json);
	}

	public function GetTruckList($data)
	{
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['TruckID']
			);

			$query = "select T.*,PL.ParkingLocationName,F.FacilityName,C.CarrierName,C.POC,C.Phone,C.ContactEmail,C.Address,C.Description,C.WasteCollectionEligible,C.WasteCollectionPermit from Truck T left join ParkingLocation PL on T.ParkingLocationID = PL.ParkingLocationID left join facility F on T.FacilityID = F.FacilityID left join Carrier C on T.CarrierID = C.CarrierID where T.Status NOT IN ('Complete', 'No Show', 'Failed') AND T.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'";
			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'FacilityName') {
							$query = $query . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'CarrierName') {
							$query = $query . " AND C.CarrierName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ParkingLocationName') {
							$query = $query . " AND PL.ParkingLocationName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalType') {
							$query = $query . " AND T.ArrivalType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalDate') {
							$query = $query . " AND T.ArrivalDate like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalTime') {
							$query = $query . " AND T.ArrivalTime like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'LoadType') {
							$query = $query . " AND T.LoadType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'TruckReg') {
							$query = $query . " AND T.TruckReg like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'DriverName') {
							$query = $query . " AND T.DriverName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'DriverID') {
							$query = $query . " AND T.DriverID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Notes') {
							$query = $query . " AND T.Notes like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'LoadNumber') {
							$query = $query . " AND T.LoadNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'LoadQuantity') {
							$query = $query . " AND T.LoadQuantity like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'TrailerNumber') {
							$query = $query . " AND T.TrailerNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Status') {
							$query = $query . " AND T.Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}

						if ($key == 'ShipmentTicketID') {
							$query = $query . " AND T.ShipmentTicketID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}

						if ($key == 'SealID') {
							$query = $query . " AND T.SealID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}

					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by F.FacilityName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'CarrierName') {
					$query = $query . " order by C.CarrierName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ParkingLocationName') {
					$query = $query . " order by PL.ParkingLocationName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalType') {
					$query = $query . " order by T.ArrivalType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalDate') {
					$query = $query . " order by T.ArrivalDate " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalTime') {
					$query = $query . " order by T.ArrivalTime " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LoadType') {
					$query = $query . " order by T.LoadType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'TruckReg') {
					$query = $query . " order by T.TruckReg " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'DriverName') {
					$query = $query . " order by T.DriverName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'DriverID') {
					$query = $query . " order by T.DriverID " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Notes') {
					$query = $query . " order by T.Notes " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LoadNumber') {
					$query = $query . " order by T.LoadNumber " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LoadQuantity') {
					$query = $query . " order by T.LoadQuantity " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'TrailerNumber') {
					$query = $query . " order by T.TrailerNumber " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Status') {
					$query = $query . " order by T.Status " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ShipmentTicketID') {
					$query = $query . " order by T.ShipmentTicketID " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'SealID') {
					$query = $query . " order by T.SealID " . $order_by_type . " ";
				} 
 			} else {
				$query = $query . " order by T.TruckID desc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					 // Calculate DepartureTime: 30 mins after ArrivalTime
            if (!empty($row['ArrivalTime'])) {
                $arrival = new DateTime($row['ArrivalTime']);
                $arrival->modify('+30 minutes');
                $row['DepartureTime'] = $arrival->format('H:i');
            } else {
                $row['DepartureTime'] = null;
            }
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Data Available";
			}

			if ($data['skip'] == 0) {

				$query1 = "select count(*) from Truck T left join ParkingLocation PL on T.ParkingLocationID = PL.ParkingLocationID
								left join facility F on T.FacilityID = F.FacilityID	left join Carrier C on T.CarrierID = C.CarrierID where T.Status NOT IN ('Complete', 'No Show', 'Failed') AND T.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'FacilityName') {
								$query1 = $query1 . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'CarrierName') {
								$query1 = $query1 . " AND C.CarrierName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ParkingLocationName') {
								$query1 = $query1 . " AND PL.ParkingLocationName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalType') {
								$query1 = $query1 . " AND T.ArrivalType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalDate') {
								$query1 = $query1 . " AND T.ArrivalDate like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalTime') {
								$query1 = $query1 . " AND T.ArrivalTime like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'LoadType') {
								$query1 = $query1 . " AND T.LoadType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'TruckReg') {
								$query1 = $query1 . " AND T.TruckReg like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'DriverName') {
								$query1 = $query1 . " AND T.DriverName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'DriverID') {
								$query1 = $query1 . " AND T.DriverID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Notes') {
								$query1 = $query1 . " AND T.Notes like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'LoadNumber') {
								$query1 = $query1 . " AND T.LoadNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'LoadQuantity') {
								$query1 = $query1 . " AND T.LoadQuantity like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'TrailerNumber') {
								$query1 = $query1 . " AND T.TrailerNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Status') {
								$query1 = $query1 . " AND T.Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}


							if ($key == 'ShipmentTicketID') {
								$query1 = $query1 . " AND T.ShipmentTicketID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}

							if ($key == 'SealID') {
								$query1 = $query1 . " AND T.SealID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}

						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function GetCompleteNoShowTruckList($data)
	{
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['TruckID']
			);

			$query = "select T.*,PL.ParkingLocationName,F.FacilityName,C.CarrierName,C.POC,C.Phone,C.ContactEmail,C.Address,C.Description,C.WasteCollectionEligible,C.WasteCollectionPermit from Truck T left join ParkingLocation PL on T.ParkingLocationID = PL.ParkingLocationID left join facility F on T.FacilityID = F.FacilityID left join Carrier C on T.CarrierID = C.CarrierID where T.Status IN ('Complete', 'No Show', 'Failed') AND T.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'";
			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'FacilityName') {
							$query = $query . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'CarrierName') {
							$query = $query . " AND C.CarrierName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ParkingLocationName') {
							$query = $query . " AND PL.ParkingLocationName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalType') {
							$query = $query . " AND T.ArrivalType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalDate') {
							$query = $query . " AND T.ArrivalDate like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalTime') {
							$query = $query . " AND T.ArrivalTime like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'LoadType') {
							$query = $query . " AND T.LoadType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'TruckReg') {
							$query = $query . " AND T.TruckReg like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'DriverName') {
							$query = $query . " AND T.DriverName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'DriverID') {
							$query = $query . " AND T.DriverID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Notes') {
							$query = $query . " AND T.Notes like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'LoadNumber') {
							$query = $query . " AND T.LoadNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'LoadQuantity') {
							$query = $query . " AND T.LoadQuantity like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'TrailerNumber') {
							$query = $query . " AND T.TrailerNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Status') {
							$query = $query . " AND T.Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}

						if ($key == 'ShipmentTicketID') {
							$query = $query . " AND T.ShipmentTicketID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}

						if ($key == 'SealID') {
							$query = $query . " AND T.SealID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}

					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by F.FacilityName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'CarrierName') {
					$query = $query . " order by C.CarrierName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ParkingLocationName') {
					$query = $query . " order by PL.ParkingLocationName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalType') {
					$query = $query . " order by T.ArrivalType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalDate') {
					$query = $query . " order by T.ArrivalDate " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalTime') {
					$query = $query . " order by T.ArrivalTime " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LoadType') {
					$query = $query . " order by T.LoadType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'TruckReg') {
					$query = $query . " order by T.TruckReg " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'DriverName') {
					$query = $query . " order by T.DriverName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'DriverID') {
					$query = $query . " order by T.DriverID " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Notes') {
					$query = $query . " order by T.Notes " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LoadNumber') {
					$query = $query . " order by T.LoadNumber " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LoadQuantity') {
					$query = $query . " order by T.LoadQuantity " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'TrailerNumber') {
					$query = $query . " order by T.TrailerNumber " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Status') {
					$query = $query . " order by T.Status " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ShipmentTicketID') {
					$query = $query . " order by T.ShipmentTicketID " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'SealID') {
					$query = $query . " order by T.SealID " . $order_by_type . " ";
				}
 			} else {
				$query = $query . " order by T.TruckID desc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					 // Calculate DepartureTime: 30 mins after ArrivalTime
            if (!empty($row['ArrivalTime'])) {
                $arrival = new DateTime($row['ArrivalTime']);
                $arrival->modify('+30 minutes');
                $row['DepartureTime'] = $arrival->format('H:i');
            } else {
                $row['DepartureTime'] = null;
            }
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Data Available";
			}

			if ($data['skip'] == 0) {

				$query1 = "select count(*) from Truck T left join ParkingLocation PL on T.ParkingLocationID = PL.ParkingLocationID
								left join facility F on T.FacilityID = F.FacilityID	left join Carrier C on T.CarrierID = C.CarrierID where T.Status IN ('Complete', 'No Show', 'Failed') AND T.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'FacilityName') {
								$query1 = $query1 . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'CarrierName') {
								$query1 = $query1 . " AND C.CarrierName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ParkingLocationName') {
								$query1 = $query1 . " AND PL.ParkingLocationName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalType') {
								$query1 = $query1 . " AND T.ArrivalType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalDate') {
								$query1 = $query1 . " AND T.ArrivalDate like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalTime') {
								$query1 = $query1 . " AND T.ArrivalTime like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'LoadType') {
								$query1 = $query1 . " AND T.LoadType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'TruckReg') {
								$query1 = $query1 . " AND T.TruckReg like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'DriverName') {
								$query1 = $query1 . " AND T.DriverName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'DriverID') {
								$query1 = $query1 . " AND T.DriverID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Notes') {
								$query1 = $query1 . " AND T.Notes like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'LoadNumber') {
								$query1 = $query1 . " AND T.LoadNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'LoadQuantity') {
								$query1 = $query1 . " AND T.LoadQuantity like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'TrailerNumber') {
								$query1 = $query1 . " AND T.TrailerNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Status') {
								$query1 = $query1 . " AND T.Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}

							if ($key == 'ShipmentTicketID') {
								$query1 = $query1 . " AND T.ShipmentTicketID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'SealID') {
								$query1 = $query1 . " AND T.SealID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}

						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function DeleteTruck($data)
		{
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);

			try {
				// First, check the current status of the truck before allowing deletion
				$statusCheckQuery = "SELECT Status FROM Truck WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['id']) . "'";
				$statusResult = mysqli_query($this->connectionlink, $statusCheckQuery);

				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = 'Error checking truck status: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				if (mysqli_num_rows($statusResult) == 0) {
					$json['Success'] = false;
					$json['Result'] = 'Truck booking not found.';
					return json_encode($json);
				}

				$statusRow = mysqli_fetch_assoc($statusResult);
				$currentStatus = $statusRow['Status'];

				// Prevent deletion if truck status is 'In Progress' or 'Arrived'
				if ($currentStatus === 'In Progress' || $currentStatus === 'Arrived') {
					$json['Success'] = false;
					$json['Result'] = 'Cannot delete booking with status "' . $currentStatus . '". Only bookings that have not yet arrived can be deleted.';
					return json_encode($json);
				}

				$transaction = 'Administration ---> eViridis Administration --->Truck Booking';
				$description = 'Truck Booking Delete';
				$this->RecordUserTransaction($transaction, $description);

				$sql = "DELETE FROM Truck WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['id']) . "'";
				$query = mysqli_query($this->connectionlink, $sql);

				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				} else {
					$json['Success'] = true;
					$json['Result'] = "Record Deleted Successfully.";
					return json_encode($json);
				}
			} catch (Exception $e) {
				$json['Success'] = false;
				$json['Result'] = 'Error deleting truck: ' . $e->getMessage();
				return json_encode($json);
			}
		}
	public function ChangeStatus($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);

		// Get original status for validation and tracking
		$originalStatus = isset($data['OriginalStatus']) ? $data['OriginalStatus'] : '';
		$newStatus = $data['Status'];
		$truckID = $data['TruckID'];

		// If no original status provided, get it from database
		if (empty($originalStatus)) {
			$statusQuery = "SELECT Status FROM Truck WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $truckID) . "'";
			$statusResult = mysqli_query($this->connectionlink, $statusQuery);
			if ($statusResult && mysqli_num_rows($statusResult) > 0) {
				$statusRow = mysqli_fetch_assoc($statusResult);
				$originalStatus = $statusRow['Status'];
			}
		}

		// Validation: Check if status change is allowed
		$validationResult = $this->validateStatusChange($originalStatus, $newStatus);
		if (!$validationResult['valid']) {
			$json['Success'] = false;
			$json['Result'] = $validationResult['message'];
			return json_encode($json);
		}

		// Update the status
		$query = "UPDATE Truck SET
				  Status = '" . mysqli_real_escape_string($this->connectionlink, $newStatus) . "',
				  UpdatedDate = NOW(),
				  UpdatedBy = '" . $_SESSION['user']['UserId'] . "'
				  WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $truckID) . "'";

		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		} else {
			// Log the status change in tracking
			$trackingAction = 'Status Changed | From: ' . $originalStatus . ' | To: ' . $newStatus .
							  ' | Changed by: ' . $_SESSION['user']['UserName'];
			$this->logTruckTracking($truckID, $trackingAction, 'Truck List');

			$json['Success'] = true;
			$json['Result'] = 'Status changed from "' . $originalStatus . '" to "' . $newStatus . '" successfully';
			return json_encode($json);
		}
	}

	// Validation function for status changes
	private function validateStatusChange($originalStatus, $newStatus) {
		// If status hasn't changed, allow it
		if ($originalStatus === $newStatus) {
			return array('valid' => true);
		}

		// Define allowed transitions
		$allowedTransitions = array(
			'Requested' => array('Arrived', 'No Show'),
			'Reserved' => array('Arrived', 'No Show')
		);

		// Check if the original status allows the new status
		if (isset($allowedTransitions[$originalStatus]) &&
			in_array($newStatus, $allowedTransitions[$originalStatus])) {
			return array('valid' => true);
		}

		// Generate appropriate error message
		$message = '';
		if ($newStatus === 'No Show') {
			$message = 'No Show status can only be set for bookings with "Requested" or "Reserved" status. Current status: ' . $originalStatus;
		} else if ($newStatus === 'Arrived') {
			$message = 'Arrived status can only be set for bookings with "Requested" or "Reserved" status. Current status: ' . $originalStatus;
		} else {
			$message = 'Status change from "' . $originalStatus . '" to "' . $newStatus . '" is not allowed.';
		}

		return array(
			'valid' => false,
			'message' => $message
		);
	}

	public function GenerateTruckListxls($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['TruckListxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

	public function GenerateCompleteNoShowTruckListxls($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['CompleteNoShowTruckListxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

	public function UploadTruckBookingFile($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available',
			'Errors' => array()
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Truck Booking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Truck Booking Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Truck Booking')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Truck Booking Page';
				return json_encode($json);
			}
			if($data['file']['type'] != 'application/vnd.ms-excel' && $data['file']['type'] != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
				$json['Success'] = false;
				$json['Result'] = 'Invalid File type. Please upload Excel files only.';
				return json_encode($json);
			}
			$filename = time().$data['file']['name'];

			$upload = $this->UploadToS3($filename,$data['file']['tmp_name']);
			if(!$upload) {
				$json['Success'] = false;
				$json['Result'] = 'Unable to upload file to S3';
				return json_encode($json);
			}

			// Record file upload
			$query = "insert into admin_file_uploads (FileName,DateCreated,CreatedBy,FileType) values ('".$filename."',NOW(), '".$_SESSION['user']['UserId']."','TruckBooking')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$UploadID = mysqli_insert_id($this->connectionlink);

			$s3 = S3Client::factory(
				array(
					'credentials' => array(
						'key' => S3_key_eviridis,
						'secret' => S3_secret_eviridis
					),
					'version' => 'latest',
					'region'  => S3_region_eviridis
				)
			);
			$s3->registerStreamWrapper();

			if($xlsx = SimpleXLSX::parse('s3://'.S3_bucket_eviridis.'/'.$filename)) {
				$new_records = 0;
				$error_count = 0;
				$errors = array();
				$new_records = 0;
				$error_count = 0;
				$errors = array();

				$row_number = 0;
				foreach ($xlsx->rows() as $elt) {
					$row_number++;
					$current_row = $elt;

					if($row_number == 1) { // Header validation
						$expected_headers = ['Facility','Arrival Type','Parking Type','Parking Location','Carrier','Expected Arrival Date','Expected Arrival Time','Load Type','Load Number','Vehicle Type','Truck Reg','Trailer Number','Driver','Driver ID','Shipment Ticket ID\'s','Seal ID','Classification Type','Waste Collection Permit','Notes','Status'];

						for($i = 0; $i < count($expected_headers); $i++) {
							if(!isset($elt[$i]) || trim($elt[$i]) != $expected_headers[$i]) {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format. Cell " . chr(65 + $i) . "1 should be '" . $expected_headers[$i] . "'";
								return json_encode($json);
							}
						}
						continue;
					}

					// Process data rows
					$exception = false;
					$exception_message = '';

					// Initialize variables
					$FacilityID = null;
					$ParkingLocationID = null;
					$ParkTypeID = null;
					$CarrierID = null;
					$TruckTypeID = null;

					// Validate Facility (Column A) - Only allow current user's facility
					if(empty(trim($elt[0]))) {
						$exception = true;
						$exception_message = 'Facility is required';
					} else {
						$query = "SELECT FacilityID FROM facility WHERE FacilityName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[0]))."' AND FacilityStatus = '1' AND FacilityID = '".$_SESSION['user']['FacilityID']."'";
						$result = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_num_rows($result) > 0) {
							$row = mysqli_fetch_assoc($result);
							$FacilityID = $row['FacilityID'];
						} else {
							$exception = true;
							$exception_message = 'You can only upload records for your facility: "'.trim($elt[0]).'"';
						}
					}

					// Validate Arrival Type (Column B)
					$ArrivalType = trim($elt[1]);
					if(empty($ArrivalType)) {
						$exception = true;
						$exception_message = 'Arrival Type is required';
					} elseif(!in_array($ArrivalType, ['Inbound', 'Outbound'])) {
						$exception = true;
						$exception_message = 'Arrival Type must be "Inbound" or "Outbound"';
					}

					// Validate Parking Type (Column C)
					if(!empty(trim($elt[2])) && $FacilityID) {
						$query = "SELECT ParkTypeID FROM ParkType WHERE ParkTypeName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."' AND FacilityID = '".$FacilityID."' AND Status = 'Active'";
						$result = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_num_rows($result) > 0) {
							$row = mysqli_fetch_assoc($result);
							$ParkTypeID = $row['ParkTypeID'];
						} else {
							$exception = true;
							$exception_message = 'Parking Type "'.trim($elt[2]).'" does not exist for this facility';
						}
					}

					// Validate Parking Location (Column D)
					if(!empty(trim($elt[3])) && $FacilityID) {
						$whereClause = "ParkingLocationName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."' AND FacilityID = '".$FacilityID."' AND Status = 'Active'";
						if($ParkTypeID) {
							$whereClause .= " AND ParkTypeID = '".$ParkTypeID."'";
						}
						$query = "SELECT ParkingLocationID FROM ParkingLocation WHERE ".$whereClause;
						$result = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_num_rows($result) > 0) {
							$row = mysqli_fetch_assoc($result);
							$ParkingLocationID = $row['ParkingLocationID'];
						} else {
							$exception = true;
							$exception_message = 'Parking Location "'.trim($elt[3]).'" does not exist for this facility and parking type';
						}
					}

					// Validate Carrier (Column E)
					if(!empty(trim($elt[4])) && $FacilityID) {
						$query = "SELECT CarrierID FROM Carrier WHERE CarrierName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."' AND StatusID = '1' AND FacilityID = '".$FacilityID."'";
						$result = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_num_rows($result) > 0) {
							$row = mysqli_fetch_assoc($result);
							$CarrierID = $row['CarrierID'];
						} else {
							$exception = true;
							$exception_message = 'Carrier "'.trim($elt[4]).'" does not exist for this facility';
						}
					}

					// Validate Arrival Date (Column F)
					$ArrivalDate = trim($elt[5]);
					if(empty($ArrivalDate)) {
						$exception = true;
						$exception_message = 'Arrival Date is required';
					} else {
						// Convert Excel date to MySQL format
						$convertedDate = $this->convertExcelDateToMySQLDate($ArrivalDate);
						if($convertedDate === false) {
							$exception = true;
							$exception_message = 'Invalid Arrival Date format. Please use MM/DD/YYYY, DD/MM/YYYY, YYYY-MM-DD, or Excel date format';
						} else {
							$ArrivalDate = $convertedDate;
						}
					}

					// Validate Arrival Time (Column G) - Must be in 30-minute intervals
					$ArrivalTime = trim($elt[6]);
					if(!empty($ArrivalTime)) {
						if(!$this->isValidThirtyMinuteInterval($ArrivalTime)) {
							$exception = true;
							$exception_message = 'Arrival Time "'.$ArrivalTime.'" must be in 30-minute intervals (e.g., 09:00, 09:30, 10:00, 10:30)';
						}
					}

					// Validate Vehicle Type (Column J)
					if(!empty(trim($elt[9])) && $FacilityID) {
						$query = "SELECT TruckTypeID FROM TruckType WHERE TruckTypeName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[9]))."' AND Status = 'Active' AND FacilityID = '".$FacilityID."'";
						$result = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_num_rows($result) > 0) {
							$row = mysqli_fetch_assoc($result);
							$TruckTypeID = $row['TruckTypeID'];
						} else {
							$exception = true;
							$exception_message = 'Vehicle Type "'.trim($elt[9]).'" does not exist for this facility';
						}
					}

					// Validate required fields: Either Truck Reg OR Trailer Number must be provided
					$TruckReg = trim($elt[10]);
					$TrailerNumber = trim($elt[11]);
					if(empty($TruckReg) && empty($TrailerNumber)) {
						$exception = true;
						$exception_message = 'Either Truck Reg or Trailer Number is required';
					}

					// Validate Status (Column T)
					$Status = trim($elt[19]);
					if(empty($Status)) {
						$Status = 'Requested'; // Default status
					} elseif(!in_array($Status, ['Requested', 'Reserved'])) {
						$exception = true;
						$exception_message = 'Invalid Status. Must be one of: Requested, Reserved';
					}

					// Check for double booking validation (same parking location + arrival date + arrival time)
					if(!$exception && $ParkingLocationID && !empty($ArrivalDate) && !empty(trim($elt[6]))) {
						$arrivalTime = trim($elt[6]);
						$convertedArrivalTime = $this->convertExcelTimeToDBFormat($arrivalTime);
						$doubleBookingQuery = "SELECT TruckID, TruckReg, TrailerNumber FROM Truck
											   WHERE ParkingLocationID = '".$ParkingLocationID."'
											   AND ArrivalDate = '".$ArrivalDate."'
											   AND ArrivalTime = '".mysqli_real_escape_string($this->connectionlink, $convertedArrivalTime)."'";
						$doubleBookingResult = mysqli_query($this->connectionlink, $doubleBookingQuery);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = 'Double booking validation error: ' . mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_num_rows($doubleBookingResult) > 0) {
							$existingBooking = mysqli_fetch_assoc($doubleBookingResult);
							$existingVehicle = !empty($existingBooking['TruckReg']) ?
											   'Truck: ' . $existingBooking['TruckReg'] :
											   'Trailer: ' . $existingBooking['TrailerNumber'];
							$exception = true;
							// Extract just HH:MM for display in error message
							$displayTime = substr($convertedArrivalTime, 0, 5); // Get HH:MM from HH:MM:SS
							$exception_message = 'Double booking detected: Parking location "'.trim($elt[3]).'" is already booked for '.$ArrivalDate.' at '.$displayTime.' by '.$existingVehicle;
						}
					}

					// If validation failed, add error and continue to next row
					if($exception) {
						$error_count++;
						$errors[] = "Row $row_number: $exception_message";
						continue;
					}

					// Create truck booking record
					try {
						// Convert Excel time format to proper database format
						$convertedArrivalTime = $this->convertExcelTimeToDBFormat(trim($elt[6]));

						$insertQuery = "INSERT INTO Truck (
							FacilityID, ArrivalType, ParkTypeID, ParkingLocationID, CarrierID, ArrivalDate, ArrivalTime,
							DepartureTime, LoadType, LoadNumber, TruckTypeID, TruckReg, TrailerNumber,
							DriverName, DriverID, ShipmentTicketID, SealID, ClassificationType,
							WasteCollectionPermit, Notes, Status, CreatedDate, CreatedBy
						) VALUES (
							'" . mysqli_real_escape_string($this->connectionlink, $FacilityID) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $ArrivalType) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $ParkTypeID) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $ParkingLocationID) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $CarrierID) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $ArrivalDate) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $convertedArrivalTime) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $convertedArrivalTime) . "',
							'" . mysqli_real_escape_string($this->connectionlink, trim($elt[7])) . "',
							'" . mysqli_real_escape_string($this->connectionlink, trim($elt[8])) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $TruckTypeID) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $TruckReg) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $TrailerNumber) . "',
							'" . mysqli_real_escape_string($this->connectionlink, trim($elt[12])) . "',
							'" . mysqli_real_escape_string($this->connectionlink, trim($elt[13])) . "',
							'" . mysqli_real_escape_string($this->connectionlink, trim($elt[14])) . "',
							'" . mysqli_real_escape_string($this->connectionlink, trim($elt[15])) . "',
							'" . mysqli_real_escape_string($this->connectionlink, trim($elt[16])) . "',
							'" . mysqli_real_escape_string($this->connectionlink, trim($elt[17])) . "',
							'" . mysqli_real_escape_string($this->connectionlink, trim($elt[18])) . "',
							'" . mysqli_real_escape_string($this->connectionlink, $Status) . "',
							NOW(),
							'" . $_SESSION['user']['UserId'] . "'
						)";

						$insertResult = mysqli_query($this->connectionlink, $insertQuery);
						if(mysqli_error($this->connectionlink)) {
							$error_count++;
							$errors[] = "Row $row_number: Database error - " . mysqli_error($this->connectionlink);
						} else {
							$new_records++;
							$insert_id = mysqli_insert_id($this->connectionlink);

							// Log tracking for new booking
							$trackingAction = 'New Truck Booking Created via Bulk Upload | Truck Reg: ' . ($TruckReg ?: 'N/A') . ' | Trailer: ' . ($TrailerNumber ?: 'N/A') . ' | Driver: ' . (trim($elt[12]) ?: 'N/A') . ' | Load #: ' . (trim($elt[8]) ?: 'N/A') . ' | Arrival Date: ' . $ArrivalDate . ' | Status: ' . $Status;
							$this->logTruckTracking($insert_id, $trackingAction, 'Truck Booking');
						}

					} catch (Exception $e) {
						$error_count++;
						$errors[] = "Row $row_number: Exception - " . $e->getMessage();
					}

				}

				// Update upload record with results
				$message = $new_records.' New Truck Bookings Created';
				if($error_count > 0) {
					$message .= ', '.$error_count.' Errors encountered';
				}
				$query4 = "UPDATE admin_file_uploads SET Comments = '".mysqli_real_escape_string($this->connectionlink,$message)."' WHERE ID = '".$UploadID."'";
				mysqli_query($this->connectionlink,$query4);

				// Record transaction
				$transaction = 'Truckyard ---> Truck Booking';
				$description = 'Truck Booking File Uploaded';
				$this->RecordUserTransaction($transaction,$description);

				$json['Success'] = true;
				$json['Result'] = $message;
				$json['Errors'] = $errors;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Unable to parse Excel file: ' . SimpleXLSX::parseError();
				return json_encode($json);
			}
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function SearchActiveBooking($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => null
		);
		try {
			// Filter by facility and exclude completed/failed bookings
			$whereClause = "WHERE T.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'
							AND T.Status NOT IN ('Complete', 'No Show', 'Failed')";

			// Build search conditions - use AND to match all provided criteria for exact booking
			$searchConditions = array();

			if (!empty($data['LoadNumber'])) {
				$searchConditions[] = "T.LoadNumber = '" . mysqli_real_escape_string($this->connectionlink, $data['LoadNumber']) . "'";
			}

			if (!empty($data['TruckReg'])) {
				$searchConditions[] = "T.TruckReg = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckReg']) . "'";
			}

			if (!empty($data['TrailerNumber'])) {
				$searchConditions[] = "T.TrailerNumber = '" . mysqli_real_escape_string($this->connectionlink, $data['TrailerNumber']) . "'";
			}

			if (empty($searchConditions)) {
				$json['Success'] = false;
				$json['Result'] = 'Either Truck Registration, Trailer Number, or Load Number is required';
				return json_encode($json);
			}

			// Use AND condition to match all provided fields for exact booking identification
			$whereClause .= " AND " . implode(' AND ', $searchConditions);

			$query = "SELECT T.*,
					  F.FacilityName,
					  PL.ParkingLocationName,
					  C.CarrierName, C.POC, C.Phone, C.ContactEmail, C.Address, C.Description,
					  C.WasteCollectionEligible, C.WasteCollectionPermit,
					  TT.TruckTypeName as VehicleTypeName,
					  PT.ParkTypeName,
					  TDR.ID as ProcessedID, TDR.DockLockEngaged, TDR.SealID, TDR.CheckinDate, TDR.CheckinTime,
					  TDR.DockingCompleted, TDR.DockingCompletedDate, TDR.DockingTDRLead,
					  TDR.ReleaseCompleted, TDR.ReleaseCompletedDate, TDR.ReleaseTDRLead,
					  TDR.Unsafe, TDR.UnsafeTDRManager, TDR.C3POEntryID, TDR.UnsafeOperationType
					  FROM Truck T
					  LEFT JOIN facility F ON T.FacilityID = F.FacilityID
					  LEFT JOIN ParkingLocation PL ON T.ParkingLocationID = PL.ParkingLocationID
					  LEFT JOIN Carrier C ON T.CarrierID = C.CarrierID
					  LEFT JOIN TruckType TT ON T.TruckTypeID = TT.TruckTypeID
					  LEFT JOIN ParkType PT ON T.ParkTypeID = PT.ParkTypeID
					  LEFT JOIN truck_docking_release TDR ON T.TruckID = TDR.TruckID
					  $whereClause
				  ORDER BY T.ArrivalDate DESC, T.ArrivalTime DESC
				  LIMIT 1";

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($q) > 0) {
				$booking = mysqli_fetch_assoc($q);

				// Check if truck is already processed
				if ($booking['Processed'] == 1 && $booking['ProcessedID']) {
					$booking['BookingProcessed'] = true;

					// Get completed steps for this truck
					$stepsQuery = "SELECT TDRSOPID, TdrType, StepNo FROM truck_docking_release_steps
								   WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $booking['TruckID']) . "'";
					$stepsResult = mysqli_query($this->connectionlink, $stepsQuery);

					$completedSteps = array();
					while ($step = mysqli_fetch_assoc($stepsResult)) {
						$completedSteps[] = $step;
					}
					$booking['CompletedSteps'] = $completedSteps;

					// Set completion status from database
					$booking['DockingCompleted'] = ($booking['DockingCompleted'] == 1);
					$booking['ReleaseCompleted'] = ($booking['ReleaseCompleted'] == 1);
				} else {
					$booking['BookingProcessed'] = false;
					$booking['DockingCompleted'] = false;
					$booking['ReleaseCompleted'] = false;
					$booking['CompletedSteps'] = array();
				}

				// Convert LoadQuantity to integer for proper number input handling
				$booking['LoadQuantity'] = (int)$booking['LoadQuantity'];

				$json['Success'] = true;
				$json['Result'] = $booking;
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No active booking found for the provided vehicle information';
			}

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetTDRSteps($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => array('docking' => array(), 'release' => array())
		);
		try {
			// Get docking steps
			$dockingQuery = "SELECT T.* FROM TDRSOP T
							LEFT JOIN TruckType TT ON T.TruckTypeID = TT.TruckTypeID
							WHERE TT.TruckTypeName = '" . mysqli_real_escape_string($this->connectionlink, $data['VehicleType']) . "'
							AND T.DockLockEngaged = '" . mysqli_real_escape_string($this->connectionlink, $data['DockLockEngaged']) . "'
							AND T.TdrType = 'Trailer Docking'
							AND T.Status = 'Active'
							ORDER BY T.StepNo ASC";

			$dockingResult = mysqli_query($this->connectionlink, $dockingQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$dockingSteps = array();
			while ($row = mysqli_fetch_assoc($dockingResult)) {
				$dockingSteps[] = $row;
			}

			// Get release steps
			$releaseQuery = "SELECT T.* FROM TDRSOP T
							LEFT JOIN TruckType TT ON T.TruckTypeID = TT.TruckTypeID
							WHERE TT.TruckTypeName = '" . mysqli_real_escape_string($this->connectionlink, $data['VehicleType']) . "'
							AND T.DockLockEngaged = '" . mysqli_real_escape_string($this->connectionlink, $data['DockLockEngaged']) . "'
							AND T.TdrType = 'Trailer Release'
							AND T.Status = 'Active'
							ORDER BY T.StepNo ASC";

			$releaseResult = mysqli_query($this->connectionlink, $releaseQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$releaseSteps = array();
			while ($row = mysqli_fetch_assoc($releaseResult)) {
				$releaseSteps[] = $row;
			}

			$json['Success'] = true;
			$json['Result'] = array(
				'docking' => $dockingSteps,
				'release' => $releaseSteps
			);

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function TestConnection($data)
	{
		$json = array(
			'Success' => true,
			'Result' => 'Connection test successful'
		);
		return json_encode($json);
	}

	public function VerifyTPVR($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'Invalid TPVR credentials'
		);
		try {

			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['TDRSpotter'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['TDRSpotterController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not TDR Spotter Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}

				$json['Success'] = true;				
				$json['Result'] = 'TPVR verified successfully';
				$json['VerifiedUser'] = $row['FirstName'] . ' ' . $row['LastName'];
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid TDR Spotter Controller or Password";
				return json_encode($json);
			}			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	// Helper function to check if parking location is available (not locked)
	public function CheckParkingLocationAvailability($parkingLocationID)
	{
		$query = "SELECT LockedLoadNumber FROM ParkingLocation WHERE ParkingLocationID = '" . mysqli_real_escape_string($this->connectionlink, $parkingLocationID) . "'";
		$result = mysqli_query($this->connectionlink, $query);

		if (mysqli_error($this->connectionlink)) {
			return array('available' => false, 'error' => mysqli_error($this->connectionlink));
		}

		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($result);
			if (!empty($row['LockedLoadNumber'])) {
				return array('available' => false, 'lockedBy' => $row['LockedLoadNumber']);
			}
		}

		return array('available' => true);
	}

	// Helper function to unlock parking location
	public function UnlockParkingLocation($truckID)
	{
		$getParkingLocationQuery = "SELECT ParkingLocationID FROM truck_docking_release WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $truckID) . "'";
		$getParkingLocationResult = mysqli_query($this->connectionlink, $getParkingLocationQuery);

		if (mysqli_error($this->connectionlink)) {
			return array('success' => false, 'error' => mysqli_error($this->connectionlink));
		}

		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$parkingLocationRow = mysqli_fetch_assoc($getParkingLocationResult);
			$unlockQuery = "UPDATE ParkingLocation SET
						   LockedLoadNumber = NULL
						   WHERE ParkingLocationID = '" . mysqli_real_escape_string($this->connectionlink, $parkingLocationRow['ParkingLocationID']) . "'";
			$unlockResult = mysqli_query($this->connectionlink, $unlockQuery);

			if (mysqli_error($this->connectionlink)) {
				return array('success' => false, 'error' => mysqli_error($this->connectionlink));
			}

			return array('success' => true);
		}

		return array('success' => false, 'error' => 'No parking location found for truck');
	}

	public function ProcessTruckBooking($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'Failed to process truck booking'
		);
		try {
			// Check if parking location is available (not locked)
			$availabilityCheck = $this->CheckParkingLocationAvailability($data['ParkingLocationID']);
			if (!$availabilityCheck['available']) {
				$json['Success'] = false;
				if (isset($availabilityCheck['error'])) {
					$json['Result'] = $availabilityCheck['error'];
				} else {
					$json['Result'] = 'Parking Location is currently locked by Load Number: ' . $availabilityCheck['lockedBy'];
				}
				return json_encode($json);
			}

			// Get the current status and load number of the truck
			$statusQuery = "SELECT Status, LoadNumber FROM Truck WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";
			$statusResult = mysqli_query($this->connectionlink, $statusQuery);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (!$statusResult || mysqli_num_rows($statusResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'Truck booking not found';
				return json_encode($json);
			}

			$statusRow = mysqli_fetch_assoc($statusResult);
			$currentStatus = $statusRow['Status'];
			$loadNumber = $statusRow['LoadNumber'];

			// Validate that only Arrived or In Progress bookings can be processed
			if ($currentStatus !== 'Arrived' && $currentStatus !== 'In Progress') {
				$json['Success'] = false;
				$json['Result'] = 'Only "Arrived" or "In Progress" bookings can be processed. Current status: ' . $currentStatus;
				return json_encode($json);
			}
			// Insert into truck_docking_release table with additional booking information
			$insertQuery = "INSERT INTO truck_docking_release
							(TruckID, ParkingLocationID, CarrierID, TruckTypeID, CheckinDate, CheckinTime, DockLockEngaged, SealID,
							 LoadQuantity, DriverName, DriverID, ShipmentTicketID, ClassificationType, WasteCollectionPermit,
							 CreatedDate, CreatedBy)
							VALUES ('" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['CarrierID']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['CheckinDate']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['CheckinTime']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['DockLockEngaged']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['SealID']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['LoadQuantity'] ?? '') . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['DriverName'] ?? '') . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['DriverID'] ?? '') . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['ShipmentTicketID'] ?? '') . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['ClassificationType'] ?? '') . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['WasteCollectionPermit'] ?? '') . "',
									NOW(),
									'" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['UserId']) . "')";

			$insertResult = mysqli_query($this->connectionlink, $insertQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$processedID = mysqli_insert_id($this->connectionlink);

			// Update Truck table with processed details and status
			$statusUpdate = '';
			if ($currentStatus === 'Arrived') {
				$statusUpdate = ", Status = 'In Progress'";
			}

			// Update Truck table with processed details and status (only processing info, not booking details)
			$updateQuery = "UPDATE Truck SET
							Processed = 1,
							ProcessedTime = NOW(),
							ProcessedBy = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['UserId']) . "'
							$statusUpdate
							WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";

			$updateResult = mysqli_query($this->connectionlink, $updateQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Lock the parking location with the load number
			$lockQuery = "UPDATE ParkingLocation SET
						  LockedLoadNumber = '" . mysqli_real_escape_string($this->connectionlink, $loadNumber) . "'
						  WHERE ParkingLocationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "'";
			$lockResult = mysqli_query($this->connectionlink, $lockQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Log tracking information with changes
			$trackingAction = 'Truck Booking Processed';
			if ($currentStatus === 'Arrived') {
				$trackingAction .= ' | Status changed from "Arrived" to "In Progress"';
			}
			if (!empty($data['TrackingChanges'])) {
				$trackingAction .= ' | Changes: ' . $data['TrackingChanges'];
			}
			$this->logTruckTracking($data['TruckID'], $trackingAction, 'Trailer Dock/Release');

			$json['Success'] = true;
			$json['Result'] = 'Truck booking processed successfully';
			$json['ProcessedID'] = $processedID;

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function SaveStepCompletion($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'Failed to save step completion'
		);
		try {
			// Insert into truck_docking_release_steps table
			$insertQuery = "INSERT INTO truck_docking_release_steps
							(TDRSOPID, TruckID, TdrType, StepNo, StepDescription, TDRSpotterController, StepCompletedDate, StepCompletedBy)
							VALUES ('" . mysqli_real_escape_string($this->connectionlink, $data['TDRSOPID']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['TdrType']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['StepNo']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['StepDescription']) . "',
									'" . mysqli_real_escape_string($this->connectionlink, $data['TDRSpotterController']) . "',
									NOW(),
									'" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['UserId']) . "')";

			$insertResult = mysqli_query($this->connectionlink, $insertQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$stepID = mysqli_insert_id($this->connectionlink);

			// Log tracking information with step details
			$trackingAction = 'Step Completed: ' . $data['TdrType'] . ' - Step ' . $data['StepNo'] . ': ' . $data['StepDescription'];
			$controllerDetails = !empty($data['TDRSpotterController']) ? ' | Controller: ' . $data['TDRSpotterController'] : '';
			$tdrSpotterLogin = !empty($data['TDRSpotterController']) ? $data['TDRSpotterController'] : null;
			$this->logTruckTracking($data['TruckID'], $trackingAction . $controllerDetails, 'Trailer Dock/Release', $tdrSpotterLogin);

			$json['Success'] = true;
			$json['Result'] = 'Step completion saved successfully';
			$json['StepID'] = $stepID;

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function CompleteDocking($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'Failed to complete docking'
		);
		try {
			// Verify TPVR credentials for TDR Lead			
			$userQuery = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['TDRLead'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";

			$userResult = mysqli_query($this->connectionlink, $userQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($userResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'Invalid TDR Lead credentials or insufficient permissions';
				return json_encode($json);
			}

			$leadUser = mysqli_fetch_assoc($userResult);


			if($leadUser['Status'] != '1') {
				$json['Success'] = false;
				$json['Result'] = "User is not active";
				return json_encode($json);
			}
			if($leadUser['TDRLeadController'] != '1') {
				$json['Success'] = false;
				$json['Result'] = "User is not TDR Lead Controller";
				return json_encode($json);
			}

			if($leadUser['UserId'] == $_SESSION['user']['UserId']) {
				$json['Success'] = false;
				$json['Result'] = "Controller should be different from logged in user";
				return json_encode($json);
			}

			// Update truck_docking_release table to mark docking as completed
			$updateQuery = "UPDATE truck_docking_release SET
							DockingCompleted = 1,
							DockingCompletedDate = NOW(),
							DockingCompletedBy = '" . mysqli_real_escape_string($this->connectionlink, $leadUser['UserId']) . "',
							DockingTDRLead = '" . mysqli_real_escape_string($this->connectionlink, $data['TDRLead']) . "'
							WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";

			$updateResult = mysqli_query($this->connectionlink, $updateQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Log tracking information
			$trackingAction = 'Docking Completed | TDR Lead: ' . $data['TDRLead'] . ' (' . $leadUser['FirstName'] . ' ' . $leadUser['LastName'] . ')';
			$this->logTruckTracking($data['TruckID'], $trackingAction, 'Trailer Dock/Release', null, $data['TDRLead']);

			$json['Success'] = true;
			$json['Result'] = 'Docking completed successfully';
			$json['LeadUser'] = $leadUser['FirstName'] . ' ' . $leadUser['LastName'];

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function CompleteRelease($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'Failed to complete release'
		);
		try {
			// Verify TPVR credentials for TDR Lead			
			$userQuery = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['TDRLead'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$userResult = mysqli_query($this->connectionlink, $userQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($userResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'Invalid TDR Lead credentials or insufficient permissions';
				return json_encode($json);
			}

			$leadUser = mysqli_fetch_assoc($userResult);


			if($leadUser['Status'] != '1') {
				$json['Success'] = false;
				$json['Result'] = "User is not active";
				return json_encode($json);
			}
			if($leadUser['TDRLeadController'] != '1') {
				$json['Success'] = false;
				$json['Result'] = "User is not TDR Lead Controller";
				return json_encode($json);
			}

			if($leadUser['UserId'] == $_SESSION['user']['UserId']) {
				$json['Success'] = false;
				$json['Result'] = "Controller should be different from logged in user";
				return json_encode($json);
			}

			// Update truck_docking_release table to mark release as completed
			$updateQuery = "UPDATE truck_docking_release SET
							ReleaseCompleted = 1,
							ReleaseCompletedDate = NOW(),
							ReleaseCompletedBy = '" . mysqli_real_escape_string($this->connectionlink, $leadUser['UserId']) . "',
							ReleaseTDRLead = '" . mysqli_real_escape_string($this->connectionlink, $data['TDRLead']) . "'
							WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";

			$updateResult = mysqli_query($this->connectionlink, $updateQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Update Truck table to Complete status after release completion
			$truckUpdateQuery = "UPDATE Truck SET
								 Status = 'Complete',
								 CompletedDate = NOW(),
								 CompletedBy = '" . mysqli_real_escape_string($this->connectionlink, $leadUser['UserId']) . "'
								 WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";

			$truckUpdateResult = mysqli_query($this->connectionlink, $truckUpdateQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Unlock the parking location by getting the parking location ID from truck_docking_release
			$getParkingLocationQuery = "SELECT ParkingLocationID FROM truck_docking_release WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";
			$getParkingLocationResult = mysqli_query($this->connectionlink, $getParkingLocationQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$parkingLocationRow = mysqli_fetch_assoc($getParkingLocationResult);
				$unlockQuery = "UPDATE ParkingLocation SET
							   LockedLoadNumber = NULL
							   WHERE ParkingLocationID = '" . mysqli_real_escape_string($this->connectionlink, $parkingLocationRow['ParkingLocationID']) . "'";
				$unlockResult = mysqli_query($this->connectionlink, $unlockQuery);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}

			// Log tracking information
			$trackingAction = 'Release Completed | TDR Lead: ' . $data['TDRLead'] . ' (' . $leadUser['FirstName'] . ' ' . $leadUser['LastName'] . ') | Truck Status: Complete';
			$this->logTruckTracking($data['TruckID'], $trackingAction, 'Trailer Dock/Release', null, $data['TDRLead']);

			$json['Success'] = true;
			$json['Result'] = 'Release completed successfully';
			$json['LeadUser'] = $leadUser['FirstName'] . ' ' . $leadUser['LastName'];

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function VerifyUnsafeTPVR($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'Invalid unsafe TPVR credentials'
		);
		try {
			// Verify TDR Manager credentials against user table			
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['TDRManager'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($q) > 0) {
				$manager = mysqli_fetch_assoc($q);


				if($manager['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($manager['TDRManagerController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not TDR Manager Controller";
					return json_encode($json);
				}

				if($manager['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}

				// Update truck_docking_release with unsafe TPVR data
				$updateQuery = "UPDATE truck_docking_release
				SET UnsafeTDRManager = '" . mysqli_real_escape_string($this->connectionlink, $data['TDRManager']) . "',
					Unsafe = 1,
					C3POEntryID = '" . mysqli_real_escape_string($this->connectionlink, $data['C3POEntryID']) . "',
					UnsafeVerifiedBy = '" . mysqli_real_escape_string($this->connectionlink, $manager['UserId']) . "',
					UnsafeVerifiedDate = NOW(),
					UnsafeOperationType = '" . mysqli_real_escape_string($this->connectionlink, $data['OperationType']) . "'
				WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";

				$updateResult = mysqli_query($this->connectionlink, $updateQuery);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = 'Error saving unsafe TPVR data: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				// Update truck booking status to Failed and close the booking
				$truckUpdateQuery = "UPDATE Truck
									SET Status = 'Failed',										
										CompletedDate = NOW(),
										CompletedBy = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['UserId']) . "'
									WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";

				$truckUpdateResult = mysqli_query($this->connectionlink, $truckUpdateQuery);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = 'Error updating truck booking status: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				// Unlock the parking location
				$unlockResult = $this->UnlockParkingLocation($data['TruckID']);
				if (!$unlockResult['success']) {
					$json['Success'] = false;
					$json['Result'] = $unlockResult['error'];
					return json_encode($json);
				}

				// Log tracking information
				$trackingAction = 'Unsafe Condition Reported | TDR Manager: ' . $data['TDRManager'] . ' (' . $manager['FirstName'] . ' ' . $manager['LastName'] . ') | C3PO Entry: ' . $data['C3POEntryID'] . ' | Operation: ' . $data['OperationType'] . ' | Booking Status: Failed';
				$this->logTruckTracking($data['TruckID'], $trackingAction, 'Trailer Dock/Release', null, null, $data['TDRManager']);

				$json['Success'] = true;
				$json['Result'] = 'Unsafe TPVR verified successfully';
				$json['ManagerName'] = $manager['FirstName'] . ' ' . $manager['LastName'];
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid TDR Manager credentials or insufficient permissions';
			}

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetTruckTrackingHistory($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No tracking history found'
		);
		try {
			$query = "SELECT tt.Action, tt.CreatedDate, tt.ModuleName,
					  CONCAT(u.FirstName, ' ', u.LastName) as CreatedBy
					  FROM truck_tracking tt
					  LEFT JOIN users u ON tt.CreatedBy = u.UserId
					  WHERE tt.TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'
					  ORDER BY tt.CreatedDate DESC";

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$trackingHistory = array();
			while ($row = mysqli_fetch_assoc($q)) {
				$trackingHistory[] = $row;
			}

			$json['Success'] = true;
			$json['Result'] = $trackingHistory;

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function ValidateSealID($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'Invalid Seal ID'
		);
		try {
			// Check if the Seal ID matches the one stored in the truck booking
			$query = "SELECT SealID FROM Truck WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($q) > 0) {
				$truck = mysqli_fetch_assoc($q);
				$storedSealID = trim($truck['SealID']);
				$enteredSealID = trim($data['SealID']);

				// If no seal ID is stored in the truck record, validation passes
				if (empty($storedSealID)) {
					$json['Success'] = true;
					$json['Result'] = 'No Seal ID validation required';
				}
				// If seal IDs match (case-insensitive), validation passes
				else if (strcasecmp($storedSealID, $enteredSealID) === 0) {
					$json['Success'] = true;
					$json['Result'] = 'Seal ID validated successfully';
				}
				// If seal IDs don't match, validation fails
				else {
					$json['Success'] = false;
					$json['Result'] = 'Seal ID does not match the booking record. Expected: ' . $storedSealID;
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Truck booking not found';
			}

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	private function logTruckTracking($truckID, $action, $moduleName, $tdrSpotterLogin = null, $tdrLeadLogin = null, $tdrManagerLogin = null)
	{
		try {
			// Build the column list and values based on available data
			$columns = "(TruckID, Action, CreatedDate, CreatedBy, ModuleName";
			$values = "('" . mysqli_real_escape_string($this->connectionlink, $truckID) . "',
					   '" . mysqli_real_escape_string($this->connectionlink, $action) . "',
					   NOW(),
					   '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['UserId']) . "',
					   '" . mysqli_real_escape_string($this->connectionlink, $moduleName) . "'";

			// Add TDRSpotterLogin if provided
			if (!empty($tdrSpotterLogin)) {
				$columns .= ", TDRSpotterLogin";
				$values .= ", '" . mysqli_real_escape_string($this->connectionlink, $tdrSpotterLogin) . "'";
			}

			// Add TDRLeadLogin if provided
			if (!empty($tdrLeadLogin)) {
				$columns .= ", TDRLeadLogin";
				$values .= ", '" . mysqli_real_escape_string($this->connectionlink, $tdrLeadLogin) . "'";
			}

			// Add TDRManagerLogin if provided
			if (!empty($tdrManagerLogin)) {
				$columns .= ", TDRManagerLogin";
				$values .= ", '" . mysqli_real_escape_string($this->connectionlink, $tdrManagerLogin) . "'";
			}

			$columns .= ")";
			$values .= ")";

			$trackingQuery = "INSERT INTO truck_tracking $columns VALUES $values";

			mysqli_query($this->connectionlink, $trackingQuery);
			// Don't throw error if tracking fails - it's not critical
			if (mysqli_error($this->connectionlink)) {
				error_log("Truck tracking log error: " . mysqli_error($this->connectionlink));
			}
		} catch (Exception $e) {
			error_log("Truck tracking log exception: " . $e->getMessage());
		}
	}

	public function CreateUnbookedBooking($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => $data
		);

		// Debug: Log received data
		error_log("CreateUnbookedBooking received data: " . print_r($data, true));

		// Validate that either TruckReg or TrailerNumber is provided
		$truckReg = trim($data['TruckReg'] ?? '');
		$trailerNumber = trim($data['TrailerNumber'] ?? '');

		if (empty($truckReg) && empty($trailerNumber)) {
			$json['Success'] = false;
			$json['Result'] = 'Either Truck Registration or Trailer Number is required.';
			return json_encode($json);
		}

		// Validate required fields for Truck table
		$requiredFields = ['FacilityID', 'ParkTypeID', 'ParkingLocationID', 'CarrierID', 'TruckTypeID'];
		foreach ($requiredFields as $field) {
			if (empty($data[$field])) {
				error_log("CreateUnbookedBooking validation failed for field: $field, value: " . ($data[$field] ?? 'NULL'));
				$json['Success'] = false;
				$json['Result'] = ucfirst(str_replace('ID', '', $field)) . ' is required.';
				return json_encode($json);
			}
		}

		// Validate DockLockEngaged separately (for truck_docking_release table)
		if (empty($data['DockLockEngaged'])) {
			error_log("CreateUnbookedBooking validation failed for DockLockEngaged: " . ($data['DockLockEngaged'] ?? 'NULL'));
			$json['Success'] = false;
			$json['Result'] = 'Dock Lock Engaged status is required.';
			return json_encode($json);
		}

		// Check if parking location is available (not locked) before processing
		$availabilityCheck = $this->CheckParkingLocationAvailability($data['ParkingLocationID']);
		if (!$availabilityCheck['available']) {
			$json['Success'] = false;
			if (isset($availabilityCheck['error'])) {
				$json['Result'] = $availabilityCheck['error'];
			} else {
				$json['Result'] = 'Parking Location is currently locked by Load Number: ' . $availabilityCheck['lockedBy'] . '. Please choose a different location.';
			}
			return json_encode($json);
		}

		// Set default values for unbooked vehicles
		$arrivalType = 'Walk-in'; // Unbooked arrival
		$status = 'In Progress'; // Set to In Progress since they're being processed immediately
		$loadQuantity = !empty($data['LoadQuantity']) ? $data['LoadQuantity'] : 1;

		// Create the booking record
		$query = "INSERT INTO Truck (
			FacilityID, ArrivalType, ParkTypeID, ParkingLocationID, CarrierID,
			ArrivalDate, ArrivalTime, LoadType, LoadNumber, LoadQuantity,
			TruckTypeID, TruckReg, TrailerNumber, DriverName, DriverID,
			SealID, Notes, Status, CreatedDate, CreatedBy
		) VALUES (
			'" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $arrivalType) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeID']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['CarrierID']) . "',
			NULL,
			NULL,
			'" . mysqli_real_escape_string($this->connectionlink, $data['LoadType'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['LoadNumber'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $loadQuantity) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $truckReg) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $trailerNumber) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['DriverName']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['DriverID'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['SealID'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['Notes'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $status) . "',
			NOW(),
			'" . $_SESSION['user']['UserId'] . "'
		)";

		$result = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		$insert_id = mysqli_insert_id($this->connectionlink);

		// Create the truck_docking_release record for processing with additional booking information
		$processQuery = "INSERT INTO truck_docking_release (
			TruckID, ParkingLocationID, CarrierID, TruckTypeID, CheckinDate, CheckinTime, DockLockEngaged, SealID,
			LoadQuantity, DriverName, DriverID, ShipmentTicketID, ClassificationType, WasteCollectionPermit,
			CreatedDate, CreatedBy
		) VALUES (
			'" . $insert_id . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['CarrierID']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['CheckinDate']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['CheckinTime']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['DockLockEngaged']) . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['SealID'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['LoadQuantity'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['DriverName'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['DriverID'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['ShipmentTicketID'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['ClassificationType'] ?? '') . "',
			'" . mysqli_real_escape_string($this->connectionlink, $data['WasteCollectionPermit'] ?? '') . "',
			NOW(),
			'" . $_SESSION['user']['UserId'] . "'
		)";

		$processResult = mysqli_query($this->connectionlink, $processQuery);
		if (mysqli_error($this->connectionlink)) {
			// If processing record creation fails, we should still continue
			error_log("Failed to create truck_docking_release record: " . mysqli_error($this->connectionlink));
		}

		$processedID = mysqli_insert_id($this->connectionlink);

		// Update Truck table with processed details (same as ProcessTruckBooking)
		$updateQuery = "UPDATE Truck SET
						Processed = 1,
						ProcessedTime = NOW(),
						ProcessedBy = '" . $_SESSION['user']['UserId'] . "'
						WHERE TruckID = '" . $insert_id . "'";

		$updateResult = mysqli_query($this->connectionlink, $updateQuery);
		if (mysqli_error($this->connectionlink)) {
			error_log("Failed to update Truck processed status: " . mysqli_error($this->connectionlink));
		}

		// Lock the parking location with the load number
		$loadNumber = $data['LoadNumber'] ?? '';
		if (!empty($loadNumber)) {
			$lockQuery = "UPDATE ParkingLocation SET
						  LockedLoadNumber = '" . mysqli_real_escape_string($this->connectionlink, $loadNumber) . "'
						  WHERE ParkingLocationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "'";
			$lockResult = mysqli_query($this->connectionlink, $lockQuery);
			if (mysqli_error($this->connectionlink)) {
				error_log("Failed to lock parking location: " . mysqli_error($this->connectionlink));
			}
		}

		// Log the unbooked vehicle creation and processing in tracking
		$vehicleInfo = !empty($truckReg) ? 'Truck: ' . $truckReg : 'Trailer: ' . $trailerNumber;
		$trackingAction = 'Unbooked Vehicle Processed | ' . $vehicleInfo .
						  ' | Driver: ' . $data['DriverName'] .
						  ' | Status: Vehicle arrived without prior booking and set to "In Progress" for immediate processing' .
						  ' | Check-in: ' . $data['CheckinDate'] . ' ' . $data['CheckinTime'];
		$this->logTruckTracking($insert_id, $trackingAction, 'Trailer Dock/Release');

		// Get the created booking details to return
		$detailsQuery = "SELECT T.*, F.FacilityName, PL.ParkingLocationName, C.CarrierName, TT.TruckTypeName as VehicleTypeName
						 FROM Truck T
						 LEFT JOIN Facility F ON T.FacilityID = F.FacilityID
						 LEFT JOIN ParkingLocation PL ON T.ParkingLocationID = PL.ParkingLocationID
						 LEFT JOIN Carrier C ON T.CarrierID = C.CarrierID
						 LEFT JOIN TruckType TT ON T.TruckTypeID = TT.TruckTypeID
						 WHERE T.TruckID = '" . $insert_id . "'";

		$detailsResult = mysqli_query($this->connectionlink, $detailsQuery);
		if ($detailsResult && mysqli_num_rows($detailsResult) > 0) {
			$bookingDetails = mysqli_fetch_assoc($detailsResult);
			$bookingDetails['LoadQuantity'] = (int)$bookingDetails['LoadQuantity'];
			$bookingDetails['BookingProcessed'] = true; // Mark as processed since we created the processing record
			$bookingDetails['Processed'] = 1; // Mark as processed in Truck table
			$bookingDetails['CheckinDate'] = $data['CheckinDate'];
			$bookingDetails['CheckinTime'] = $data['CheckinTime'];
			$bookingDetails['ProcessedID'] = $processedID; // Include the truck_docking_release ID

			$json['Success'] = true;
			$json['Result'] = $bookingDetails;
			$json['TruckID'] = $insert_id;
			$json['ProcessedID'] = $processedID;
		} else {
			// Create a basic booking object even if details query fails
			$basicBooking = array(
				'TruckID' => $insert_id,
				'TruckReg' => $data['TruckReg'] ?? '',
				'TrailerNumber' => $data['TrailerNumber'] ?? '',
				'DriverName' => $data['DriverName'],
				'DriverID' => $data['DriverID'] ?? '',
				'FacilityID' => $data['FacilityID'],
				'ParkTypeID' => $data['ParkTypeID'],
				'ParkingLocationID' => $data['ParkingLocationID'],
				'CarrierID' => $data['CarrierID'],
				'TruckTypeID' => $data['TruckTypeID'],
				'LoadType' => $data['LoadType'] ?? '',
				'LoadNumber' => $data['LoadNumber'] ?? '',
				'LoadQuantity' => (int)($data['LoadQuantity'] ?? 1),
				'Notes' => $data['Notes'] ?? '',
				'Status' => 'Arrived',
				'BookingProcessed' => true,
				'Processed' => 1,
				'CheckinDate' => $data['CheckinDate'],
				'CheckinTime' => $data['CheckinTime'],
				'ProcessedID' => $processedID,
				'DockLockEngaged' => $data['DockLockEngaged']
			);

			$json['Success'] = true;
			$json['Result'] = $basicBooking;
			$json['TruckID'] = $insert_id;
			$json['ProcessedID'] = $processedID;
		}

		return json_encode($json);
	}



	public function GetVehicleTypes($data)
	{
		$json = array('Success' => false, 'Result' => array());

		$query = "SELECT TruckTypeID, TruckTypeName FROM TruckType WHERE Status = 'Active' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "' ORDER BY TruckTypeName";
		$result = mysqli_query($this->connectionlink, $query);

		if (mysqli_error($this->connectionlink)) {
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		$vehicleTypes = array();
		while ($row = mysqli_fetch_assoc($result)) {
			$vehicleTypes[] = $row;
		}

		$json['Success'] = true;
		$json['Result'] = $vehicleTypes;
		return json_encode($json);
	}

	public function GetParkingLocationsByType($data)
	{
		$json = array('Success' => false, 'Result' => array());

		if (empty($data['ParkTypeID'])) {
			$json['Result'] = 'Park Type ID is required';
			return json_encode($json);
		}

		$query = "SELECT ParkingLocationID, ParkingLocationName
				  FROM ParkingLocation
				  WHERE ParkTypeID = '" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeID']) . "'
				  AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'
				  ORDER BY ParkingLocationName";
		$result = mysqli_query($this->connectionlink, $query);

		if (mysqli_error($this->connectionlink)) {
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		$locations = array();
		while ($row = mysqli_fetch_assoc($result)) {
			$locations[] = $row;
		}

		$json['Success'] = true;
		$json['Result'] = $locations;
		return json_encode($json);
	}

	// Truck Images Management Functions
	public function UploadTruckImage($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			if (!isset($data['TruckID']) || empty($data['TruckID'])) {
				$json['Success'] = false;
				$json['Result'] = 'TruckID is required';
				return json_encode($json);
			}

			if (!isset($data['file']) || empty($data['file']['tmp_name'])) {
				$json['Success'] = false;
				$json['Result'] = 'No file uploaded';
				return json_encode($json);
			}

			// Generate unique filename
			$filename = 'truck_images/' . $data['TruckID'] . '/' . time() . '_' . $data['file']['name'];

			// Upload to S3 using inherited function
			$upload = $this->UploadToS3($filename, $data['file']['tmp_name']);
			if ($upload !== true) {
				$json['Success'] = false;
				$json['Result'] = 'Unable to upload file to S3: ' . $upload;
				return json_encode($json);
			}

			// Save to database
			$query = "INSERT INTO TruckImages (TruckID, ImagePath, CreatedDate, CreatedBy)
					  VALUES ('" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "',
							  '" . mysqli_real_escape_string($this->connectionlink, $filename) . "',
							  NOW(),
							  '" . $_SESSION['user']['UserId'] . "')";

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'File uploaded successfully';
			$json['ImageID'] = mysqli_insert_id($this->connectionlink);
			return json_encode($json);

		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function GetTruckImages($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			if (!isset($data['TruckID']) || empty($data['TruckID'])) {
				$json['Success'] = false;
				$json['Result'] = 'TruckID is required';
				return json_encode($json);
			}

			$query = "SELECT ImageID, TruckID, ImagePath, CreatedDate, CreatedBy
					  FROM TruckImages
					  WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'
					  ORDER BY CreatedDate DESC";

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$images = array();
			while ($row = mysqli_fetch_assoc($q)) {
				$images[] = $row;
			}

			$json['Success'] = true;
			$json['Result'] = $images;
			return json_encode($json);

		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function DeleteTruckImage($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			if (!isset($data['ImageID']) || empty($data['ImageID'])) {
				$json['Success'] = false;
				$json['Result'] = 'ImageID is required';
				return json_encode($json);
			}

			// Get image path before deleting
			$query = "SELECT ImagePath FROM TruckImages WHERE ImageID = '" . mysqli_real_escape_string($this->connectionlink, $data['ImageID']) . "'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_affected_rows($this->connectionlink) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'Image not found';
				return json_encode($json);
			}

			$row = mysqli_fetch_assoc($q);
			$imagePath = $row['ImagePath'];

			// Delete from database
			$deleteQuery = "DELETE FROM TruckImages WHERE ImageID = '" . mysqli_real_escape_string($this->connectionlink, $data['ImageID']) . "'";
			$deleteQ = mysqli_query($this->connectionlink, $deleteQuery);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Note: We're not deleting from S3 to maintain data integrity
			// The file will remain in S3 but won't be accessible through the application

			$json['Success'] = true;
			$json['Result'] = 'Image deleted successfully';
			return json_encode($json);

		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	// Truck HyperLink Management Functions
	public function GetTruckHyperLink($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			if (!isset($data['TruckID']) || empty($data['TruckID'])) {
				$json['Success'] = false;
				$json['Result'] = 'TruckID is required';
				return json_encode($json);
			}

			$query = "SELECT HyperLink FROM Truck WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				$json['Success'] = true;
				$json['Result'] = $row['HyperLink'];
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Truck not found';
			}
			return json_encode($json);

		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function SaveTruckHyperLink($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			if (!isset($data['TruckID']) || empty($data['TruckID'])) {
				$json['Success'] = false;
				$json['Result'] = 'TruckID is required';
				return json_encode($json);
			}

			if (!isset($data['HyperLink']) || empty($data['HyperLink'])) {
				$json['Success'] = false;
				$json['Result'] = 'HyperLink is required';
				return json_encode($json);
			}

			// Validate URL format
			if (!filter_var($data['HyperLink'], FILTER_VALIDATE_URL)) {
				$json['Success'] = false;
				$json['Result'] = 'Invalid URL format';
				return json_encode($json);
			}

			$query = "UPDATE Truck SET HyperLink = '" . mysqli_real_escape_string($this->connectionlink, $data['HyperLink']) . "',
					  UpdatedDate = NOW(), UpdatedBy = '" . $_SESSION['user']['UserId'] . "'
					  WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$json['Success'] = true;
				$json['Result'] = 'Hyperlink saved successfully';
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No changes made or truck not found';
			}
			return json_encode($json);

		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function DeleteTruckHyperLink($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			if (!isset($data['TruckID']) || empty($data['TruckID'])) {
				$json['Success'] = false;
				$json['Result'] = 'TruckID is required';
				return json_encode($json);
			}

			$query = "UPDATE Truck SET HyperLink = NULL, UpdatedDate = NOW(), UpdatedBy = '" . $_SESSION['user']['UserId'] . "'
					  WHERE TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Hyperlink deleted successfully';
			return json_encode($json);

		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	/**
	 * Validate if time is in 30-minute intervals
	 * @param string $timeValue - Time value to validate (e.g., "09:30", "10:00", "09:30:00", "10:00:00", "1970-01-01 12:00:00")
	 * @return bool - True if valid 30-minute interval, false otherwise
	 */
	private function isValidThirtyMinuteInterval($timeValue) {
		// Remove any extra whitespace
		$timeValue = trim($timeValue);

		// Handle Excel datetime format (e.g., "1970-01-01 12:00:00")
		// Extract just the time portion if it's a full datetime
		if(preg_match('/^\d{4}-\d{2}-\d{2}\s+(.+)$/', $timeValue, $datetimeMatches)) {
			$timeValue = trim($datetimeMatches[1]);
		}

		// Check if time format is valid (H:MM, HH:MM, H:MM:SS, or HH:MM:SS)
		// Excel often converts times to include seconds (e.g., 11:00 becomes 11:00:00)
		if(!preg_match('/^([0-9]|[01][0-9]|2[0-3]):([0-5][0-9])(?::([0-5][0-9]))?$/', $timeValue, $matches)) {
			return false;
		}

		$hour = intval($matches[1]);
		$minute = intval($matches[2]);
		$second = isset($matches[3]) ? intval($matches[3]) : 0;

		// Check if minutes are 00 or 30
		if($minute !== 0 && $minute !== 30) {
			return false;
		}

		// If seconds are provided, they must be 00 for valid 30-minute intervals
		if($second !== 0) {
			return false;
		}

		// Ensure hour is valid (0-23)
		if($hour < 0 || $hour > 23) {
			return false;
		}

		return true;
	}

	/**
	 * Convert Excel time formats to proper time format for database storage
	 * @param string $timeValue - Time value (e.g., "09:30", "09:30:00", "1970-01-01 12:00:00")
	 * @return string - Formatted time (HH:MM:SS) for database storage
	 */
	private function convertExcelTimeToDBFormat($timeValue) {
		$timeValue = trim($timeValue);

		// Handle Excel datetime format (e.g., "1970-01-01 12:00:00")
		// Extract just the time portion if it's a full datetime
		if(preg_match('/^\d{4}-\d{2}-\d{2}\s+(.+)$/', $timeValue, $datetimeMatches)) {
			$timeValue = trim($datetimeMatches[1]);
		}

		// Extract hour and minute from time format
		if(preg_match('/^([0-9]|[01][0-9]|2[0-3]):([0-5][0-9])(?::([0-5][0-9]))?$/', $timeValue, $matches)) {
			$hour = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
			$minute = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
			$second = isset($matches[3]) ? str_pad($matches[3], 2, '0', STR_PAD_LEFT) : '00';
			return $hour . ':' . $minute . ':' . $second;
		}

		// Return original if no match (fallback)
		return $timeValue;
	}

	/**
	 * Convert Excel date formats to MySQL date format (YYYY-MM-DD)
	 * Handles Excel serial numbers, MM/DD/YYYY, DD/MM/YYYY, and YYYY-MM-DD formats
	 * @param mixed $dateValue - The date value from Excel
	 * @return string|false - MySQL formatted date (YYYY-MM-DD) or false if invalid
	 */
	private function convertExcelDateToMySQLDate($dateValue) {
		// Remove any extra whitespace
		$dateValue = trim($dateValue);

		// If empty, return false
		if(empty($dateValue)) {
			return false;
		}

		// Check if it's already in MySQL format (YYYY-MM-DD)
		if(preg_match('/^\d{4}-\d{2}-\d{2}$/', $dateValue)) {
			$date = DateTime::createFromFormat('Y-m-d', $dateValue);
			if($date && $date->format('Y-m-d') === $dateValue) {
				return $dateValue;
			}
		}

		// Check if it's an Excel serial number (numeric value)
		if(is_numeric($dateValue)) {
			try {
				// Excel serial date conversion
				// Excel epoch starts from 1900-01-01, but Excel incorrectly treats 1900 as a leap year
				$excelEpoch = new DateTime('1900-01-01');
				$days = intval($dateValue) - 2; // Subtract 2 to account for Excel's leap year bug
				$excelEpoch->add(new DateInterval('P' . $days . 'D'));
				return $excelEpoch->format('Y-m-d');
			} catch (Exception $e) {
				// Fall through to other format checks
			}
		}

		// Try common date formats
		$formats = [
			'n/j/Y',    // M/D/YYYY (1/5/2024)
			'm/d/Y',    // MM/DD/YYYY (01/05/2024)
			'j/n/Y',    // D/M/YYYY (5/1/2024)
			'd/m/Y',    // DD/MM/YYYY (05/01/2024)
			'n-j-Y',    // M-D-YYYY (1-5-2024)
			'm-d-Y',    // MM-DD-YYYY (01-05-2024)
			'j-n-Y',    // D-M-YYYY (5-1-2024)
			'd-m-Y',    // DD-MM-YYYY (05-01-2024)
			'Y/m/d',    // YYYY/MM/DD
			'Y/n/j',    // YYYY/M/D
			'M j, Y',   // Jan 5, 2024
			'F j, Y',   // January 5, 2024
			'd-M-Y',    // 05-Jan-2024
			'j-M-Y',    // 5-Jan-2024
		];

		foreach($formats as $format) {
			$date = DateTime::createFromFormat($format, $dateValue);
			if($date && $date->format($format) === $dateValue) {
				return $date->format('Y-m-d');
			}
		}

		// Try strtotime as last resort
		$timestamp = strtotime($dateValue);
		if($timestamp !== false) {
			return date('Y-m-d', $timestamp);
		}

		// If all else fails, return false
		return false;
	}

	public function UpdateLoadQuantity($data)
	{
		$json = array();

		// Validate required parameters
		if (empty($data['TruckID'])) {
			$json['Success'] = false;
			$json['Result'] = 'Truck ID is required';
			return json_encode($json);
		}

		if (!isset($data['LoadQuantity']) || $data['LoadQuantity'] === '') {
			$json['Success'] = false;
			$json['Result'] = 'Load Quantity is required';
			return json_encode($json);
		}

		$loadQuantity = (int)$data['LoadQuantity'];
		if ($loadQuantity < 1) {
			$json['Success'] = false;
			$json['Result'] = 'Load Quantity must be at least 1';
			return json_encode($json);
		}

		$truckID = mysqli_real_escape_string($this->connectionlink, $data['TruckID']);
		$processedID = isset($data['ProcessedID']) ? mysqli_real_escape_string($this->connectionlink, $data['ProcessedID']) : '';

		// Check if truck exists and get current status
		$checkQuery = "SELECT TruckID, Status, LoadQuantity FROM Truck WHERE TruckID = '$truckID'";
		$checkResult = mysqli_query($this->connectionlink, $checkQuery);

		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		if (mysqli_num_rows($checkResult) == 0) {
			$json['Success'] = false;
			$json['Result'] = 'Truck not found';
			return json_encode($json);
		}

		$truck = mysqli_fetch_assoc($checkResult);
		$currentStatus = strtolower($truck['Status']);
		$oldLoadQuantity = $truck['LoadQuantity'];

		// Only allow updates when status is "Arrived" or "In Progress"
		if ($currentStatus !== 'arrived' && $currentStatus !== 'in progress') {
			$json['Success'] = false;
			$json['Result'] = 'Load quantity can only be updated when booking status is "Arrived" or "In Progress". Current status: ' . $truck['Status'];
			return json_encode($json);
		}

		// Update the Truck table
		$updateTruckQuery = "UPDATE Truck
							 SET LoadQuantity = '$loadQuantity',
								 UpdatedDate = NOW(),
								 UpdatedBy = '" . $_SESSION['user']['UserId'] . "'
							 WHERE TruckID = '$truckID'";

		$updateTruckResult = mysqli_query($this->connectionlink, $updateTruckQuery);

		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = 'Error updating truck: ' . mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		// Update truck_docking_release table if ProcessedID is provided
		if (!empty($processedID)) {
			$updateTDRQuery = "UPDATE truck_docking_release
							   SET LoadQuantity = '$loadQuantity'
							   WHERE ID = '$processedID'";

			$updateTDRResult = mysqli_query($this->connectionlink, $updateTDRQuery);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = 'Error updating truck docking release: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}
		}

		// Log the change in truck tracking
		$trackingAction = 'Load Quantity Updated | Old Value: ' . $oldLoadQuantity . ' | New Value: ' . $loadQuantity;
		$this->logTruckTracking($truckID, $trackingAction, 'Load Quantity Update');

		$json['Success'] = true;
		$json['Result'] = 'Load quantity updated successfully';
		$json['OldValue'] = $oldLoadQuantity;
		$json['NewValue'] = $loadQuantity;

		return json_encode($json);
	}

	public function UpdateTruckReg($data)
	{
		$json = array();

		// Validate required parameters
		if (empty($data['TruckID'])) {
			$json['Success'] = false;
			$json['Result'] = 'Truck ID is required';
			return json_encode($json);
		}

		if (empty($data['TruckReg'])) {
			$json['Success'] = false;
			$json['Result'] = 'Truck Registration is required';
			return json_encode($json);
		}

		$truckID = mysqli_real_escape_string($this->connectionlink, $data['TruckID']);
		$truckReg = mysqli_real_escape_string($this->connectionlink, $data['TruckReg']);
		$processedID = isset($data['ProcessedID']) ? mysqli_real_escape_string($this->connectionlink, $data['ProcessedID']) : '';

		// Get current truck data
		$query = "SELECT TruckReg, Status FROM Truck WHERE TruckID = '$truckID'";
		$result = mysqli_query($this->connectionlink, $query);

		if (!$result || mysqli_num_rows($result) == 0) {
			$json['Success'] = false;
			$json['Result'] = 'Truck not found';
			return json_encode($json);
		}

		$truck = mysqli_fetch_assoc($result);
		$currentStatus = strtolower($truck['Status']);

		// Only allow updates when status is "Arrived" or "In Progress"
		if ($currentStatus !== 'arrived' && $currentStatus !== 'in progress') {
			$json['Success'] = false;
			$json['Result'] = 'Truck registration can only be updated when booking status is "Arrived" or "In Progress". Current status: ' . $truck['Status'];
			return json_encode($json);
		}

		// Update Truck table
		$updateQuery = "UPDATE Truck SET TruckReg = '$truckReg', UpdatedDate = NOW(), UpdatedBy = '" . $_SESSION['user']['UserId'] . "' WHERE TruckID = '$truckID'";
		$updateResult = mysqli_query($this->connectionlink, $updateQuery);

		if (!$updateResult) {
			$json['Success'] = false;
			$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		// Log the change in truck tracking
		$trackingAction = 'Truck Registration Updated | Old Value: ' . $truck['TruckReg'] . ' | New Value: ' . $truckReg;
		$this->logTruckTracking($truckID, $trackingAction, 'Truck Registration Update');

		$json['Success'] = true;
		$json['Result'] = 'Truck registration updated successfully';
		$json['OldValue'] = $truck['TruckReg'];
		$json['NewValue'] = $truckReg;

		return json_encode($json);
	}

	public function UpdateTrailerNumber($data)
	{
		$json = array();

		// Validate required parameters
		if (empty($data['TruckID'])) {
			$json['Success'] = false;
			$json['Result'] = 'Truck ID is required';
			return json_encode($json);
		}

		if (empty($data['TrailerNumber'])) {
			$json['Success'] = false;
			$json['Result'] = 'Trailer Number is required';
			return json_encode($json);
		}

		$truckID = mysqli_real_escape_string($this->connectionlink, $data['TruckID']);
		$trailerNumber = mysqli_real_escape_string($this->connectionlink, $data['TrailerNumber']);
		$processedID = isset($data['ProcessedID']) ? mysqli_real_escape_string($this->connectionlink, $data['ProcessedID']) : '';

		// Get current truck data
		$query = "SELECT TrailerNumber, Status FROM Truck WHERE TruckID = '$truckID'";
		$result = mysqli_query($this->connectionlink, $query);

		if (!$result || mysqli_num_rows($result) == 0) {
			$json['Success'] = false;
			$json['Result'] = 'Truck not found';
			return json_encode($json);
		}

		$truck = mysqli_fetch_assoc($result);
		$currentStatus = strtolower($truck['Status']);

		// Only allow updates when status is "Arrived" or "In Progress"
		if ($currentStatus !== 'arrived' && $currentStatus !== 'in progress') {
			$json['Success'] = false;
			$json['Result'] = 'Trailer number can only be updated when booking status is "Arrived" or "In Progress". Current status: ' . $truck['Status'];
			return json_encode($json);
		}

		// Update Truck table
		$updateQuery = "UPDATE Truck SET TrailerNumber = '$trailerNumber', UpdatedDate = NOW(), UpdatedBy = '" . $_SESSION['user']['UserId'] . "' WHERE TruckID = '$truckID'";
		$updateResult = mysqli_query($this->connectionlink, $updateQuery);

		if (!$updateResult) {
			$json['Success'] = false;
			$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		// Log the change in truck tracking
		$trackingAction = 'Trailer Number Updated | Old Value: ' . $truck['TrailerNumber'] . ' | New Value: ' . $trailerNumber;
		$this->logTruckTracking($truckID, $trackingAction, 'Trailer Number Update');

		$json['Success'] = true;
		$json['Result'] = 'Trailer number updated successfully';
		$json['OldValue'] = $truck['TrailerNumber'];
		$json['NewValue'] = $trailerNumber;

		return json_encode($json);
	}

	public function UpdateLoadNumber($data)
	{
		$json = array();

		// Validate required parameters
		if (empty($data['TruckID'])) {
			$json['Success'] = false;
			$json['Result'] = 'Truck ID is required';
			return json_encode($json);
		}

		if (empty($data['LoadNumber'])) {
			$json['Success'] = false;
			$json['Result'] = 'Load Number is required';
			return json_encode($json);
		}

		$truckID = mysqli_real_escape_string($this->connectionlink, $data['TruckID']);
		$loadNumber = mysqli_real_escape_string($this->connectionlink, $data['LoadNumber']);
		$processedID = isset($data['ProcessedID']) ? mysqli_real_escape_string($this->connectionlink, $data['ProcessedID']) : '';

		// Get current truck data
		$query = "SELECT LoadNumber, Status FROM Truck WHERE TruckID = '$truckID'";
		$result = mysqli_query($this->connectionlink, $query);

		if (!$result || mysqli_num_rows($result) == 0) {
			$json['Success'] = false;
			$json['Result'] = 'Truck not found';
			return json_encode($json);
		}

		$truck = mysqli_fetch_assoc($result);
		$currentStatus = strtolower($truck['Status']);

		// Only allow updates when status is "Arrived" or "In Progress"
		if ($currentStatus !== 'arrived' && $currentStatus !== 'in progress') {
			$json['Success'] = false;
			$json['Result'] = 'Load number can only be updated when booking status is "Arrived" or "In Progress". Current status: ' . $truck['Status'];
			return json_encode($json);
		}

		// Update Truck table
		$updateQuery = "UPDATE Truck SET LoadNumber = '$loadNumber', UpdatedDate = NOW(), UpdatedBy = '" . $_SESSION['user']['UserId'] . "' WHERE TruckID = '$truckID'";
		$updateResult = mysqli_query($this->connectionlink, $updateQuery);

		if (!$updateResult) {
			$json['Success'] = false;
			$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		// Log the change in truck tracking
		$trackingAction = 'Load Number Updated | Old Value: ' . $truck['LoadNumber'] . ' | New Value: ' . $loadNumber;
		$this->logTruckTracking($truckID, $trackingAction, 'Load Number Update');

		$json['Success'] = true;
		$json['Result'] = 'Load number updated successfully';
		$json['OldValue'] = $truck['LoadNumber'];
		$json['NewValue'] = $loadNumber;

		return json_encode($json);
	}

}

?>