<?php
session_start();
$id = $_GET['id'];
// Include the main TCPDF library (search for installation path).
require_once('tcpdf_include.php');
// create new PDF document
$pdf = new TCPDF('P', 'mm', array('70','100'), true, 'UTF-8', false);;
//$pdf = new TCPDF('P', 'mm', array('60','127'), true, 'UTF-8', false);
//$pdf = new TCPDF('P', 'mm', array('101.6','76.2'), true, 'UTF-8', false);
//$pdf = new TCPDF('P', 'mm', array('101.6','152.4'), true, 'UTF-8', false);
// set document information
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Eviridis');
$pdf->SetTitle('Shipment Serial Label');
$pdf->SetSubject('Shipment');
$pdf->SetKeywords('TCPDF, PDF, example, test, guide');
// remove default header/footer
$pdf->setPrintHeader(false);
$pdf->setPrintFooter(false);
// set default monospaced font
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
// set margins
$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
$pdf->setTopMargin(0);
$pdf->SetRightMargin(0);
$pdf->setHeaderMargin(0);
$pdf->SetFooterMargin(0);
// set auto page breaks
$pdf->SetAutoPageBreak(TRUE, 0);
// set image scale factor
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
// set some language-dependent strings (optional)
if (@file_exists(dirname(__FILE__).'/lang/eng.php')) {
    require_once(dirname(__FILE__).'/lang/eng.php');
    $pdf->setLanguageArray($l);
}
// ---------------------------------------------------------
// set font
//$pdf->SetFont('helvetica', '', 11);
//$pdf->SetFont("helvetica", "B", 13);
$pdf->SetFont("helvetica", "B", 11);
include_once("../../../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
include_once("../../config.php");
$dateformat = DATEFORMAT;
$sql = "SELECT
    ci.CustomPalletItemID,
    ci.CustomPalletID,
    cp.BinName,
    COALESCE(a.SerialNumber, sr.ServerSerialNumber, mr.MediaSerialNumber) as SerialNumber,
    COALESCE(a.UniversalModelNumber, sr.MPN, mr.MediaMPN) as UniversalModelNumber,
    COALESCE(pt.parttype, bpt.parttype) as part_type,
    ci.Notes,
    ci.DateCreated as CreatedDate,
    CASE
        WHEN ci.AssetScanID IS NOT NULL THEN 'Asset'
        WHEN ci.ServerID IS NOT NULL THEN 'Server'
        WHEN ci.MediaID IS NOT NULL THEN 'Media'
        WHEN ci.byproduct_id IS NOT NULL THEN 'ByProduct'
        ELSE 'Unknown'
    END as ItemType,
    a.AssetScanID,
    sr.ServerID,
    mr.MediaID,
    bp.byproduct_id,
    c.COO,
    a.SerialNumber as AssetSerialNumber,
    sr.ServerSerialNumber as ServerSerialNumber,
    mr.MediaSerialNumber
FROM custompallet_items ci
INNER JOIN custompallet cp ON ci.CustomPalletID = cp.CustomPalletID
LEFT JOIN asset a ON ci.AssetScanID = a.AssetScanID
LEFT JOIN speed_server_recovery sr ON ci.ServerID = sr.ServerID
LEFT JOIN speed_media_recovery mr ON ci.MediaID = mr.MediaID
LEFT JOIN by_products bp ON ci.byproduct_id = bp.byproduct_id
LEFT JOIN parttype pt ON COALESCE(a.parttypeid, sr.parttypeid, mr.parttypeid) = pt.parttypeid
LEFT JOIN parttype bpt ON bp.part_type = bpt.parttypeid
LEFT JOIN COO c ON COALESCE(a.COOID, sr.COOID, mr.COOID) = c.COOID
WHERE ci.CustomPalletItemID = '".mysqli_real_escape_string($connectionlink,$id)."'";

$query = mysqli_query($connectionlink,$sql);
if(mysqli_error($connectionlink))
{
	echo mysqli_error($connectionlink);
}
$row = mysqli_fetch_assoc($query);

// Handle different item types for serial number display
$SerialNumber = '';
$UniversalModelNumber = '';

if($row['ItemType'] == 'Asset') {
    $SerialNumber = $row['AssetSerialNumber'];
    $UniversalModelNumber = $row['UniversalModelNumber'];
} else if($row['ItemType'] == 'Server') {
    $SerialNumber = $row['ServerSerialNumber'];
    $UniversalModelNumber = $row['UniversalModelNumber'];
} else if($row['ItemType'] == 'Media') {
    $SerialNumber = $row['MediaSerialNumber'];
    $UniversalModelNumber = $row['UniversalModelNumber'];
} else if($row['ItemType'] == 'ByProduct') {
    $SerialNumber = 'N/A (ByProduct)';
    $UniversalModelNumber = 'N/A (ByProduct)';
}

$SerialNumber_1 = "SerialNumber :".$SerialNumber;
$UniversalModelNumber_1 = "MPN :".$UniversalModelNumber;
$PartType_1 = "Part Type :".$row['part_type'];
$ItemType_1 = "Type :".$row['ItemType'];
$BinName_1 = "Bin :".$row['BinName'];


// $CustomID = $row['sanitization_custom_id'];
// $CustomID_1 = "Custom ID :".$CustomID;

// add a page
$pdf->AddPage();


		if(1) {
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,$SerialNumber_1,0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->write1DBarcode($SerialNumber, 'C128', '3', '', 54, 14, '', $style, 'N');
		}

    	if(1) {
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,$UniversalModelNumber_1,0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			if($row['ItemType'] != 'ByProduct') {
				$pdf->write1DBarcode($UniversalModelNumber, 'C128', '3', '', 54, 14, '', $style, 'N');
			}
		}

		// Add additional information
		if(1) {
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,$PartType_1,0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,$BinName_1,0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			// $pdf->setX(3);
			// $pdf->Cell(0 , 0,$ItemType_1,0,1,'L',0,0,true,'','T');
		}
		
// ---------------------------------------------------------
//Close and output PDF document
$pdf->Output('shipmentseriallabel.pdf', 'I');
//============================================================+
// END OF FILE
//============================================================+