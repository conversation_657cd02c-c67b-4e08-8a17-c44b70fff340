<?php
session_start();
include_once("../../config.php");
$data = $_SESSION['TDRSOPConfigurationxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "TDRSOPConfiguration.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('TDR SOP Configuration List');
$header = array('TDR Type','Vehicle Type','Step No','Description','Dock Lock Engaged','Spotter TPVR Required','Status');

$sql = "select t.*, tt.TruckTypeName from TDRSOP t
        LEFT JOIN TruckType tt ON t.TruckTypeID = tt.TruckTypeID";

$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
	$spotterTPVRText = ($row['SpotterTPVRRequired'] == 1) ? 'Yes' : 'No';
	$vehicleType = !empty($row['TruckTypeName']) ? $row['TruckTypeName'] : '';
	$row2  = array($row['TdrType'],$vehicleType,$row['StepNo'],$row['Description'],$row['DockLockEngaged'],$spotterTPVRText,$row['Status']);
	$rows[] = $row2;
}

$sheet_name = 'TDRSOPConfigurationList';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 