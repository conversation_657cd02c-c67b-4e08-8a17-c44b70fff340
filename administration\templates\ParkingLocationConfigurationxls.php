<?php
session_start();
include_once("../../config.php");
$data = $_SESSION['ParkingLocationConfigurationxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "ParkingLocationConfiguration.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Parking Location Configuration List');
$header = array('Facility','Parking Location Name','Description','Parking Type','Status');

$sql = "Select PL.*,F.FacilityName,PT.ParkTypeName from ParkingLocation PL left join facility F on F.FacilityID = PL.FacilityID left join ParkType PT on PT.ParkTypeID = PL.ParkTypeID Where 1 ";

// Apply filters if they exist
if(count($data[0]) > 0) {
    foreach ($data[0] as $key => $value) {
        if($value != '') {
            if ($key == 'ParkTypeName') {
                $sql = $sql . " AND PT.ParkTypeName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
            }
            if ($key == 'ParkingLocationName') {
                $sql = $sql . " AND PL.ParkingLocationName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
            }
            if ($key == 'Description') {
                $sql = $sql . " AND PL.Description like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
            }
            if ($key == 'FacilityName') {
                $sql = $sql . " AND F.FacilityName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
            }
            if ($key == 'Status') {
                $sql = $sql . " AND PL.Status like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
            }
        }
    }
}

// Apply sorting if specified
if($data['OrderBy'] != '') {
    if($data['OrderByType'] == 'asc') {
        $order_by_type = 'asc';
    } else {
        $order_by_type = 'desc';
    }

    if($data['OrderBy'] == 'FacilityName') {
        $sql = $sql . " order by F.FacilityName ".$order_by_type." ";
    } else if($data['OrderBy'] == 'ParkingLocationName') {
        $sql = $sql . " order by PL.ParkingLocationName ".$order_by_type." ";
    } else if($data['OrderBy'] == 'Description') {
        $sql = $sql . " order by PL.Description ".$order_by_type." ";
    } else if($data['OrderBy'] == 'ParkTypeName') {
        $sql = $sql . " order by PT.ParkTypeName ".$order_by_type." ";
    } else if($data['OrderBy'] == 'Status') {
        $sql = $sql . " order by PL.Status ".$order_by_type." ";
    }            
} else {
    $sql = $sql . " order by PL.ParkingLocationName asc ";
}

$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}

while($row = mysqli_fetch_assoc($query))
{
    $row2  = array($row['FacilityName'],$row['ParkingLocationName'],$row['Description'],$row['ParkTypeName'],$row['Status']);
    $rows[] = $row2;
}

$sheet_name = 'ParkingLocationConfigurationList';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?>
