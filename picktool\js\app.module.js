(function () {
    'use strict';

    var module = angular.module('app', [
        // Core modules
         'app.core'

        // Custom Feature modules
        ,'app.ui'
        ,'app.ui.form'
        ,'app.ui.form.validation'


        // 3rd party feature modules
        ,'md.data.table'
        ,'global'
        ,'angularFileUpload'
        ,'angularMoment'
    ]);

    module.config(['$stateProvider', '$urlRouterProvider', '$ocLazyLoadProvider',
        function($stateProvider, $urlRouterProvider, $ocLazyLoadProvider) {

            $stateProvider
                .state('PickConfiguration', {
                    url: '/PickConfiguration',
                    templateUrl: "templates/PickConfiguration.html"
                })
                .state('PickConfiguration/:ConfigurationID', {
                    url: '/PickConfiguration/:ConfigurationID',
                    templateUrl: "templates/PickConfiguration.html"
                })                
                .state('PickConfigurationDetailsList', {
                    url: '/PickConfigurationDetailsList',
                    templateUrl: "templates/PickConfigurationDetailsList.html"
                })
                .state('PickConfigurationDetailsListCompleted', {
                    url: '/PickConfigurationDetailsListCompleted',
                    templateUrl: "templates/PickConfigurationDetailsListCompleted.html"
                })
                .state('PickPath', {
                    url: '/PickPath',
                    templateUrl: "templates/PickPath.html"
                }) 
                
                .state('ProcessPickPath', {
                    url: '/ProcessPickPath',
                    templateUrl: "templates/ProcessPickPath.html"
                })

                .state('AssignDispositionIneligibility', {
                    url: '/AssignDispositionIneligibility',
                    templateUrl: "templates/AssignDispositionIneligibility.html"
                })
                .state('AssignDispositionIneligibility/:EligibilityID', {
                    url: '/AssignDispositionIneligibility/:EligibilityID',
                    templateUrl: "templates/AssignDispositionIneligibility.html"
                })

                .state('AssignDispositionIneligibilityList', {
                    url: '/AssignDispositionIneligibilityList',
                    templateUrl: "templates/AssignDispositionIneligibilityList.html"
                })

            $urlRouterProvider
                .when('/', '/PickConfiguration')
                .otherwise('/PickConfiguration');
        }
    ]);


    module.controller("PickConfiguration", function ($scope,$rootScope,$mdToast,$stateParams,UserFacility,$mdDialog) {
        $rootScope.$broadcast('preloader:active');

        // Check page permissions
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Pick Configuration',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );
                    window.location = host;
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        // Initialize scope variables
        $scope.PickConfiguration = {};
        $scope.Facilities = [];
        $scope.PartTypes = [];
        $scope.PickEligibleDispositions = [];
        $scope.EligibleAssignDispositions = []; // New array for eligible assign dispositions
        $scope.ConfigurationDetails = [];
        $scope.DetailForm = {};
        $scope.showAddDetailForm = true; // Show form by default since details are required
        $scope.isEditingDetail = false;

        // Get user facility and set as default
        UserFacility.async().then(function (d) {
            $scope.UserFacility = d.data;
            $scope.PickConfiguration.FacilityID = $scope.UserFacility;
        });

        // Load facilities
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilities',
            success: function (data) {
                if (data.Success) {
                    $scope.Facilities = data.Result;
                } else {
                    $scope.Facilities = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        // Load Eligible Part Types based on assign disposition eligibility list
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host + 'picktool/includes/Pick_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetEligiblePartTypes',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {
                    $scope.PartTypes = data.Result;
                } else {
                    $scope.PartTypes = [];
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        // Initialize Pick Eligible Dispositions as empty - will be loaded when part type is selected
        $scope.PickEligibleDispositions = [];



        // Function to load eligible from dispositions based on Part Type
        $scope.loadEligibleFromDispositions = function() {
            if (!$scope.PickConfiguration.parttypeid) {
                $scope.PickEligibleDispositions = [];
                return;
            }

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetEligibleFromDispositions&parttypeid=' + $scope.PickConfiguration.parttypeid + '&FacilityID=' + $scope.PickConfiguration.FacilityID,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.PickEligibleDispositions = data.Result;
                    } else {
                        $scope.PickEligibleDispositions = [];
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.PickEligibleDispositions = [];
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        // Function to load eligible assign dispositions based on Part Type and From Disposition
        $scope.loadEligibleAssignDispositions = function() {
            if (!$scope.PickConfiguration.parttypeid || !$scope.PickConfiguration.FromDispositionID) {
                $scope.EligibleAssignDispositions = [];
                return;
            }

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetEligibleAssignDispositions&parttypeid=' + $scope.PickConfiguration.parttypeid + '&FromDispositionID=' + $scope.PickConfiguration.FromDispositionID + '&FacilityID=' + $scope.PickConfiguration.FacilityID,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.EligibleAssignDispositions = data.Result;
                    } else {
                        $scope.EligibleAssignDispositions = [];
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.EligibleAssignDispositions = [];
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        // Function to handle Part Type change
        $scope.onPartTypeChange = function() {
            // Clear dependent fields when part type changes
            $scope.PickConfiguration.FromDispositionID = '';
            $scope.PickConfiguration.ToDispositionID = '';
            $scope.EligibleAssignDispositions = [];

            // Load eligible from dispositions for the selected part type
            $scope.loadEligibleFromDispositions();
        };

        // Function to handle From Disposition change
        $scope.onFromDispositionChange = function() {
            $scope.PickConfiguration.ToDispositionID = ''; // Clear assign disposition when from disposition changes
            $scope.loadEligibleAssignDispositions();
        };

        // Configuration Details Functions
        $scope.addConfigurationDetail = function() {
            $scope.DetailForm = {
                PartTypeName: $scope.getPartTypeName($scope.PickConfiguration.parttypeid),
                FromDispositionName: $scope.getDispositionName($scope.PickConfiguration.FromDispositionID, 'PickEligible'),
                ToDispositionName: $scope.getDispositionName($scope.PickConfiguration.ToDispositionID, 'All'),
                mpn: '',
                PickQuantity: '',
                busy: false
            };
            $scope.isEditingDetail = false;
            $scope.showAddDetailForm = true;
        };

        $scope.editConfigurationDetail = function(detail) {
            $scope.DetailForm = {
                DetailID: detail.DetailID,
                PartTypeName: $scope.getPartTypeName($scope.PickConfiguration.parttypeid),
                FromDispositionName: $scope.getDispositionName($scope.PickConfiguration.FromDispositionID, 'PickEligible'),
                ToDispositionName: $scope.getDispositionName($scope.PickConfiguration.ToDispositionID, 'All'),
                mpn: detail.mpn,
                PickQuantity: detail.PickQuantity,
                busy: false
            };
            $scope.isEditingDetail = true;
            $scope.showAddDetailForm = true;
        };

        // Cleanup MPN function - calls GetExactMPN to clean and standardize MPN
        $scope.cleanupMPN = function() {
            if (!$scope.DetailForm.mpn || $scope.DetailForm.mpn.trim() === '') {
                // If MPN is empty, just move to next field
                setTimeout(function() {
                    document.getElementById('pick_quantity_input').focus();
                }, 100);
                return;
            }

            var cleanupData = {
                mpn: $scope.DetailForm.mpn,
                parttypeid: $scope.PickConfiguration.parttypeid
            };

            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=CleanupMPN&' + $.param(cleanupData),
                success: function (data) {
                    if (data.Success && data.CleanMPN) {
                        // Update the MPN field with the cleaned MPN
                        $scope.DetailForm.mpn = data.CleanMPN;
                        $scope.$apply();

                        // Automatically move cursor to Pick Quantity field
                        setTimeout(function() {
                            document.getElementById('pick_quantity_input').focus();
                        }, 100);
                    } else {
                        // Show error message if validation fails with configuration part type
                        var configPartType = $scope.getPartTypeName($scope.PickConfiguration.parttypeid);
                        var errorMessage = data.Result;
                        if (configPartType) {
                            errorMessage += ' (' + configPartType + ')';
                        }
                        $mdToast.show(
                            $mdToast.simple()
                                .content(errorMessage)
                                .action('OK')
                                .position('right')
                                .hideDelay(4000)
                                .toastClass('md-toast-danger md-block')
                        );
                        // Keep the invalid MPN and select it for easy replacement
                        setTimeout(function() {
                            var mpnInput = document.getElementById('mpn_input');
                            mpnInput.focus();
                            mpnInput.select();
                        }, 100);
                    }
                    initSessionTime();
                }, error: function (data) {
                    // On error, show message and select MPN field
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error cleaning up MPN')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    setTimeout(function() {
                        var mpnInput = document.getElementById('mpn_input');
                        mpnInput.focus();
                        mpnInput.select();
                    }, 100);
                    initSessionTime();
                }
            });
        };

        // Validate MPN function
        $scope.validateMPN = function(mpn) {
            if (!mpn || mpn.trim() === '') {
                return; // Allow empty MPN
            }

            var validationData = {
                mpn: mpn,
                ConfigurationID: $scope.PickConfiguration.ConfigurationID,
                parttypeid: $scope.PickConfiguration.parttypeid,
                DetailID: $scope.DetailForm.DetailID || ''
            };

            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateMPN&' + $.param(validationData),
                success: function (data) {
                    if (!data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(4000)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error validating MPN')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.saveConfigurationDetail = function() {
            $scope.DetailForm.busy = true;

            // Validate required fields
            if (!$scope.DetailForm.PickQuantity || $scope.DetailForm.PickQuantity < 1) {
                $scope.DetailForm.busy = false;
                $mdToast.show(
                    $mdToast.simple()
                        .content('Pick Quantity is required and must be at least 1')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                );
                return;
            }

            // Validate required configuration fields manually
            if (!$scope.PickConfiguration.FacilityID ||
                !$scope.PickConfiguration.PickPathName ||
                !$scope.PickConfiguration.parttypeid ||
                !$scope.PickConfiguration.FromDispositionID ||
                !$scope.PickConfiguration.ToDispositionID) {
                $scope.DetailForm.busy = false;
                $mdToast.show(
                    $mdToast.simple()
                        .content('Please complete all required configuration fields first')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                );
                return;
            }

            // Prepare data for saving
            var saveData = {
                // Configuration data
                ConfigurationID: $scope.PickConfiguration.ConfigurationID || '',
                FacilityID: $scope.PickConfiguration.FacilityID,
                PickPathName: $scope.PickConfiguration.PickPathName,
                parttypeid: $scope.PickConfiguration.parttypeid,
                FromDispositionID: $scope.PickConfiguration.FromDispositionID,
                ToDispositionID: $scope.PickConfiguration.ToDispositionID,
                // Detail data
                DetailID: $scope.DetailForm.DetailID || '',
                mpn: $scope.DetailForm.mpn || '',
                PickQuantity: $scope.DetailForm.PickQuantity
            };

            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=SaveConfigurationWithDetail&' + $.param(saveData),
                success: function (data) {
                    $scope.DetailForm.busy = false;
                    if (data.Success) {
                        var wasNewConfiguration = !$scope.PickConfiguration.ConfigurationID;

                        // Update ConfigurationID if it was just created
                        if (data.ConfigurationID && !$scope.PickConfiguration.ConfigurationID) {
                            $scope.PickConfiguration.ConfigurationID = data.ConfigurationID;
                        }

                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-success md-block')
                        );

                        if (wasNewConfiguration) {
                            // Both configuration and detail were saved - redirect to configuration page
                            setTimeout(function() {
                                window.location = "#!/PickConfiguration/" + $scope.PickConfiguration.ConfigurationID;
                            }, 1000); // Small delay to show the toast message
                        } else {
                            // Only detail was saved - refresh details list and prepare for next detail
                            $scope.loadConfigurationDetails($scope.PickConfiguration.ConfigurationID);

                            // After list is refreshed, open form for next detail and focus on MPN field
                            setTimeout(function() {
                                $scope.addConfigurationDetail();
                                $scope.$apply();

                                // Focus on MPN field after form is shown
                                setTimeout(function() {
                                    var mpnField = document.getElementById('mpn_input');
                                    if (mpnField) {
                                        mpnField.focus();
                                    }
                                }, 100);
                            }, 500); // Wait for list to refresh
                        }

                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.DetailForm.busy = false;
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error saving detail')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.deleteConfigurationDetail = function(detailID) {
            // Validation: Check if this is the only detail remaining
            if ($scope.ConfigurationDetails && $scope.ConfigurationDetails.length <= 1) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Cannot delete the last MPN. Please close the Pick Path')
                        .action('OK')
                        .position('right')
                        .hideDelay(4000)
                        .toastClass('md-toast-danger md-block')
                );
                return;
            }

            var confirm = $mdDialog.confirm()
                .title('Delete Configuration Detail')
                .textContent('Are you sure you want to delete this configuration detail?')
                .ariaLabel('Delete confirmation')
                .ok('Delete')
                .cancel('Cancel');

            $mdDialog.show(confirm).then(function() {
                // User clicked Delete - proceed with deletion from database
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'picktool/includes/Pick_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteConfigurationDetail&DetailID=' + detailID + '&ConfigurationID=' + $scope.PickConfiguration.ConfigurationID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            // Reload configuration details to show updated list
                            $scope.loadConfigurationDetails($scope.PickConfiguration.ConfigurationID);
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(3000)
                                    .toastClass('md-toast-success md-block')
                            );
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(3000)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Error deleting detail')
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-danger md-block')
                        );
                        initSessionTime(); $scope.$apply();
                    }
                });
            });
            // If user clicked Cancel or pressed ESC, do nothing
        };

        $scope.cancelAddDetail = function() {
            $scope.DetailForm = {};
            $scope.showAddDetailForm = false;
            $scope.isEditingDetail = false;
        };

        // Cancel configuration creation - clear all fields except FacilityID
        // If configuration already exists, redirect to list page
        $scope.cancelConfiguration = function() {
            if ($scope.PickConfiguration.ConfigurationID) {
                // Configuration already created - redirect to list page
                window.location = '#!/PickConfigurationDetailsList';
            } else {
                // Configuration not created yet - clear form
                var facilityID = $scope.PickConfiguration.FacilityID;
                $scope.PickConfiguration = {
                    FacilityID: facilityID
                };
                $scope.EligibleAssignDispositions = [];
                // Reset form validation
                if ($scope.pick_configuration_form) {
                    $scope.pick_configuration_form.$setPristine();
                    $scope.pick_configuration_form.$setUntouched();
                }
            }
        };

        // Helper functions to get names for display
        $scope.getPartTypeName = function(parttypeid) {
            var partType = $scope.PartTypes.find(function(pt) { return pt.parttypeid == parttypeid; });
            return partType ? partType.parttype : '';
        };

        $scope.getDispositionName = function(dispositionId, type) {
            var dispositions = type === 'PickEligible' ? $scope.PickEligibleDispositions : $scope.EligibleAssignDispositions;
            var disposition = dispositions.find(function(d) { return d.disposition_id == dispositionId; });
            return disposition ? disposition.disposition : '';
        };

        // Auto Generate Pick Path Name
        $scope.AutoGeneratePickPathName = function() {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GeneratePickPathName',
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.PickConfiguration.PickPathName = data.Result;
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Pick Path Name generated: ' + data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-success md-block')
                        );
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error generating Pick Path Name')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    initSessionTime(); $scope.$apply();
                }
            });
        };



        // Load configuration details
        $scope.loadConfigurationDetails = function(configurationID) {
            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPickConfigurationDetails&ConfigurationID=' + configurationID,
                success: function (data) {
                    if (data.Success && data.Details) {
                        $scope.ConfigurationDetails = data.Details;
                        console.log('Configuration Details loaded:', data.Details);
                    } else {
                        $scope.ConfigurationDetails = [];
                        console.log('No configuration details found');
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.ConfigurationDetails = [];
                    console.log('Error loading configuration details');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        // Load existing configuration if ConfigurationID is provided
        if ($stateParams.ConfigurationID) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPickConfigurationDetails&ConfigurationID=' + $stateParams.ConfigurationID,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    if (data.Success) {
                        $scope.PickConfiguration = data.Result;
                        // Load configuration details from the response (no need for separate call)
                        if (data.Details) {
                            $scope.ConfigurationDetails = data.Details;
                            console.log('Configuration Details loaded:', data.Details);
                        } else {
                            $scope.ConfigurationDetails = [];
                            console.log('No configuration details found');
                        }
                        // Load eligible assign dispositions to populate the dropdown
                        $scope.loadEligibleFromDispositions();
                        $scope.loadEligibleAssignDispositions();
                    } else {
                        $scope.PickConfiguration = {};
                        var op = data.Result.split(' ');
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        }
    });

    // Pick Configuration Details List Controller
    module.controller('PickConfigurationDetailsList', function($scope, $rootScope, $stateParams, $mdToast, $mdDialog) {

        // Initialize variables
        $scope.groupedConfigurations = [];
        $scope.currentPage = 0;
        $scope.itemsPerPage = 25;
        $scope.total = 0;
        $scope.OrderBy = 'CreatedDate';
        $scope.OrderByType = 'asc';
        $scope.filter_text = [{}];
        $scope.busy = false;

        // Function to calculate days difference from created date
        $scope.GetDaysDifference = function(createdDate) {
            if (!createdDate) return 0;
            var duration = moment.duration(moment(new Date()).diff(moment(createdDate)));
            var days = duration.asDays();
            return Math.floor(days);
        };

        // Pagination functions
        $scope.range = function() {
            var rangeSize = 5;
            var ps = [];
            var start;

            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize+1;
            }

            for (var i=start; i<start+rangeSize; i++) {
                if (i>=0) {
                    ps.push(i);
                }
            }
            return ps;
        };

        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };

        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };

        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage)-1;
        };

        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount()) {
                $scope.currentPage++;
            }
        };

        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() ? "disabled" : "";
        };

        $scope.setPage = function(n) {
            $scope.currentPage = n;
        };

        $scope.firstPage = function() {
            $scope.currentPage = 0;
        };

        $scope.lastPage = function() {
            $scope.currentPage = $scope.pageCount();
        };

        // Watch for page changes
        $scope.$watch('currentPage', function(newValue, oldValue) {
            if (newValue !== oldValue) {
                $scope.CallServerFunction(newValue);
            }
        });

        // Sorting function
        $scope.MakeOrderBy = function(orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.CallServerFunction($scope.currentPage);
        };

        // Filter function
        $scope.MakeFilter = function() {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        // Main server call function
        $scope.CallServerFunction = function(page) {
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            var data = {
                ajax: 'GetPickConfigurationDetailsList',
                page: page,
                itemsPerPage: $scope.itemsPerPage,
                OrderBy: $scope.OrderBy,
                OrderByType: $scope.OrderByType,
                filter: $scope.filter_text[0]
            };

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: data,
                success: function(response) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;

                    if (response.Success) {
                        $scope.groupedConfigurations = response.Result || [];
                        $scope.total = response.Total || 0;
                    } else {
                        $scope.groupedConfigurations = [];
                        $scope.total = 0;
                        $scope.showToast('Error loading data: ' + response.Result, 'md-toast-danger');
                    }

                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;
                    $scope.groupedConfigurations = [];
                    $scope.total = 0;
                    $scope.showToast('Error loading data: ' + error, 'md-toast-danger');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Export to Excel function
        $scope.ExportPickDetailsxls = function() {
            var filterData = {};
            if ($scope.filter_text && $scope.filter_text[0]) {
                filterData = $scope.filter_text[0];
            }

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'ExportPickConfigurationDetails',
                    OrderBy: $scope.OrderBy || 'CreatedDate',
                    OrderByType: $scope.OrderByType || 'desc',
                    filter: filterData
                },
                success: function(response) {
                    if (response.Success) {
                        window.location = "templates/PickConfigurationDetailsxls.php";
                    } else {
                        $scope.showToast('Export failed: ' + response.Result, 'md-toast-danger');
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $scope.showToast('Export failed: ' + error, 'md-toast-danger');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Edit pick detail function
        $scope.editPickDetail = function(detail) {
            window.location.href = '#!/PickConfiguration/' + detail.ConfigurationID;
        };

        // Delete pick detail function
        $scope.deletePickDetail = function(detailID, configurationID) {
            var confirm = $mdDialog.confirm()
                .title('Delete Pick Configuration Detail')
                .textContent('Are you sure you want to delete this pick configuration detail?')
                .ariaLabel('Delete confirmation')
                .ok('Delete')
                .cancel('Cancel');

            $mdDialog.show(confirm).then(function() {
                jQuery.ajax({
                    url: host + 'picktool/includes/Pick_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: {
                        ajax: 'DeleteConfigurationDetail',
                        DetailID: detailID,
                        ConfigurationID: configurationID
                    },
                    success: function(response) {
                        if (response.Success) {
                            $scope.showToast(response.Result, 'md-toast-success');
                            $scope.CallServerFunction($scope.currentPage);
                        } else {
                            $scope.showToast('Delete failed: ' + response.Result, 'md-toast-danger');
                        }
                        initSessionTime();
                        $scope.$apply();
                    },
                    error: function(xhr, status, error) {
                        $scope.showToast('Delete failed: ' + error, 'md-toast-danger');
                        initSessionTime();
                        $scope.$apply();
                    }
                });
            });
        };

        // Toast message function
        $scope.showToast = function(message, toastClass) {
            $mdToast.show({
                template: '<md-toast class="' + toastClass + ' md-block"><span>' + message + '</span></md-toast>',
                hideDelay: 3000,
                position: 'right'
            });
        };

        // Convert filter array to single object (same as TruckList)
        $scope.convertSingle = function(multiarray) {
            var result = {};
            for (var i in multiarray) {
                result[i] = multiarray[i];
            }
            return result;
        };

        // Pick Detail Tracking History Functions
        $scope.selectedDetail = {};
        $scope.trackingHistory = [];
        $scope.trackingHistoryLoading = false;

        // Show tracking history for entire configuration (all details)
        $scope.showConfigurationTrackingHistory = function(config) {
            $mdDialog.show({
                templateUrl: 'pickDetailTrackingDialog.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
                fullscreen: false,
                controller: function($scope, $mdDialog, $mdToast) {
                    $scope.selectedDetail = {
                        ConfigurationID: config.ConfigurationID,
                        PickPathName: config.PickPathName
                    };
                    $scope.trackingHistory = [];
                    $scope.trackingHistoryLoading = true;

                    $scope.closeDialog = function() {
                        $mdDialog.hide();
                    };

                    // Fetch tracking history for entire configuration (DetailID = 0)
                    jQuery.ajax({
                        url: host + 'picktool/includes/Pick_submit.php',
                        dataType: 'json',
                        type: 'get',
                        data: 'ajax=GetPickDetailTrackingHistory&DetailID=0&ConfigurationID=' + config.ConfigurationID,
                        success: function(data) {
                            $scope.trackingHistoryLoading = false;
                            if (data.Success) {
                                $scope.trackingHistory = data.Result;
                            } else {
                                $scope.trackingHistory = [];
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content('Error loading configuration history: ' + data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(3000)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.$apply();
                        },
                        error: function(xhr, status, error) {
                            $scope.trackingHistoryLoading = false;
                            $scope.trackingHistory = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .content('Error loading configuration history: ' + error)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(3000)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.$apply();
                        }
                    });
                }
            });
        };

        // Close configuration function
        $scope.closeConfiguration = function(config) {
            var confirm = $mdDialog.confirm()
                .title('Close Configuration')
                .textContent('Are you sure you want to close this configuration? This will mark the configuration and all its details as "Completed".')
                .ariaLabel('Close confirmation')
                .ok('Close Configuration')
                .cancel('Cancel');

            $mdDialog.show(confirm).then(function() {
                jQuery.ajax({
                    url: host + 'picktool/includes/Pick_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: {
                        ajax: 'CloseConfiguration',
                        ConfigurationID: config.ConfigurationID
                    },
                    success: function(response) {
                        if (response.Success) {
                            $scope.showToast(response.Result, 'md-toast-success');
                            $scope.CallServerFunction($scope.currentPage);
                        } else {
                            $scope.showToast('Close failed: ' + response.Result, 'md-toast-danger');
                        }
                        initSessionTime();
                        $scope.$apply();
                    },
                    error: function(xhr, status, error) {
                        $scope.showToast('Close failed: ' + error, 'md-toast-danger');
                        initSessionTime();
                        $scope.$apply();
                    }
                });
            });
        };



        // Initialize the page
        $scope.CallServerFunction(0);

    });

    // Pick Configuration Details List Completed Controller
    module.controller('PickConfigurationDetailsListCompleted', function($scope, $rootScope, $stateParams, $mdToast, $mdDialog) {

        // Initialize variables
        $scope.groupedConfigurations = [];
        $scope.currentPage = 0;
        $scope.itemsPerPage = 25;
        $scope.total = 0;
        $scope.OrderBy = 'CreatedDate';
        $scope.OrderByType = 'desc';
        $scope.filter_text = [{}];
        $scope.busy = false;

        // Pagination functions
        $scope.range = function() {
            var rangeSize = 5;
            var ps = [];
            var start;

            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize+1;
            }

            for (var i=start; i<start+rangeSize; i++) {
                if (i>=0) {
                    ps.push(i);
                }
            }
            return ps;
        };

        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
                $scope.CallServerFunction($scope.currentPage);
            }
        };

        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };

        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage)-1;
        };

        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount()) {
                $scope.currentPage++;
            }
        };

        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() ? "disabled" : "";
        };

        $scope.setPage = function(n) {
            $scope.currentPage = n;
        };

        $scope.firstPage = function() {
            $scope.currentPage = 0;
        };

        $scope.lastPage = function() {
            $scope.currentPage = $scope.pageCount();
        };

        // Watch for page changes
        $scope.$watch('currentPage', function(newValue, oldValue) {
            if (newValue !== oldValue) {
                $scope.CallServerFunction(newValue);
            }
        });

        // Toast function
        $scope.showToast = function(message, className) {
            $mdToast.show(
                $mdToast.simple()
                    .textContent(message)
                    .position('right')
                    .hideDelay(3000)
                    .toastClass(className)
            );
        };

        // Sorting function
        $scope.MakeOrderBy = function(orderBy) {
            if ($scope.OrderBy === orderBy) {
                $scope.OrderByType = ($scope.OrderByType === 'asc') ? 'desc' : 'asc';
            } else {
                $scope.OrderBy = orderBy;
                $scope.OrderByType = 'asc';
            }
            $scope.currentPage = 0;
            $scope.CallServerFunction(0);
        };

        // Filter function
        $scope.MakeFilter = function() {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        // Main server call function
        $scope.CallServerFunction = function(page) {
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            var data = {
                ajax: 'GetPickConfigurationDetailsListCompleted',
                page: page,
                itemsPerPage: $scope.itemsPerPage,
                OrderBy: $scope.OrderBy,
                OrderByType: $scope.OrderByType,
                filter: $scope.filter_text[0]
            };

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: data,
                success: function(response) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;

                    if (response.Success) {
                        $scope.groupedConfigurations = response.Result || [];
                        $scope.total = response.Total || 0;
                    } else {
                        $scope.groupedConfigurations = [];
                        $scope.total = 0;
                        $scope.showToast('Error loading data: ' + response.Result, 'md-toast-danger');
                    }

                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;
                    $scope.groupedConfigurations = [];
                    $scope.total = 0;
                    $scope.showToast('Error loading data: ' + error, 'md-toast-danger');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Export to Excel function
        $scope.ExportPickDetailsxls = function() {
            var filterData = {};
            if ($scope.filter_text && $scope.filter_text[0]) {
                filterData = $scope.filter_text[0];
            }

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'ExportPickConfigurationDetailsCompleted',
                    OrderBy: $scope.OrderBy || 'CreatedDate',
                    OrderByType: $scope.OrderByType || 'desc',
                    filter: filterData
                },
                success: function(response) {
                    if (response.Success) {
                        window.location = "templates/PickConfigurationDetailsCompletedxls.php";
                    } else {
                        $scope.showToast('Export failed: ' + response.Result, 'md-toast-danger');
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $scope.showToast('Export failed: ' + error, 'md-toast-danger');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Show tracking history for entire configuration (all details)
        $scope.showConfigurationTrackingHistory = function(config) {
            $mdDialog.show({
                templateUrl: 'pickDetailTrackingDialog.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
                fullscreen: false,
                controller: function($scope, $mdDialog, $mdToast) {
                    $scope.selectedDetail = {
                        ConfigurationID: config.ConfigurationID,
                        PickPathName: config.PickPathName
                    };
                    $scope.trackingHistory = [];
                    $scope.trackingHistoryLoading = true;

                    $scope.closeDialog = function() {
                        $mdDialog.hide();
                    };

                    // Load tracking history for the entire configuration
                    jQuery.ajax({
                        url: host + 'picktool/includes/Pick_submit.php',
                        dataType: 'json',
                        type: 'get',
                        data: {
                            ajax: 'GetPickDetailTrackingHistory',
                            ConfigurationID: config.ConfigurationID,
                            DetailID: 0 // 0 means get all tracking for the configuration
                        },
                        success: function(response) {
                            $scope.trackingHistoryLoading = false;
                            if (response.Success) {
                                $scope.trackingHistory = response.Result || [];
                            } else {
                                $scope.trackingHistory = [];
                                $mdToast.show(
                                    $mdToast.simple()
                                        .textContent('Failed to load tracking history: ' + response.Result)
                                        .position('right')
                                        .hideDelay(3000)
                                        .toastClass('md-toast-danger')
                                );
                            }
                            $scope.$apply();
                        },
                        error: function(xhr, status, error) {
                            $scope.trackingHistoryLoading = false;
                            $scope.trackingHistory = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .textContent('Error loading tracking history: ' + error)
                                    .position('right')
                                    .hideDelay(3000)
                                    .toastClass('md-toast-danger')
                            );
                            $scope.$apply();
                        }
                    });
                }
            });
        };

        // Initialize the page
        $scope.CallServerFunction(0);

    });

    // Pick Path Controller (updated to use grouped display like PickConfigurationDetailsList)
    module.controller('PickPath', function($scope, $rootScope, $stateParams, $mdToast, $mdDialog) {

        // Check page permissions
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Pick Path Assignment',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );
                    window.location = host;
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        // Initialize variables
        $scope.groupedConfigurations = []; // Changed from pagedItems to groupedConfigurations
        $scope.currentPage = 0;
        $scope.itemsPerPage = 25;
        $scope.total = 0;
        $scope.OrderBy = 'CreatedDate';
        $scope.OrderByType = 'asc';
        $scope.filter_text = [{}];
        $scope.busy = false;

        // Pagination functions
        $scope.range = function() {
            var rangeSize = 5;
            var ps = [];
            var start;
            var totalPages = $scope.pageCount();

            start = $scope.currentPage;
            if (start > totalPages - rangeSize) {
                start = totalPages - rangeSize + 1;
            }

            for (var i = start; i < start + rangeSize && i < totalPages; i++) {
                if (i >= 0) {
                    ps.push(i);
                }
            }
            return ps;
        };

        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
                $scope.CallServerFunction($scope.currentPage);
            }
        };

        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
                $scope.CallServerFunction($scope.currentPage);
            }
        };

        $scope.firstPage = function() {
            $scope.currentPage = 0;
            $scope.CallServerFunction($scope.currentPage);
        };

        $scope.lastPage = function() {
            $scope.currentPage = $scope.pageCount() - 1;
            $scope.CallServerFunction($scope.currentPage);
        };

        $scope.setPage = function(n) {
            $scope.currentPage = n;
            $scope.CallServerFunction($scope.currentPage);
        };

        $scope.pageCount = function() {
            return Math.ceil($scope.total / $scope.itemsPerPage);
        };

        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };

        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };



        // Toast message function
        $scope.showToast = function(message, toastClass) {
            $mdToast.show({
                template: '<md-toast class="' + toastClass + ' md-block"><span>' + message + '</span></md-toast>',
                hideDelay: 3000,
                position: 'right'
            });
        };

        // Main server call function
        $scope.CallServerFunction = function(page) {
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            var data = {
                ajax: 'GetPickPathListGrouped',
                page: page,
                itemsPerPage: $scope.itemsPerPage,
                OrderBy: $scope.OrderBy,
                OrderByType: $scope.OrderByType,
                filter: $scope.filter_text[0]
            };

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: data,
                success: function(response) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;

                    if (response.Success) {
                        $scope.groupedConfigurations = response.Result;
                        $scope.total = response.Total;
                        $scope.$apply();
                    } else {
                        $scope.showToast('Error loading pick paths: ' + response.Result, 'md-toast-danger');
                    }
                },
                error: function(xhr, status, error) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;
                    $scope.showToast('Network error loading pick paths', 'md-toast-danger');
                    $scope.$apply();
                }
            });
        };

        // Filter function
        $scope.MakeFilter = function() {
            $scope.currentPage = 0;
            $scope.CallServerFunction(0);
        };

        // Sorting function
        $scope.MakeOrderBy = function(orderBy) {
            if ($scope.OrderBy === orderBy) {
                $scope.OrderByType = ($scope.OrderByType === 'asc') ? 'desc' : 'asc';
            } else {
                $scope.OrderBy = orderBy;
                $scope.OrderByType = 'asc';
            }
            $scope.currentPage = 0;
            $scope.CallServerFunction(0);
        };

        // Convert filter array to single object (same as TruckList)
        $scope.convertSingle = function(multiarray) {
            var result = {};
            for (var i in multiarray) {
                result[i] = multiarray[i];
            }
            return result;
        };

        // User Assignment Modal Functions
        $scope.selectedConfiguration = null;
        $scope.availableUsers = [];
        $scope.assignedUsers = [];
        $scope.userSearchText = '';

        $scope.openUserAssignmentModal = function(config) {
            $scope.selectedConfiguration = config;

            var parentScope = $scope;

            $mdDialog.show({
                templateUrl: 'user-assignment-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
                controller: function($scope, $mdDialog, $rootScope, $mdToast) {
                    // Copy configuration data
                    $scope.selectedConfiguration = config;
                    $scope.availableUsers = [];
                    $scope.assignedUsers = [];
                    $scope.userSearchText = '';

                    // Modal close functions
                    $scope.closeDialog = function() {
                        $mdDialog.hide();
                        parentScope.CallServerFunction(parentScope.currentPage);
                    };

                    $scope.cancel = function() {
                        $mdDialog.cancel();
                    };

                    // Toast function
                    $scope.showToast = function(message, toastClass) {
                        $mdToast.show({
                            template: '<md-toast class="' + toastClass + ' md-block"><span>' + message + '</span></md-toast>',
                            hideDelay: 3000,
                            position: 'right'
                        });
                    };

                    // Load available users
                    $scope.loadAvailableUsers = function() {
                        if (!$scope.selectedConfiguration) return;

                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'picktool/includes/Pick_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: {
                                ajax: 'GetAvailableUsers',
                                ConfigurationID: $scope.selectedConfiguration.ConfigurationID
                            },
                            success: function(response) {
                                if (response.Success) {
                                    $scope.availableUsers = response.Result;
                                } else {
                                    $scope.showToast('Failed to load users: ' + response.Result, 'md-toast-danger');
                                    $scope.availableUsers = [];
                                }
                                $rootScope.$broadcast('preloader:hide');
                                $scope.$apply();
                            },
                            error: function(xhr, status, error) {
                                $scope.showToast('Failed to load users: ' + error, 'md-toast-danger');
                                $scope.availableUsers = [];
                                $rootScope.$broadcast('preloader:hide');
                                $scope.$apply();
                            }
                        });
                    };

                    // Load assigned users
                    $scope.loadAssignedUsers = function() {
                        if (!$scope.selectedConfiguration) return;

                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'picktool/includes/Pick_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: {
                                ajax: 'GetConfigurationUserAssignments',
                                ConfigurationID: $scope.selectedConfiguration.ConfigurationID
                            },
                            success: function(response) {
                                if (response.Success) {
                                    $scope.assignedUsers = response.Result;
                                } else {
                                    $scope.showToast('Failed to load assignments: ' + response.Result, 'md-toast-danger');
                                    $scope.assignedUsers = [];
                                }
                                $rootScope.$broadcast('preloader:hide');
                                $scope.$apply();
                            },
                            error: function(xhr, status, error) {
                                $scope.showToast('Failed to load assignments: ' + error, 'md-toast-danger');
                                $scope.assignedUsers = [];
                                $rootScope.$broadcast('preloader:hide');
                                $scope.$apply();
                            }
                        });
                    };

                    // Assign user to configuration
                    $scope.assignUser = function(user) {
                        if (!$scope.selectedConfiguration) return;

                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'picktool/includes/Pick_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: {
                                ajax: 'AssignUserToConfiguration',
                                ConfigurationID: $scope.selectedConfiguration.ConfigurationID,
                                UserID: user.UserId,
                                UserName: user.UserName
                            },
                            success: function(response) {
                                if (response.Success) {
                                    $scope.showToast('User assigned successfully', 'md-toast-success');
                                    $scope.loadAvailableUsers();
                                    $scope.loadAssignedUsers();
                                } else {
                                    $scope.showToast('Failed to assign user: ' + response.Result, 'md-toast-danger');
                                    $rootScope.$broadcast('preloader:hide');
                                }
                                $scope.$apply();
                            },
                            error: function(xhr, status, error) {
                                $scope.showToast('Failed to assign user: ' + error, 'md-toast-danger');
                                $rootScope.$broadcast('preloader:hide');
                                $scope.$apply();
                            }
                        });
                    };

                    // Remove user from configuration
                    $scope.removeUser = function(assignment) {
                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'picktool/includes/Pick_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: {
                                ajax: 'RemoveUserFromConfiguration',
                                AssignID: assignment.AssignID
                            },
                            success: function(response) {
                                if (response.Success) {
                                    $scope.showToast('User removed successfully', 'md-toast-success');
                                    $scope.loadAvailableUsers();
                                    $scope.loadAssignedUsers();
                                } else {
                                    $scope.showToast('Failed to remove user: ' + response.Result, 'md-toast-danger');
                                    $rootScope.$broadcast('preloader:hide');
                                }
                                $scope.$apply();
                            },
                            error: function(xhr, status, error) {
                                $scope.showToast('Failed to remove user: ' + error, 'md-toast-danger');
                                $rootScope.$broadcast('preloader:hide');
                                $scope.$apply();
                            }
                        });
                    };

                    // Load data when modal opens
                    $scope.loadAvailableUsers();
                    $scope.loadAssignedUsers();
                }
            });
        };

        // Show tracking history for entire configuration (all details)
        $scope.showConfigurationTrackingHistory = function(config) {
            $mdDialog.show({
                templateUrl: 'pickDetailTrackingDialog.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
                fullscreen: false,
                controller: function($scope, $mdDialog, $mdToast) {
                    $scope.selectedDetail = {
                        ConfigurationID: config.ConfigurationID,
                        PickPathName: config.PickPathName
                    };
                    $scope.trackingHistory = [];
                    $scope.trackingHistoryLoading = true;

                    $scope.closeDialog = function() {
                        $mdDialog.hide();
                    };

                    // Fetch tracking history for entire configuration (DetailID = 0)
                    jQuery.ajax({
                        url: host + 'picktool/includes/Pick_submit.php',
                        dataType: 'json',
                        type: 'get',
                        data: 'ajax=GetPickDetailTrackingHistory&DetailID=0&ConfigurationID=' + config.ConfigurationID,
                        success: function(data) {
                            $scope.trackingHistoryLoading = false;
                            if (data.Success) {
                                $scope.trackingHistory = data.Result;
                            } else {
                                $scope.trackingHistory = [];
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content('Error loading configuration history: ' + data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(3000)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.$apply();
                        },
                        error: function(xhr, status, error) {
                            $scope.trackingHistoryLoading = false;
                            $scope.trackingHistory = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .content('Error loading configuration history')
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(3000)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.$apply();
                        }
                    });
                }
            });
        };

        // Initialize the page
        $scope.CallServerFunction(0);
    });

    // ProcessPickPath Controller
    module.controller('ProcessPickPath', function($scope, $rootScope, $mdToast, $mdDialog, $window, UserFacility) {

        // Initialize variables
        $scope.configurations = [];
        $scope.selectedConfiguration = null;
        $scope.pagedItems = [];
        $scope.busy = false;

        // Initialize pick process input variables using object to avoid scope issues
        $scope.pickInput = {
            sourceBinName: '',
            serialNumber: ''
        };
        $scope.sourceBinBusy = false;
        $scope.serialNumberBusy = false;

        // Initialize recent transactions
        $scope.recentTransactions = [];

        // Initialize MPN location suggestions
        $scope.mpnLocationSuggestions = [];
        $scope.mpnLocationBusy = false;
        $scope.selectedMPN = '';
        $scope.selectedFromDisposition = '';

        // Load user facility
        UserFacility.async().then(function (d) {
            $scope.UserFacility = d.data;
            $scope.loadConfigurations();
        });

        // Load configurations for current user facility
        $scope.loadConfigurations = function() {
            if (!$scope.UserFacility) {
                $scope.showToast('User facility not loaded', 'md-toast-danger');
                return;
            }

            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'GetConfigurationsForFacility',
                    FacilityID: $scope.UserFacility
                },
                success: function(response) {
                    if (response.Success) {
                        $scope.configurations = response.Result;
                        if ($scope.configurations.length === 0) {
                            $scope.showToast('No configurations found for your facility', 'md-toast-info');
                        }
                    } else {
                        $scope.showToast('Failed to load configurations: ' + response.Result, 'md-toast-danger');
                        $scope.configurations = [];
                    }
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $scope.showToast('Failed to load configurations: ' + error, 'md-toast-danger');
                    $scope.configurations = [];
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Load configuration details for selected configuration (filtered by current user assignments)
        $scope.loadConfigurationDetails = function() {
            if (!$scope.selectedConfiguration) {
                $scope.pagedItems = [];
                $scope.recentTransactions = [];
                return;
            }

            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'GetUserAssignedConfigurationDetails',
                    ConfigurationID: $scope.selectedConfiguration.ConfigurationID
                },
                success: function(response) {
                    if (response.Success) {
                        $scope.pagedItems = response.Result;
                        // Initialize additional properties for each detail
                        angular.forEach($scope.pagedItems, function(detail) {
                            detail.busy = false;
                            detail.IsMapped = detail.MappingID ? true : false;
                            if (detail.IsMapped && detail.MappedBinName) {
                                detail.BinName = detail.MappedBinName;
                            }
                        });
                        if ($scope.pagedItems.length === 0) {
                            $scope.showToast('No details assigned to you for this configuration', 'md-toast-info');
                        }
                        // Load recent transactions after loading configuration details
                        $scope.loadRecentTransactions();
                    } else {
                        $scope.showToast('Failed to load configuration details: ' + response.Result, 'md-toast-danger');
                        $scope.pagedItems = [];
                    }
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $scope.showToast('Failed to load configuration details: ' + error, 'md-toast-danger');
                    $scope.pagedItems = [];
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Load recent transactions for selected configuration
        $scope.loadRecentTransactions = function() {
            if (!$scope.selectedConfiguration) {
                $scope.recentTransactions = [];
                return;
            }

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'GetRecentTransactions',
                    ConfigurationID: $scope.selectedConfiguration.ConfigurationID
                },
                success: function(response) {
                    if (response.Success) {
                        $scope.recentTransactions = response.Result;
                    } else {
                        $scope.recentTransactions = [];
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $scope.recentTransactions = [];
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Map bin to configuration detail
        $scope.mapBinToDetail = function(detail) {
            if (!detail.BinName || detail.BinName.trim() === '') {
                $scope.showToast('Please enter a bin name', 'md-toast-warning');
                return;
            }

            if (detail.IsMapped) {
                $scope.showToast('This detail is already mapped to a bin', 'md-toast-info');
                return;
            }

            detail.busy = true;

            // Extract values to avoid object serialization issues
            var detailID = detail.DetailID;
            var binName = detail.BinName.trim();

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'MapBinToConfigurationDetail',
                    DetailID: detailID,
                    BinName: binName
                },
                success: function(response) {
                    detail.busy = false;
                    if (response.Success) {
                        detail.IsMapped = true;
                        detail.MappingID = response.MappingID;
                        detail.CustomPalletID = response.CustomPalletID;
                        $scope.showToast('Bin mapped successfully', 'md-toast-success');
                        $scope.loadConfigurationDetails();
                    } else {
                        $scope.showToast('Failed to map bin: ' + response.Result, 'md-toast-danger');
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    detail.busy = false;
                    $scope.showToast('Failed to map bin: ' + error, 'md-toast-danger');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Process Source BIN function
        $scope.processSourceBin = function() {
            if (!$scope.sourceBinName || $scope.sourceBinName.trim() === '') {
                $scope.showToast('Please enter a source bin name', 'md-toast-warning');
                return;
            }

            $scope.sourceBinBusy = true;
            $rootScope.$broadcast('preloader:active');

            // TODO: Implement source bin processing logic
            // This will be implemented based on your requirements

            // Placeholder for now
            setTimeout(function() {
                $scope.sourceBinBusy = false;
                $rootScope.$broadcast('preloader:hide');
                $scope.showToast('Source BIN processing - functionality to be implemented', 'md-toast-info');
                $scope.$apply();
            }, 1000);
        };

        // Process Serial Number function
        $scope.processSerialNumber = function() {
            if (!$scope.pickInput.serialNumber || $scope.pickInput.serialNumber.trim() === '') {
                $scope.showToast('Please enter a serial number', 'md-toast-warning');
                return;
            }

            if (!$scope.selectedConfiguration) {
                $scope.showToast('Please select a configuration first', 'md-toast-warning');
                return;
            }

            $scope.serialNumberBusy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'ProcessSerialNumber',
                    SerialNumber: $scope.pickInput.serialNumber,
                    ConfigurationID: $scope.selectedConfiguration.ConfigurationID
                },
                success: function(response) {
                    $scope.serialNumberBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if (response.Success) {
                        $scope.showToast('Serial processed successfully: ' + response.Result, 'md-toast-success');
                        // Clear the serial number input after successful processing
                        $scope.pickInput.serialNumber = '';

                        // Automatically refresh Pick Configuration Details to show updated counts
                        $scope.loadConfigurationDetails();

                        // Automatically refresh Recent Transactions
                        $scope.loadRecentTransactions();
                    } else {
                        $scope.showToast('Serial processing failed: ' + response.Result, 'md-toast-danger');

                         // Automatically refresh Pick Configuration Details to show updated counts
                        $scope.loadConfigurationDetails();
                    }

                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $scope.serialNumberBusy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.showToast('Failed to process serial number: ' + error, 'md-toast-danger');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Process Source Bin function
        $scope.processSourceBin = function() {
            if (!$scope.pickInput.sourceBinName || $scope.pickInput.sourceBinName.trim() === '') {
                $scope.showToast('Please enter a source bin name', 'md-toast-warning');
                return;
            }

            if (!$scope.selectedConfiguration) {
                $scope.showToast('Please select a configuration first', 'md-toast-warning');
                return;
            }

            $scope.sourceBinBusy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'ProcessSourceBin',
                    SourceBinName: $scope.pickInput.sourceBinName,
                    ConfigurationID: $scope.selectedConfiguration.ConfigurationID
                },
                success: function(response) {
                    $scope.sourceBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if (response.Success) {
                        $scope.showToast('Source bin processed successfully: ' + response.Result, 'md-toast-success');
                        // Clear the source bin input after successful processing
                        $scope.pickInput.sourceBinName = '';

                        // Automatically refresh Pick Configuration Details to show updated counts
                        $scope.loadConfigurationDetails();

                        // Automatically refresh Recent Transactions
                        $scope.loadRecentTransactions();
                    } else {
                        $scope.showToast('Source bin processing failed: ' + response.Result, 'md-toast-danger');

                         // Automatically refresh Pick Configuration Details to show updated counts
                        $scope.loadConfigurationDetails();
                    }

                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $scope.sourceBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.showToast('Failed to process source bin: ' + error, 'md-toast-danger');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Search MPN locations function
        $scope.searchMPNLocations = function(mpn, fromDispositionID, partTypeID) {
            if (!mpn || mpn.trim() === '') {
                $scope.showToast('MPN is required for location search', 'md-toast-warning');
                return;
            }

            if (!$scope.selectedConfiguration) {
                $scope.showToast('Please select a configuration first', 'md-toast-warning');
                return;
            }

            $scope.mpnLocationBusy = true;
            $scope.selectedMPN = mpn;
            $scope.selectedFromDisposition = '';
            $scope.mpnLocationSuggestions = [];
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'SearchMPNLocations',
                    MPN: mpn,
                    FromDispositionID: fromDispositionID,
                    PartTypeID: partTypeID,
                    ConfigurationID: $scope.selectedConfiguration.ConfigurationID
                },
                success: function(response) {
                    $scope.mpnLocationBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if (response.Success) {
                        $scope.mpnLocationSuggestions = response.Result.locations || [];
                        $scope.selectedFromDisposition = response.Result.fromDisposition || '';

                        if ($scope.mpnLocationSuggestions.length === 0) {
                            $scope.showToast('No valid locations found for MPN: ' + mpn, 'md-toast-info');
                        } else {
                            $scope.showToast('Found ' + $scope.mpnLocationSuggestions.length + ' location(s) for MPN: ' + mpn, 'md-toast-success');
                        }
                    } else {
                        $scope.showToast('Error searching locations: ' + response.Result, 'md-toast-danger');
                        $scope.mpnLocationSuggestions = [];
                    }
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $scope.mpnLocationBusy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.showToast('Network error searching locations: ' + error, 'md-toast-danger');
                    $scope.mpnLocationSuggestions = [];
                    $scope.$apply();
                }
            });
        };

        // Toast message function
        $scope.showToast = function(message, toastClass) {
            $mdToast.show({
                template: '<md-toast class="' + toastClass + ' md-block"><span>' + message + '</span></md-toast>',
                hideDelay: 3000,
                position: 'right'
            });
        };

        // Actions Functions for Bin Management

        // Create Bin Function
        $scope.CreateBin = function(detail, ev) {
            // Store the current detail for later use in mapping
            $scope.currentDetail = detail;

            // Initialize create bin data
            $scope.createBinData = {
                idPackage: '',
                BinName: '',
                FacilityID: $scope.UserFacility || '',
                LocationType: 'WIP',
                LocationGroup: '',
                Disposition: detail.ToDisposition || '',
                Notes: ''
            };
            $scope.createBinBusy = false;

            // Load required data for create bin modal
            $scope.loadCreateBinData();

            $mdDialog.show({
                templateUrl: 'createBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: false,
                scope: $scope,
                preserveScope: true,
                controller: function($scope, $mdDialog) {
                    $scope.cancel = function() {
                        $mdDialog.cancel();
                    };
                }
            }).then(function(result) {
                if (result && result.success) {
                    // Refresh the configuration details
                    $scope.loadConfigurationDetails();
                }
            });
        };

        // Load required data for create bin modal
        $scope.loadCreateBinData = function() {
            // Load package types
            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'GetBinPackageTypes',
                    FacilityID: $scope.createBinData.FacilityID
                },
                success: function(response) {
                    if (response.Success) {
                        $scope.PackageTypes = response.Result;
                    }
                    $scope.$apply();
                }
            });

            // Load facility data
            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'GetSessionFacility'
                },
                success: function(response) {
                    if (response.Success) {
                        $scope.Facility = [response.Result];
                        $scope.createBinData.FacilityID = response.Result.FacilityID;
                        // Load location groups after facility is set
                        $scope.loadLocationGroups();
                    }
                    $scope.$apply();
                }
            });
        };

        // Auto Generate Bin Name function for ProcessPickPath module
        $scope.AutoGenerateBinNameProcessPickPath = function() {
            // Validate that container type is selected
            if (!$scope.createBinData.idPackage) {
                $scope.showToast('Please select a Bin Type first', 'md-toast-warning');
                return;
            }

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'GenerateBinName',
                    PackageID: $scope.createBinData.idPackage
                },
                success: function(response) {
                    $rootScope.$broadcast('preloader:hide');
                    if (response.Success) {
                        $scope.createBinData.BinName = response.Result;
                        $scope.showToast('Bin name generated successfully', 'md-toast-success');
                    } else {
                        $scope.showToast('Failed to generate bin name: ' + response.Result, 'md-toast-danger');
                    }
                    $scope.$apply();
                },
                error: function() {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.showToast('Error generating bin name', 'md-toast-danger');
                    $scope.$apply();
                }
            });
        };

        // Create bin function
        $scope.createBin = function() {
            $scope.createBinBusy = true;
            $rootScope.$broadcast('preloader:active');

            // Validate required fields
            if (!$scope.createBinData.BinName || !$scope.createBinData.idPackage || !$scope.createBinData.FacilityID) {
                $scope.showToast('Please fill all required fields', 'md-toast-warning');
                $scope.createBinBusy = false;
                $rootScope.$broadcast('preloader:hide');
                return;
            }

            // If Location Group is provided as text (scanned), try to find the GroupID
            if ($scope.createBinData.LocationGroup && !$scope.createBinData.LocationGroupID) {
                // Search for the location group by name
                var foundGroup = null;
                if ($scope.LocationGroups) {
                    foundGroup = $scope.LocationGroups.find(function(group) {
                        return group.GroupName.toLowerCase() === $scope.createBinData.LocationGroup.toLowerCase();
                    });
                }

                if (foundGroup) {
                    $scope.createBinData.LocationGroupID = foundGroup.GroupID;
                    $scope.createBinData.LocationID = foundGroup.LocationID;
                } else {
                    // If not found in loaded groups, let backend handle validation
                    $scope.createBinData.LocationGroupName = $scope.createBinData.LocationGroup;
                }
            }

            // Prepare data for bin creation
            var binData = {
                ajax: 'CreateBin',
                BinName: $scope.createBinData.BinName,
                idPackage: $scope.createBinData.idPackage,
                FacilityID: $scope.createBinData.FacilityID,
                LocationID: $scope.createBinData.LocationID,
                LocationGroupID: $scope.createBinData.LocationGroupID,
                LocationGroup: $scope.createBinData.LocationGroup,
                LocationGroupName: $scope.createBinData.LocationGroupName,
                Notes: $scope.createBinData.Notes
            };

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: binData,
                success: function(data) {
                    $scope.createBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if (data.Success) {
                        $scope.showToast('Bin created successfully: ' + $scope.createBinData.BinName, 'md-toast-success');

                        // Auto-map the created bin to the configuration detail
                        if ($scope.currentDetail && $scope.currentDetail.DetailID) {
                            $scope.mapBinToConfigurationDetail($scope.currentDetail.DetailID, $scope.createBinData.BinName);
                        }

                        $mdDialog.hide({success: true});
                        // Refresh the configuration details
                        $scope.loadConfigurationDetails();
                    } else {
                        $scope.showToast('Failed to create bin: ' + data.Result, 'md-toast-danger');
                    }
                    $scope.$apply();
                },
                error: function() {
                    $scope.createBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.showToast('Error creating bin', 'md-toast-danger');
                    $scope.$apply();
                }
            });
        };

        // Move Bin Function
        $scope.MoveBin = function(detail, ev) {
            // Get current bin details first
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'GetBinDetails',
                    CustomPalletID: detail.CustomPalletID
                },
                success: function(response) {
                    $rootScope.$broadcast('preloader:hide');
                    if (response.Success) {
                        var binData = response.Result;
                        binData.DetailID = detail.DetailID; // Add DetailID for mapping removal
                        $scope.CurrentPallet = binData;
                        $scope.confirmDetails = {};

                        // Show the modal
                        $scope.showMoveBinModal(ev);
                    } else {
                        $scope.showToast('Failed to get bin details: ' + response.Result, 'md-toast-danger');
                    }
                    $scope.$apply();
                },
                error: function() {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.showToast('Error getting bin details', 'md-toast-danger');
                    $scope.$apply();
                }
            });
        };

        // Show Move Bin Modal
        $scope.showMoveBinModal = function(ev) {

            $mdDialog.show({
                templateUrl: 'moveBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                scope: $scope,
                preserveScope: true,
                controller: function($scope, $mdDialog) {
                    $scope.cancel = function() {
                        $mdDialog.cancel();
                    };

                    $scope.MoveBinToNewLocationGroup = function(ev) {
                        // Validate required fields
                        if (!$scope.confirmDetails.NewLocationGroup) {
                            $scope.showToast('Please select a new location group', 'md-toast-warning');
                            return;
                        }

                        // If Location Group is provided as text (scanned), try to find the GroupID
                        if ($scope.confirmDetails.NewLocationGroup && !$scope.confirmDetails.GroupID) {
                            $scope.confirmDetails.GroupName = $scope.confirmDetails.NewLocationGroup;
                        }

                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'picktool/includes/Pick_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: {
                                ajax: 'MoveBinToNewLocationGroup',
                                CustomPalletID: $scope.CurrentPallet.CustomPalletID,
                                BinName: $scope.CurrentPallet.BinName,
                                DetailID: $scope.CurrentPallet.DetailID,
                                CurrentLocationGroup: $scope.CurrentPallet.CurrentLocationGroup,
                                CurrentLocationType: $scope.CurrentPallet.LocationType,
                                GroupID: $scope.confirmDetails.GroupID,
                                GroupName: $scope.confirmDetails.GroupName || $scope.confirmDetails.NewLocationGroup,
                                NewLocationGroup: $scope.confirmDetails.NewLocationGroup
                            },
                            success: function(data) {
                                $rootScope.$broadcast('preloader:hide');
                                if (data.Success) {
                                    $scope.showToast('Bin moved successfully', 'md-toast-success');
                                    $mdDialog.hide();
                                    $scope.loadConfigurationDetails();
                                } else {
                                    $scope.showToast('Failed to move bin: ' + data.Result, 'md-toast-danger');
                                }
                                $scope.$apply();
                            },
                            error: function() {
                                $rootScope.$broadcast('preloader:hide');
                                $scope.showToast('Error moving bin', 'md-toast-danger');
                                $scope.$apply();
                            }
                        });
                    };
                }
            });
        };

        // Close Bin Function
        $scope.CloseBin = function(detail, ev) {
            // Validate that bin is not inside a parent
            if (detail.ParentBinName && detail.ParentBinName.trim() !== '') {
                $scope.showToast('Bin cannot be closed while it is inside a parent bin. Please remove from parent first.', 'md-toast-danger');
                return;
            }

            var binData = {
                BinName: detail.MappedBinName,
                CustomPalletID: detail.CustomPalletID,
                DetailID: detail.DetailID  // Add DetailID for mapping removal
            };

            $scope.CurrentBin = binData;
            $scope.confirmDetails = {};

            $mdDialog.show({
                templateUrl: 'closeBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                scope: $scope,
                preserveScope: true,
                controller: function($scope, $mdDialog) {
                    $scope.cancel = function() {
                        $mdDialog.cancel();
                    };

                    $scope.hide = function() {
                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'picktool/includes/Pick_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: {
                                ajax: 'CloseBin',
                                CustomPalletID: $scope.CurrentBin.CustomPalletID,
                                DetailID: $scope.CurrentBin.DetailID,
                                NewSealID: $scope.confirmDetails.NewSealID,
                                BinWeight: $scope.confirmDetails.BinWeight,
                                AuditController: $scope.confirmDetails.AuditController,
                                Password: $scope.confirmDetails.Password,
                                ShippingID: $scope.confirmDetails.ShippingID || '',
                                OutboundLocationGroup: $scope.confirmDetails.OutboundLocationGroup || ''
                            },
                            success: function(data) {
                                $rootScope.$broadcast('preloader:hide');
                                if (data.Success) {
                                    $scope.showToast('Bin closed successfully', 'md-toast-success');
                                    $mdDialog.hide();
                                    $scope.loadConfigurationDetails();
                                } else {
                                    $scope.showToast('Failed to close bin: ' + data.Result, 'md-toast-danger');
                                }
                                $scope.$apply();
                            },
                            error: function() {
                                $rootScope.$broadcast('preloader:hide');
                                $scope.showToast('Error closing bin', 'md-toast-danger');
                                $scope.$apply();
                            }
                        });
                    };
                }
            });
        };

        // Consolidate Bin Function
        $scope.ConsolidateBin = function(detail, ev) {
            var binData = {
                BinName: detail.MappedBinName,
                CustomPalletID: detail.CustomPalletID,
                DetailID: detail.DetailID  // Add DetailID for mapping removal
            };

            $scope.CurrentCustomPalletPallet = binData;
            $scope.confirmDetails4 = {};

            $mdDialog.show({
                templateUrl: 'consolidateBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                locals: {
                    CurrentCustomPalletPallet: $scope.CurrentCustomPalletPallet
                },
                controller: function($scope, $mdDialog, CurrentCustomPalletPallet) {
                    // Fresh scope - no preserveScope
                    $scope.CurrentCustomPalletPallet = CurrentCustomPalletPallet;
                    $scope.confirmDetails4 = {};

                    $scope.cancel = function() {
                        $mdDialog.cancel();
                    };

                    // Need to inject required services and functions from parent
                    $scope.showToast = function(message, className) {
                        // Access parent scope's showToast function
                        var parentScope = $scope.$parent;
                        while (parentScope && !parentScope.showToast) {
                            parentScope = parentScope.$parent;
                        }
                        if (parentScope && parentScope.showToast) {
                            parentScope.showToast(message, className);
                        }
                    };

                    $scope.ConsolidateBin = function(ev) {
                        // Validate required fields (matching Move Bin pattern)
                        if (!$scope.confirmDetails4.ToBinName) {
                            $scope.showToast('Destination Bin Name is required', 'md-toast-danger');
                            return;
                        }

                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'picktool/includes/Pick_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: {
                                ajax: 'ConsolidateBin',
                                FromBinName: $scope.CurrentCustomPalletPallet.BinName,
                                ToBinName: $scope.confirmDetails4.ToBinName || 'n/a',
                                FromCustomPalletID: $scope.CurrentCustomPalletPallet.CustomPalletID,
                                DetailID: $scope.CurrentCustomPalletPallet.DetailID,
                                ToShipmentContainer: $scope.confirmDetails4.ToShipmentContainer || 'n/a',
                                ModuleName: 'ProcessPickPath'
                            },
                            success: function(data) {
                                $rootScope.$broadcast('preloader:hide');
                                if (data.Success) {
                                    $scope.showToast('Bin consolidated successfully', 'md-toast-success');
                                    $mdDialog.hide();
                                    // Need to access parent scope to reload data
                                    var parentScope = $scope.$parent;
                                    while (parentScope && !parentScope.loadConfigurationDetails) {
                                        parentScope = parentScope.$parent;
                                    }
                                    if (parentScope && parentScope.loadConfigurationDetails) {
                                        parentScope.loadConfigurationDetails();
                                    }
                                } else {                                    
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content('Failed to consolidate bin: ' + data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );
                                }
                                $scope.$apply();
                            },
                            error: function() {
                                $rootScope.$broadcast('preloader:hide');
                                $scope.showToast('Error consolidating bin', 'md-toast-danger');
                                $scope.$apply();
                            }
                        });
                    };
                }
            });
        };

        // Nest to Bin Function
        $scope.NestToBin = function(detail, ev) {
            $scope.nestToBinData = {
                BinName: detail.MappedBinName,
                CustomPalletID: detail.CustomPalletID,
                parentBin: ''
            };
            $scope.nestToBinBusy = false;

            $mdDialog.show({
                templateUrl: 'nestToBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                scope: $scope,
                preserveScope: true,
                controller: function($scope, $mdDialog) {
                    $scope.cancel = function() {
                        $mdDialog.cancel();
                    };
                }
            });
        };

        $scope.nestToBin = function() {
            $scope.nestToBinBusy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=NestToBin&' + $.param($scope.nestToBinData),
                success: function(data) {
                    $scope.nestToBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if (data.Success) {
                        $scope.showToast('Bin nested successfully', 'md-toast-success');
                        $mdDialog.hide();
                        $scope.loadConfigurationDetails();
                    } else {
                        $scope.showToast('Failed to nest bin: ' + data.Result, 'md-toast-danger');
                    }
                    $scope.$apply();
                },
                error: function() {
                    $scope.nestToBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.showToast('Error nesting bin', 'md-toast-danger');
                    $scope.$apply();
                }
            });
        };

        // Helper function to map bin to configuration detail (used by CreateBin)
        $scope.mapBinToConfigurationDetail = function(detailID, binName) {
            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: 'MapBinToConfigurationDetail',
                    DetailID: detailID,
                    BinName: binName
                },
                success: function(response) {
                    if (response.Success) {
                        $scope.showToast('Bin mapped successfully to configuration detail', 'md-toast-success');
                    } else {
                        $scope.showToast('Failed to map bin: ' + response.Result, 'md-toast-warning');
                    }
                    $scope.$apply();
                },
                error: function() {
                    $scope.showToast('Error mapping bin to configuration detail', 'md-toast-danger');
                    $scope.$apply();
                }
            });
        };

        // Location Group autocomplete functions for Create Bin modal
        $scope.LocationGroupChange = function(searchText) {
            // Trigger location group search for create bin
        };

        $scope.queryLocationGroupSearch = function(searchText) {
            if (!searchText || searchText.length < 1) {
                return $scope.LocationGroups || [];
            }

            // Use receive module's GetMatchingLocationGroups for real-time search
            return jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'get',
                data: {
                    ajax: 'GetMatchingLocationGroups',
                    keyword: searchText,
                    FacilityID: $scope.createBinData.FacilityID,
                    LocationType: 'WIP'
                }
            }).then(function(response) {
                if (response.Success && response.Result) {
                    return response.Result;
                } else {
                    return [];
                }
            });
        };

        $scope.selectedLocationGroupChange = function(item) {
            if (item) {
                $scope.createBinData.LocationGroupID = item.GroupID;
                $scope.createBinData.LocationID = item.LocationID;
                $scope.createBinData.LocationGroup = item.GroupName;
            }
        };

        // Load location groups for create bin using receive module function
        $scope.loadLocationGroups = function() {
            if ($scope.createBinData.FacilityID) {
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'get',
                    data: {
                        ajax: 'GetMatchingLocationGroups',
                        keyword: '',
                        FacilityID: $scope.createBinData.FacilityID,
                        LocationType: 'WIP'
                    },
                    success: function(response) {
                        if (response.Success) {
                            $scope.LocationGroups = response.Result;
                        } else {
                            $scope.LocationGroups = [];
                        }
                        $scope.$apply();
                    },
                    error: function() {
                        $scope.LocationGroups = [];
                        $scope.$apply();
                    }
                });
            }
        };

        // Location search functions for Move Bin modal
        $scope.LocationChange1 = function(searchText) {
            // Trigger location search for move bin
        };

        $scope.queryLocationSearch1 = function(searchText) {
            if (!searchText || searchText.length < 1) {
                return [];
            }

            // Use receive module's GetMatchingLocationGroups with current location type
            var locationType = $scope.CurrentPallet ? $scope.CurrentPallet.LocationType : 'WIP';
            var facilityID = $scope.CurrentPallet ? $scope.CurrentPallet.FacilityID : $_SESSION['user']['FacilityID'];

            return jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'get',
                data: {
                    ajax: 'GetMatchingLocationGroups',
                    keyword: searchText,
                    FacilityID: facilityID,
                    LocationType: locationType
                }
            }).then(function(response) {
                if (response.Success && response.Result) {
                    return response.Result;
                } else {
                    return [];
                }
            });
        };

        $scope.selectedLocationChange1 = function(item) {
            if (item) {
                $scope.confirmDetails.GroupID = item.GroupID;
                $scope.confirmDetails.GroupName = item.GroupName;
                $scope.confirmDetails.NewLocationGroup = item.GroupName;
            }
        };

        // Location search functions for Close Bin modal
        $scope.LocationChangeCloseBin = function(searchText) {
            $scope.confirmDetails.OutboundLocationGroup = searchText;
        };

        $scope.queryLocationSearchCloseBin = function(searchText) {
            if (searchText) {
                if (searchText != '' && searchText != 'undefined') {
                    return jQuery.ajax({
                        url: host + 'receive/includes/receive_submit.php',
                        dataType: 'json',
                        type: 'get',
                        data: 'ajax=GetMatchingLocationGroups&keyword=' + searchText + '&LocationType=Outbound Storage'
                    }).then(function (res) {
                        if (res.Success == true) {
                            if (res.Result.length > 0) {
                                var result_array = [];
                                for (var i = 0; i < res.Result.length; i++) {
                                    result_array.push({ value: res.Result[i]['GroupName'], GroupName: res.Result[i]['GroupName'] });
                                }
                                return result_array;
                            } else {
                                return [];
                            }
                        } else {
                            return [];
                        }
                    });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        };

        $scope.selectedLocationChangeCloseBin = function(item) {
            if (item) {
                if (item.value) {
                    $scope.confirmDetails.OutboundLocationGroup = item.value;
                } else {
                    $scope.confirmDetails.OutboundLocationGroup = '';
                }
            } else {
                $scope.confirmDetails.OutboundLocationGroup = '';
            }
        };

        // Handle shipping ID change to validate required fields
        $scope.onShippingIDChange = function() {
            // This function is called when ShippingID field changes
            // The ng-required directive on OutboundLocationGroup will handle validation automatically
        };

        // Focus next field function for forms
        $scope.FocusNextField = function(fieldId, index) {
            setTimeout(function() {
                var element = document.getElementById(fieldId);
                if (element) {
                    element.focus();
                }
            }, 100);
        };

        // Print Bin Label Function
        $scope.PrintBinLabel = function(customPalletID) {
            if (customPalletID) {
                var printUrl = host + '/label/master/examples/binlabel.php?id=' + customPalletID;
                $window.open(printUrl, '_blank');
            } else {
                $scope.showToast('No bin ID available for printing', 'md-toast-warning');
            }
        };

        // Cancel function for modals
        $scope.cancel = function() {
            $mdDialog.cancel();
        };
    });

    // AssignDispositionIneligibility Controller
    module.controller("AssignDispositionIneligibility", function ($scope,$http,$rootScope,$mdToast,$stateParams,UserFacility) {
        $rootScope.$broadcast('preloader:active');

        // Check page permissions
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Pick Disposition Eligibility',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );
                    window.location = host;
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        // Initialize scope variables
        $scope.AssignDisposition = {
            FacilityID: '',
            FacilityName: '',
            DispositionFrom: '',
            DispositionTo: '',
            part_types: [],
            Status: 'Active'
        };
        $scope.Facilities = [];
        $scope.AllDispositions = [];
        $scope.PickEligibleDispositions = []; // For Disposition From field
        $scope.PartTypes = [];

        // Get user facility and set as default
        UserFacility.async().then(function (d) {
            $scope.UserFacility = d.data;
            $scope.AssignDisposition.FacilityID = $scope.UserFacility;
            // Find facility name from the facilities list
            $scope.setFacilityName();
        });

        // Function to set facility name from facilities list
        $scope.setFacilityName = function() {
            if ($scope.Facilities && $scope.AssignDisposition.FacilityID) {
                var facility = $scope.Facilities.find(function(f) {
                    return f.FacilityID == $scope.AssignDisposition.FacilityID;
                });
                if (facility) {
                    $scope.AssignDisposition.FacilityName = facility.FacilityName;
                }
            }
        };

        // Load facilities
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilities',
            success: function (data) {
                if (data.Success) {
                    $scope.Facilities = data.Result;
                    // Set facility name after facilities are loaded
                    $scope.setFacilityName();
                } else {
                    $scope.Facilities = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        // Load All Dispositions (for Disposition To field)
        jQuery.ajax({
            url: host + 'picktool/includes/Pick_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllDispositions',
            success: function (data) {
                if (data.Success) {
                    $scope.AllDispositions = data.Result;
                } else {
                    $scope.AllDispositions = [];
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        // Load Pick Eligible Dispositions (for Disposition From field)
        jQuery.ajax({
            url: host + 'picktool/includes/Pick_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetPickEligibleDispositions',
            success: function (data) {
                if (data.Success) {
                    $scope.PickEligibleDispositions = data.Result;
                } else {
                    $scope.PickEligibleDispositions = [];
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        // Load Part Types for current facility
        jQuery.ajax({
            url: host + 'picktool/includes/Pick_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetPartTypes',
            success: function (data) {
                if (data.Success) {
                    $scope.PartTypes = data.Result;
                } else {
                    $scope.PartTypes = [];
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        // Handle Part Type Selection - if "All" is selected, deselect others
        $scope.handlePartTypeSelection = function() {
            if ($scope.AssignDisposition.part_types && $scope.AssignDisposition.part_types.includes('All')) {
                // If "All" is selected, keep only "All"
                $scope.AssignDisposition.part_types = ['All'];
            }
        };

        // Save Assign Disposition Eligibility
        $scope.ManageAssignDispositionIneligibility = function () {
            $scope.AssignDisposition.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ManageAssignDispositionIneligibility&' + $.param($scope.AssignDisposition),
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.AssignDisposition.busy = false;
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        window.location = "#!/AssignDispositionIneligibilityList";
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );

                        var op = data.Result.split(' ');
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.AssignDisposition.busy = false;
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error saving configuration')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        // Load existing record if EligibilityID is provided
        if ($stateParams.EligibilityID) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetAssignDispositionIneligibilityDetails&EligibilityID=' + $stateParams.EligibilityID,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    if (data.Success) {
                        $scope.AssignDisposition = data.Result;
                        // Facility name should already be included from backend
                        // But ensure FacilityName is set for display
                        if (!$scope.AssignDisposition.FacilityName && $scope.Facilities) {
                            $scope.setFacilityName();
                        }
                    } else {
                        $scope.AssignDisposition = {};
                        var op = data.Result.split(' ');
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        }
    });

    // AssignDispositionIneligibilityList Controller
    module.controller('AssignDispositionIneligibilityList', function($scope, $rootScope, $stateParams, $mdToast, $mdDialog) {

        // Check page permission
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Pick Disposition Eligibility',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {
                    // Permission granted, load the data
                    $scope.CallServerFunction(0);
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );
                    window.location = host;
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        // Initialize variables
        $scope.pagedItems = [];
        $scope.currentPage = 0;
        $scope.itemsPerPage = 25;
        $scope.total = 0;
        $scope.OrderBy = 'CreatedDate';
        $scope.OrderByType = 'desc';
        $scope.filter_text = [{}];
        $scope.busy = false;
        $scope.dataLoaded = false;

        // Pagination functions
        $scope.range = function() {
            var rangeSize = 5;
            var ps = [];
            var start;

            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize+1;
            }

            for (var i=start; i<start+rangeSize; i++) {
                if (i>=0) {
                    ps.push(i);
                }
            }
            return ps;
        };

        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };

        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };

        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage)-1;
        };

        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount()) {
                $scope.currentPage++;
            }
        };

        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() ? "disabled" : "";
        };

        $scope.setPage = function(n) {
            $scope.currentPage = n;
        };

        $scope.firstPage = function() {
            $scope.currentPage = 0;
        };

        $scope.lastPage = function() {
            $scope.currentPage = $scope.pageCount();
        };

        // Watch for page changes
        $scope.$watch('currentPage', function(newValue, oldValue) {
            if (newValue !== oldValue) {
                $scope.CallServerFunction(newValue);
            }
        });

        // Sorting function
        $scope.MakeOrderBy = function(orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.CallServerFunction($scope.currentPage);
        };

        // Filter function
        $scope.MakeFilter = function() {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        // Main server call function
        $scope.CallServerFunction = function(page) {
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            var data = {
                ajax: 'GetAssignDispositionIneligibilityList',
                page: page,
                itemsPerPage: $scope.itemsPerPage,
                OrderBy: $scope.OrderBy,
                OrderByType: $scope.OrderByType,
                filter: $scope.filter_text[0]
            };

            jQuery.ajax({
                url: host + 'picktool/includes/Pick_submit.php',
                dataType: 'json',
                type: 'post',
                data: data,
                success: function(response) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;
                    $scope.dataLoaded = true;

                    if (response.Success) {
                        $scope.pagedItems = response.Result || [];
                        $scope.total = response.Total || 0;
                    } else {
                        $scope.pagedItems = [];
                        $scope.total = 0;
                        $scope.showToast('Error loading data: ' + response.Result, 'md-toast-danger');
                    }

                    initSessionTime();
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;
                    $scope.dataLoaded = true;
                    $scope.pagedItems = [];
                    $scope.total = 0;
                    $scope.showToast('Error loading data: ' + error, 'md-toast-danger');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Edit record function
        $scope.editRecord = function(eligibilityID) {
            window.location.href = '#!/AssignDispositionIneligibility/' + eligibilityID;
        };

        // Delete record function
        $scope.deleteRecord = function(eligibilityID, ev) {
            var confirm = $mdDialog.confirm()
                .title('Delete Confirmation')
                .textContent('Are you sure you want to delete this Pick Disposition Eligibility configuration?')
                .ariaLabel('Delete confirmation')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');

            $mdDialog.show(confirm).then(function() {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');

                jQuery.ajax({
                    url: host + 'picktool/includes/Pick_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: {
                        ajax: 'DeleteAssignDispositionIneligibility',
                        EligibilityID: eligibilityID
                    },
                    success: function(response) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.busy = false;

                        if (response.Success) {
                            $scope.showToast(response.Result, 'md-toast-success');
                            // Reload the current page data
                            $scope.CallServerFunction($scope.currentPage);
                        } else {
                            $scope.showToast('Error: ' + response.Result, 'md-toast-danger');
                        }

                        initSessionTime();
                        $scope.$apply();
                    },
                    error: function(xhr, status, error) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.busy = false;
                        $scope.showToast('Error deleting record: ' + error, 'md-toast-danger');
                        initSessionTime();
                        $scope.$apply();
                    }
                });
            });
        };

        // Toast message function
        $scope.showToast = function(message, toastClass) {
            $mdToast.show({
                template: '<md-toast class="' + toastClass + ' md-block"><span>' + message + '</span></md-toast>',
                hideDelay: 3000,
                position: 'right'
            });
        };



    });

})();
