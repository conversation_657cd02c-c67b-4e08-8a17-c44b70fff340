<div ng-controller = "CompletedTruckList" class="page">
    <style>
        /* Status color coding - same as TruckBookingCalender and TruckList */
        .complete{ background-color: #b4dba4;}
        .noshow{ background-color: #f0908c;}
        .failed{ background-color: #dc3545; color: white;}
        .reserved{ background-color: #cecece;}
        .requested{ background-color: #a9c5ec;}
        .inprogress{ background-color: #ffe188;}
        .arrived{ background-color: #32CD32;}

        /* Status cell styling */
        .status-cell {
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: 500;
            min-width: 100px;
            display: inline-block;
        }
    </style>
    <div ng-show="loading" class="loading" style="text-align:center;"><img src="../images/loading2.gif" /> LOADING...</div>

    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="TruckList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="TruckList = !TruckList" class="material-icons md-primary" ng-show="TruckList">keyboard_arrow_up</i>
                                <i ng-click="TruckList = !TruckList" class="material-icons md-primary" ng-show="! TruckList">keyboard_arrow_down</i>
                                <span ng-click="TruckList = !TruckList">Complete, No Show & Failed Trucks</span>
                                <div flex></div> 
                                 <a href="#!/CompleteNoShowTrucks" ng-click="ExportTruckListxls()" class="md-button md-raised btn-w-md md-default" style="display: flex; margin-right: 5px;">
                                   <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>                                
                                <a href="#!/Truck" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Truck
                                </a>
                            </div>
                        </md-toolbar>
                        <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">
                            <p>No Complete, No Show or Failed Trucks available </p>
                        </div>
                        <div class="row"  ng-show="TruckList">
                            <div class="col-md-12">
                                <div class="col-md-12">                                    
                                    <div ng-show="pagedItems" class="pull-right pageditems">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-container table-responsive" style="overflow: auto;">                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LoadNumber')" ng-class="{'orderby' : OrderBy == 'LoadNumber'}">                           
                                                        <div>                               
                                                            Load # <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LoadNumber'"></i>                                    
                                                            <span ng-show="OrderBy == 'LoadNumber'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ArrivalType')" ng-class="{'orderby' : OrderBy == 'ArrivalType'}">
                                                        <div>                               
                                                            Shipment Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ArrivalType'"></i>                                 
                                                            <span ng-show="OrderBy == 'ArrivalType'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ParkingLocationName')" ng-class="{'orderby' : OrderBy == 'ParkingLocationName'}">
                                                        <div style="min-width: 180px;">                               
                                                            Parking Location <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ParkingLocationName'"></i>                                 
                                                            <span ng-show="OrderBy == 'ParkingLocationName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CarrierName')" ng-class="{'orderby' : OrderBy == 'CarrierName'}">
                                                        <div>                               
                                                            Carrier <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CarrierName'"></i>                                 
                                                            <span ng-show="OrderBy == 'CarrierName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ArrivalDate')" ng-class="{'orderby' : OrderBy == 'ArrivalDate'}">
                                                        <div>                               
                                                            Arrival Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ArrivalDate'"></i>                                 
                                                            <span ng-show="OrderBy == 'ArrivalDate'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ArrivalTime')" ng-class="{'orderby' : OrderBy == 'ArrivalTime'}">
                                                        <div style="min-width: 230px;">                               
                                                            Expected Arrival Time <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ArrivalTime'"></i>                                 
                                                            <span ng-show="OrderBy == 'ArrivalTime'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('DepartureTime')" ng-class="{'orderby' : OrderBy == 'DepartureTime'}">
                                                        <div style="min-width: 230px;">                               
                                                            Expected Departure Time  <i class="fa fa-sort pull-right" ng-show="OrderBy != 'DepartureTime'"></i>                                 
                                                            <span ng-show="OrderBy == 'DepartureTime'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th> -->
                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy('LoadType')" ng-class="{'orderby' : OrderBy == 'LoadType'}">
                                                        <div>
                                                            Load Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LoadType'"></i>
                                                            <span ng-show="OrderBy == 'LoadType'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LoadQuantity')" ng-class="{'orderby' : OrderBy == 'LoadQuantity'}">
                                                        <div>
                                                            Load Quantity <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LoadQuantity'"></i>
                                                            <span ng-show="OrderBy == 'LoadQuantity'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                   
                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('TruckReg')" ng-class="{'orderby' : OrderBy == 'TruckReg'}">                         
                                                        <div>                               
                                                            Truck Reg <i class="fa fa-sort pull-right" ng-show="OrderBy != 'TruckReg'"></i>                                  
                                                            <span ng-show="OrderBy == 'TruckReg'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy('TrailerNumber')" ng-class="{'orderby' : OrderBy == 'TrailerNumber'}">                         
                                                        <div>                               
                                                            Trailer No <i class="fa fa-sort pull-right" ng-show="OrderBy != 'TrailerNumber'"></i>                                  
                                                            <span ng-show="OrderBy == 'TrailerNumber'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('DriverName')" ng-class="{'orderby' : OrderBy == 'DriverName'}">                         
                                                        <div>                               
                                                            Driver <i class="fa fa-sort pull-right" ng-show="OrderBy != 'DriverName'"></i>                                  
                                                            <span ng-show="OrderBy == 'DriverName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('DriverID')" ng-class="{'orderby' : OrderBy == 'DriverID'}">                         
                                                        <div>                               
                                                            Driver ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'DriverID'"></i>                                  
                                                            <span ng-show="OrderBy == 'DriverID'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Notes')" ng-class="{'orderby' : OrderBy == 'Notes'}">
                                                        <div>
                                                            Notes <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Notes'"></i>
                                                            <span ng-show="OrderBy == 'Notes'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ShipmentTicketID')" ng-class="{'orderby' : OrderBy == 'ShipmentTicketID'}">
                                                        <div>
                                                            Shipment Ticket ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ShipmentTicketID'"></i>
                                                            <span ng-show="OrderBy == 'ShipmentTicketID'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('SealID')" ng-class="{'orderby' : OrderBy == 'SealID'}">
                                                        <div>
                                                            Seal ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'SealID'"></i>
                                                            <span ng-show="OrderBy == 'SealID'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">
                                                        <div>
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>
                                                            <span ng-show="OrderBy == 'Status'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>                                                     
                                                </tr>
                                                
                                                <tr class="errornone">                                                    
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LoadNumber" ng-model="filter_text[0].LoadNumber" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ArrivalType" ng-model="filter_text[0].ArrivalType" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ParkingLocationName" ng-model="filter_text[0].ParkingLocationName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="CarrierName" ng-model="filter_text[0].CarrierName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ArrivalDate" ng-model="filter_text[0].ArrivalDate" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ArrivalTime" ng-model="filter_text[0].ArrivalTime" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="DepartureTime" ng-model="filter_text[0].DepartureTime" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->
                                                   <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LoadType" ng-model="filter_text[0].LoadType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="number" name="LoadQuantity" ng-model="filter_text[0].LoadQuantity" ng-change="MakeFilter()" aria-label="number" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="TruckReg" ng-model="filter_text[0].TruckReg" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="TrailerNumber" ng-model="filter_text[0].TrailerNumber" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                   
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="DriverName" ng-model="filter_text[0].DriverName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="DriverID" ng-model="filter_text[0].DriverID" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Notes" ng-model="filter_text[0].Notes" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ShipmentTicketID" ng-model="filter_text[0].ShipmentTicketID" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="SealID" ng-model="filter_text[0].SealID" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                                                                   
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">                                                    
                                                    <td>
                                                        {{product.LoadNumber}}
                                                    </td>
                                                    <td>
                                                        {{product.ArrivalType}}                            
                                                    </td>
                                                    <td>
                                                        {{product.ParkingLocationName}}                            
                                                    </td>   
                                                    <td>
                                                        <i class="material-icons carrier-info-icon"
                                                           style="cursor: help; color: black; font-size: 16px; margin-right: 8px; vertical-align: middle;"
                                                           title="Carrier: {{product.CarrierName}}&#10;Contact: {{product.POC}}&#10;Phone: {{product.Phone}}&#10;Email: {{product.ContactEmail}}">info</i>
                                                        {{product.CarrierName}}
                                                    </td>
                                                    <td>
                                                        {{product.ArrivalDate}}                            
                                                    </td>   
                                                    <td>
                                                        {{product.ArrivalTime}}                            
                                                    </td>   
                                                    <!-- <td>
                                                        {{product.DepartureTime}}                            
                                                    </td>   -->
                                                    <td>
                                                        {{product.LoadType}}
                                                    </td>
                                                    <td>
                                                        {{product.LoadQuantity}}
                                                    </td>
                                                    <td>
                                                        {{product.TruckReg}}                            
                                                    </td>
                                                    <td>
                                                        {{product.TrailerNumber}}                            
                                                    </td>                                                   
                                                    <td>
                                                       {{product.DriverName}}
                                                    </td> 
                                                     <td>
                                                       {{product.DriverID}}
                                                    </td> 
                                                     <td>
                                                       {{product.Notes}}
                                                    </td>
                                                     <td>
                                                       {{product.ShipmentTicketID}}
                                                    </td>
                                                     <td>
                                                       {{product.SealID}}
                                                    </td>
                                                     <td>
                                                        <span class="status-cell {{getStatusClass(product.Status)}}">
                                                            {{product.Status}}
                                                        </span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="17">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

    <!-- Truck Tracking History Dialog Template -->
    <script type="text/ng-template" id="truckTrackingDialog.html">
        <md-dialog aria-label="Truck History" style="min-width: 900px; max-width: 95vw;">
            <md-toolbar>
                <div class="md-toolbar-tools">
                    <md-icon class="material-icons">history</md-icon>
                    <h2>Truck History: {{selectedTruck.TruckReg}} ({{selectedTruck.TrailerNumber}})</h2>
                    <span flex></span>
                    <md-button class="md-icon-button" ng-click="closeDialog()">
                        <md-icon class="material-icons">close</md-icon>
                    </md-button>
                </div>
            </md-toolbar>

            <md-dialog-content style="max-width: none; max-height: 70vh;">
                <div class="md-dialog-content" style="padding: 20px;">

                    <!-- Loading State -->
                    <div ng-show="trackingHistoryLoading" class="text-center" style="padding: 40px;">
                        <md-progress-circular md-mode="indeterminate" md-diameter="40"></md-progress-circular>
                        <p style="margin-top: 20px;">Loading truck history...</p>
                    </div>

                    <!-- Empty State -->
                    <div ng-show="!trackingHistoryLoading && trackingHistory.length === 0" class="text-center" style="padding: 40px;">
                        <md-icon class="material-icons" style="font-size: 48px; opacity: 0.5; color: #999;">history</md-icon>
                        <p style="margin-top: 20px; color: #666;">No history records found for this truck.</p>
                    </div>

                    <!-- History Table -->
                    <div ng-show="!trackingHistoryLoading && trackingHistory.length > 0">
                        <div style="margin-bottom: 20px;">
                            <small style="color: #666;">
                                <md-icon class="material-icons" style="font-size: 14px; vertical-align: middle;">info</md-icon>
                                Showing {{trackingHistory.length}} history record{{trackingHistory.length !== 1 ? 's' : ''}}
                            </small>
                        </div>

                        <div class="tracking-table-responsive">
                            <table class="tracking-table tracking-table-striped tracking-table-hover">
                                <thead style="background-color: #f5f5f5;">
                                    <tr>
                                        <th style="width: 150px;">Date & Time</th>
                                        <th style="width: 120px;">User</th>
                                        <th>Action</th>
                                        <th style="width: 120px;">Module Name</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="record in trackingHistory">
                                        <td style="vertical-align: middle;">
                                            <div style="font-size: 13px;">
                                                <div style="font-weight: 500;">{{record.CreatedDate}}</div>
                                            </div>
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <div style="font-size: 13px;">{{record.CreatedBy}}</div>
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <div style="font-size: 13px; line-height: 1.4;">{{record.Action}}</div>
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <div style="font-size: 13px;">{{record.ModuleName}}</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </md-dialog-content>

            <md-dialog-actions layout="row">
                <span flex></span>
                <md-button ng-click="closeDialog()" class="md-primary">
                    Close
                </md-button>
            </md-dialog-actions>
        </md-dialog>
    </script>

</div>

<style>
/* Truck History Table Styles - Scoped to tracking table only */
.tracking-table-responsive {
    border: 1px solid #ddd;
    border-radius: 4px;
}

.tracking-table th {
    border-top: none;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    color: #666;
    padding: 12px 8px;
}

.tracking-table td {
    padding: 12px 8px;
    border-top: 1px solid #eee;
}

.tracking-table-striped tbody tr:nth-of-type(odd) {
    background-color: #f9f9f9;
}

.tracking-table-hover tbody tr:hover {
    background-color: #f0f8ff;
}


</style>

