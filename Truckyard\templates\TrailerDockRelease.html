<div class="row page" data-ng-controller="TrailerDockRelease" ng-cloak>

    <div class="col-md-12">
        <article class="article">
            <md-card class="no-margin-h">
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Trailer Dock/Release</span>
                        <div flex></div>
                        <span style="color: #666; font-size: 14px;">Search for active truck bookings</span>
                    </div>
                </md-toolbar>

                <!-- Search Section -->
                <div class="row" style="background: #f8f9fa; padding: 20px; margin: 0;">
                    <div class="col-md-12">
                        <h4 style="margin-top: 0; color: #495057;">Search Vehicle</h4>
                        <small style="color: #666; font-style: italic;">Enter Truck Registration, Trailer Number, or Load Number to find active bookings</small>

                        <form name="search_form" class="form-validation" style="margin-top: 15px;">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Truck Registration</label>
                                    <input type="text" name="SearchTruckReg" ng-model="Search.TruckReg" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" ng-change="validateVehicleSearch()" ng-enter="searchVehicle()" />
                                    <div class="error-space">
                                        <div ng-messages="search_form.SearchTruckReg.$error" multiple ng-if='search_form.SearchTruckReg.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                        <div ng-if="showSearchError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                            Either Truck Registration, Trailer Number, or Load Number is required.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-1" style="display: flex; align-items: center; justify-content: center; padding-top: 20px;">
                                <span style="font-weight: 500; color: #666; font-size: 14px;">OR</span>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Trailer Number</label>
                                    <input type="text" name="SearchTrailerNumber" ng-model="Search.TrailerNumber" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" ng-change="validateVehicleSearch()" ng-enter="searchVehicle()" />
                                    <div class="error-space">
                                        <div ng-messages="search_form.SearchTrailerNumber.$error" multiple ng-if='search_form.SearchTrailerNumber.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                        <div ng-if="showSearchError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                            Either Truck Registration, Trailer Number, or Load Number is required.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-1" style="display: flex; align-items: center; justify-content: center; padding-top: 20px;">
                                <span style="font-weight: 500; color: #666; font-size: 14px;">OR</span>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Load Number</label>
                                    <input type="text" name="SearchLoadNumber" ng-model="Search.LoadNumber" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" ng-change="validateVehicleSearch()" ng-enter="searchVehicle()" />
                                    <div class="error-space">
                                        <div ng-messages="search_form.SearchLoadNumber.$error" multiple ng-if='search_form.SearchLoadNumber.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                        <div ng-if="showSearchError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                            Either Truck Registration, Trailer Number, or Load Number is required.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-1" style="padding-top: 20px;">
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                           ng-disabled="showSearchError || Search.busy"
                                           ng-click="searchVehicle()">
                                    <span ng-show="!Search.busy">Search</span>
                                    <span ng-show="Search.busy">
                                        <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular>
                                    </span>
                                </md-button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Booking Details Section -->
                <div class="row" ng-if="BookingFound">
                    <div class="col-md-12">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; margin: 20px 0; border-radius: 8px;">
                            <h4 style="margin: 0; display: flex; align-items: center;">
                                <i class="material-icons" style="margin-right: 10px;">check_circle</i>
                                Active Booking Found
                            </h4>
                            <p style="margin: 5px 0 0 0; opacity: 0.9;">Booking details loaded successfully. Review and proceed with dock/release operations.</p>
                        </div>

                        <form name="booking_form" class="form-validation">
                            <!-- Row 1: Vehicle Information -->
                            <div class="col-md-2">
                                <md-input-container class="md-block">
                                    <label>Truck Registration</label>
                                    <input type="text"
                                           name="TruckReg"
                                           ng-model="Booking.TruckReg"
                                           ng-disabled="(Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress') || (Booking.DockingCompleted && Booking.ReleaseCompleted)"
                                           ng-minlength="3"
                                           ng-maxlength="100"
                                           ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/"
                                           ng-change="onTruckRegChange()" />

                                    <div class="error-space">
                                        <div ng-messages="booking_form.TruckReg.$error" multiple ng-if='booking_form.TruckReg.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                        <div ng-if="(Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress') || (Booking.DockingCompleted && Booking.ReleaseCompleted)"
                                             style="color: #666; font-size: 12px; margin-top: 5px;">
                                            <span ng-if="Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress'">Truck registration can only be edited when booking is "Arrived" or "In Progress".</span>
                                            <span ng-if="Booking.DockingCompleted && Booking.ReleaseCompleted">Truck registration cannot be edited after both docking and release are completed.</span>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Save Truck Registration Button -->
                            <div class="col-md-1" style="padding-top: 20px;">
                                <md-button class="md-icon-button md-raised"
                                           ng-if="Booking.Status && (Booking.Status.toLowerCase() === 'arrived' || Booking.Status.toLowerCase() === 'in progress') && TruckRegChanged && !TruckRegSaving"
                                           ng-click="saveTruckReg()"
                                           style="background-color: #4CAF50; color: white; min-width: 40px; width: 40px; height: 40px;"
                                           aria-label="Save Truck Registration">
                                    <md-icon style="color: white; font-size: 20px;">save</md-icon>
                                </md-button>

                                <!-- Loading indicator -->
                                <div ng-if="TruckRegSaving" style="text-align: center;">
                                    <md-progress-circular md-mode="indeterminate" md-diameter="24px"></md-progress-circular>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <md-input-container class="md-block">
                                    <label>Trailer Number</label>
                                    <input type="text"
                                           name="TrailerNumber"
                                           ng-model="Booking.TrailerNumber"
                                           ng-disabled="(Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress') || (Booking.DockingCompleted && Booking.ReleaseCompleted)"
                                           ng-minlength="3"
                                           ng-maxlength="100"
                                           ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/"
                                           ng-change="onTrailerNumberChange()" />

                                    <div class="error-space">
                                        <div ng-messages="booking_form.TrailerNumber.$error" multiple ng-if='booking_form.TrailerNumber.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                        <div ng-if="(Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress') || (Booking.DockingCompleted && Booking.ReleaseCompleted)"
                                             style="color: #666; font-size: 12px; margin-top: 5px;">
                                            <span ng-if="Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress'">Trailer number can only be edited when booking is "Arrived" or "In Progress".</span>
                                            <span ng-if="Booking.DockingCompleted && Booking.ReleaseCompleted">Trailer number cannot be edited after both docking and release are completed.</span>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Save Trailer Number Button -->
                            <div class="col-md-1" style="padding-top: 20px;">
                                <md-button class="md-icon-button md-raised"
                                           ng-if="Booking.Status && (Booking.Status.toLowerCase() === 'arrived' || Booking.Status.toLowerCase() === 'in progress') && TrailerNumberChanged && !TrailerNumberSaving"
                                           ng-click="saveTrailerNumber()"
                                           style="background-color: #4CAF50; color: white; min-width: 40px; width: 40px; height: 40px;"
                                           aria-label="Save Trailer Number">
                                    <md-icon style="color: white; font-size: 20px;">save</md-icon>
                                </md-button>

                                <!-- Loading indicator -->
                                <div ng-if="TrailerNumberSaving" style="text-align: center;">
                                    <md-progress-circular md-mode="indeterminate" md-diameter="24px"></md-progress-circular>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Location</label>
                                    <md-select name="ParkingLocationID" ng-model="Booking.ParkingLocationID" ng-disabled="BookingProcessed" required>
                                        <md-option ng-repeat="location in ParkingLocations" value="{{location.ParkingLocationID}}">
                                            {{location.ParkingLocationName}}
                                        </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="booking_form.ParkingLocationID.$error" multiple ng-if='booking_form.ParkingLocationID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Carrier</label>
                                    <md-select name="CarrierID" ng-model="Booking.CarrierID" ng-disabled="BookingProcessed" required ng-change="onBookingCarrierChange()">
                                        <md-option ng-repeat="carrier in Carriers" value="{{carrier.CarrierID}}">
                                            {{carrier.CarrierName}}
                                        </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="booking_form.CarrierID.$error" multiple ng-if='booking_form.CarrierID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Row 2: Booking Details -->
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Vehicle Type</label>
                                    <md-select name="TruckTypeID" ng-model="Booking.TruckTypeID" ng-disabled="BookingProcessed" required ng-change="onVehicleTypeChange()">
                                        <md-option ng-repeat="vehicleType in VehicleTypes" value="{{vehicleType.TruckTypeID}}">
                                            {{vehicleType.TruckTypeName}}
                                        </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="booking_form.TruckTypeID.$error" multiple ng-if='booking_form.TruckTypeID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Scheduled Arrival Date</label>
                                    <input type="text" ng-model="Booking.ArrivalDate" readonly />
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Scheduled Arrival Time</label>
                                    <input type="text" ng-model="Booking.ArrivalTime" readonly />
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Booking Status</label>
                                    <input type="text" ng-model="Booking.Status" readonly />
                                </md-input-container>
                            </div>

                            <!-- Row 3: Check-in Information -->
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Check-in Date</label>
                                    <input type="text" ng-model="CheckIn.Date" readonly />
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Check-in Time</label>
                                    <input type="text" ng-model="CheckIn.Time" readonly />
                                </md-input-container>
                            </div>                            

                            <div class="col-md-2">
                                <md-input-container class="md-block">
                                    <label>Load Number</label>
                                    <input type="text"
                                           name="LoadNumber"
                                           ng-model="Booking.LoadNumber"
                                           ng-disabled="(Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress') || (Booking.DockingCompleted && Booking.ReleaseCompleted)"
                                           ng-minlength="3"
                                           ng-maxlength="100"
                                           ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/"
                                           ng-change="onLoadNumberChange()" />

                                    <div class="error-space">
                                        <div ng-messages="booking_form.LoadNumber.$error" multiple ng-if='booking_form.LoadNumber.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                        <div ng-if="(Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress') || (Booking.DockingCompleted && Booking.ReleaseCompleted)"
                                             style="color: #666; font-size: 12px; margin-top: 5px;">
                                            <span ng-if="Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress'">Load number can only be edited when booking is "Arrived" or "In Progress".</span>
                                            <span ng-if="Booking.DockingCompleted && Booking.ReleaseCompleted">Load number cannot be edited after both docking and release are completed.</span>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Save Load Number Button -->
                            <div class="col-md-1" style="padding-top: 20px;">
                                <md-button class="md-icon-button md-raised"
                                           ng-if="Booking.Status && (Booking.Status.toLowerCase() === 'arrived' || Booking.Status.toLowerCase() === 'in progress') && LoadNumberChanged && !LoadNumberSaving"
                                           ng-click="saveLoadNumber()"
                                           style="background-color: #4CAF50; color: white; min-width: 40px; width: 40px; height: 40px;"
                                           aria-label="Save Load Number">
                                    <md-icon style="color: white; font-size: 20px;">save</md-icon>
                                </md-button>

                                <!-- Loading indicator -->
                                <div ng-if="LoadNumberSaving" style="text-align: center;">
                                    <md-progress-circular md-mode="indeterminate" md-diameter="24px"></md-progress-circular>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Load Type</label>
                                    <input type="text" ng-model="Booking.LoadType" readonly />
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Dock Lock Engaged?</label>
                                    <md-select name="DockLockEngaged" ng-model="Booking.DockLockEngaged" ng-disabled="BookingProcessed" required>
                                        <md-option value="Yes">Yes</md-option>
                                        <md-option value="No">No</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="booking_form.DockLockEngaged.$error" multiple ng-if='booking_form.DockLockEngaged.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Seal ID</label>
                                    <input type="text" name="SealID" ng-model="Booking.SealID" ng-disabled="BookingProcessed" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" />
                                    <div class="error-space">
                                        <div ng-messages="booking_form.SealID.$error" multiple ng-if='booking_form.SealID.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Row 4: Additional Booking Information -->
                            <div class="col-md-2">
                                <md-input-container class="md-block">
                                    <label>Load Quantity</label>
                                    <input type="number"
                                           name="LoadQuantity"
                                           ng-model="Booking.LoadQuantity"
                                           ng-disabled="(Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress') || (Booking.DockingCompleted && Booking.ReleaseCompleted)"
                                           min="1"
                                           step="1"
                                           ng-change="onLoadQuantityChange()" />

                                    <div class="error-space">
                                        <div ng-messages="booking_form.LoadQuantity.$error" multiple ng-if='booking_form.LoadQuantity.$dirty'>
                                            <div ng-message="min">Quantity must be at least 1.</div>
                                        </div>
                                        <div ng-if="(Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress') || (Booking.DockingCompleted && Booking.ReleaseCompleted)"
                                             style="color: #666; font-size: 12px; margin-top: 5px;">
                                            <span ng-if="Booking.Status && Booking.Status.toLowerCase() !== 'arrived' && Booking.Status.toLowerCase() !== 'in progress'">Load quantity can only be edited when booking is "Arrived" or "In Progress".</span>
                                            <span ng-if="Booking.DockingCompleted && Booking.ReleaseCompleted">Load quantity cannot be edited after both docking and release are completed.</span>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Save Load Quantity Button -->
                            <div class="col-md-1" style="padding-top: 20px;">
                                <md-button class="md-icon-button md-raised"
                                           ng-if="Booking.Status && (Booking.Status.toLowerCase() === 'arrived' || Booking.Status.toLowerCase() === 'in progress') && LoadQuantityChanged && !LoadQuantitySaving"
                                           ng-click="saveLoadQuantity()"
                                           style="background-color: #4CAF50; color: white; min-width: 40px; width: 40px; height: 40px;"
                                           aria-label="Save Load Quantity">
                                    <md-icon style="color: white; font-size: 20px;">save</md-icon>
                                </md-button>

                                <!-- Loading indicator -->
                                <div ng-if="LoadQuantitySaving" style="text-align: center;">
                                    <md-progress-circular md-mode="indeterminate" md-diameter="24px"></md-progress-circular>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Driver/POC </label>
                                    <input type="text"
                                           name="Driver"
                                           ng-model="Booking.DriverName"
                                           ng-disabled="BookingProcessed"
                                            />
                                    <div class="error-space">
                                        <div ng-messages="booking_form.Driver.$error" multiple ng-if='booking_form.Driver.$dirty'>
                                            <div ng-message="required">Driver/POC name is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Driver ID </label>
                                    <input type="text"
                                           name="DriverID"
                                           ng-model="Booking.DriverID"
                                           ng-disabled="BookingProcessed"
                                           ng-minlength="3"
                                           ng-maxlength="100"
                                           ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/"
                                            />
                                    <div class="error-space">
                                        <div ng-messages="booking_form.DriverID.$error" multiple ng-if='booking_form.DriverID.$dirty'>
                                            <div ng-message="required">Driver ID is required.</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Shipment Ticket ID</label>
                                    <input type="text" ng-model="Booking.ShipmentTicketID" ng-disabled="BookingProcessed" />
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Classification Type</label>
                                    <md-select name="ClassificationType"
                                               ng-model="Booking.ClassificationType"
                                               ng-disabled="BookingProcessed"
                                               ng-change="onBookingClassificationTypeChange()">
                                        <md-option value="All">All</md-option>
                                        <md-option value="UEEE">UEEE</md-option>
                                        <md-option value="WEEE">WEEE</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-if="showBookingWEEEClassificationError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                            WEEE classification requires waste collection eligibility for the selected carrier.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Waste Collection Permit</label>
                                    <input type="text"
                                           name="WasteCollectionPermit"
                                           ng-model="Booking.WasteCollectionPermit"
                                           ng-minlength="3"
                                           ng-maxlength="100"
                                           ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/"
                                           ng-disabled="BookingProcessed || !((Booking.ClassificationType === 'WEEE' || Booking.ClassificationType === 'All') && selectedBookingCarrier && selectedBookingCarrier.WasteCollectionEligible == 1)" />
                                    <div class="error-space">
                                        <div ng-messages="booking_form.WasteCollectionPermit.$error" multiple ng-if='booking_form.WasteCollectionPermit.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                        <div ng-if="!((Booking.ClassificationType === 'WEEE' || Booking.ClassificationType === 'All') && selectedBookingCarrier && selectedBookingCarrier.WasteCollectionEligible == 1)" style="color: #666; font-size: 12px; margin-top: 5px;">
                                            Available when WEEE/All classification is selected with an eligible carrier.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Go Button - Only show for unprocessed bookings -->
                            <div class="col-md-3" style="padding-top: 20px;" ng-if="!BookingProcessed">
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                           ng-disabled="booking_form.$invalid || ProcessingBooking"
                                           ng-click="processBooking()">
                                    <span ng-show="!ProcessingBooking">GO</span>
                                    <span ng-show="ProcessingBooking">
                                        <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular>
                                    </span>
                                </md-button>
                            </div>

                            <!-- Processed Status Indicator -->
                            <div class="col-md-3" style="padding-top: 20px;" ng-if="BookingProcessed">
                                <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 12px 20px; border-radius: 6px; text-align: center;">
                                    <i class="material-icons" style="vertical-align: middle; margin-right: 8px; font-size: 18px;">check_circle</i>
                                    <span style="font-weight: 500;">Already Processed</span>
                                    <div style="font-size: 12px; margin-top: 4px; opacity: 0.9;" ng-if="Booking.CheckinDate && Booking.CheckinTime">
                                        {{Booking.CheckinDate}} {{Booking.CheckinTime}}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- No Booking Found Message -->
                <div class="row" ng-if="SearchPerformed && !BookingFound && !Search.busy && !ShowUnbookedForm">
                    <div class="col-md-12">
                        <div style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; padding: 20px; margin: 20px 0; border-radius: 8px; text-align: center;">
                            <i class="material-icons" style="font-size: 48px; margin-bottom: 10px;">warning</i>
                            <h4 style="margin: 0;">No Active Booking Found</h4>
                            <p style="margin: 10px 0 15px 0; opacity: 0.9;">
                                No active booking found for the entered vehicle information.
                                The vehicle may have arrived without a prior booking.
                            </p>
                            <div style="display: flex; justify-content: center; gap: 15px; margin-top: 20px;">
                                <md-button class="md-raised md-warn" ng-click="proceedWithoutBooking()">
                                    <i class="material-icons" style="margin-right: 5px;">add_circle</i>
                                    Proceed Without Booking
                                </md-button>
                                <md-button class="md-raised" ng-click="resetSearch()" style="background: rgba(255,255,255,0.2); color: white;">
                                    <i class="material-icons" style="margin-right: 5px;">search</i>
                                    Search Again
                                </md-button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading Unbooked Data -->
                <div class="row" ng-if="LoadingUnbookedData">
                    <div class="col-md-12">
                        <div style="background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%); color: white; padding: 30px; margin: 20px 0; border-radius: 8px; text-align: center;">
                            <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="40px" style="margin-bottom: 15px;"></md-progress-circular>
                            <h4 style="margin: 0;">Loading Form Data...</h4>
                            <p style="margin: 10px 0 0 0; opacity: 0.9;">
                                Please wait while we load the required information for processing your unbooked vehicle.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Unbooked Vehicle Form -->
                <div class="row" ng-if="ShowUnbookedForm && !LoadingUnbookedData">
                    <div class="col-md-12">
                        <div style="background: linear-gradient(135deg, #ff9800 0%, #ff5722 100%); color: white; padding: 15px; margin: 20px 0; border-radius: 8px;">
                            <h4 style="margin: 0; display: flex; align-items: center;">
                                <i class="material-icons" style="margin-right: 10px;">add_circle_outline</i>
                                Processing Unbooked Vehicle
                            </h4>
                            <p style="margin: 5px 0 0 0; opacity: 0.9;">Creating new booking record for vehicle that arrived without prior booking.</p>
                        </div>

                        <form name="unbooked_form" class="form-validation">
                            <!-- Row 1: Vehicle Information -->
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Truck Registration</label>
                                    <input type="text" name="UnbookedTruckReg" ng-model="UnbookedBooking.TruckReg" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" ng-change="validateUnbookedVehicle()" />
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedTruckReg.$error" multiple ng-if='unbooked_form.UnbookedTruckReg.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                        <div ng-if="showUnbookedVehicleError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                            Either Truck Registration or Trailer Number is required.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-1" style="display: flex; align-items: center; justify-content: center; padding-top: 20px;">
                                <span style="font-weight: 500; color: #666; font-size: 14px;">OR</span>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Trailer Number</label>
                                    <input type="text" name="UnbookedTrailerNumber" ng-model="UnbookedBooking.TrailerNumber" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" ng-change="validateUnbookedVehicle()" />
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedTrailerNumber.$error" multiple ng-if='unbooked_form.UnbookedTrailerNumber.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                        <div ng-if="showUnbookedVehicleError" style="color: #f44336; font-size: 12px; margin-top: 5px;">
                                            Either Truck Registration or Trailer Number is required.
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Driver/POC Name</label>
                                    <input type="text" name="UnbookedDriverName" ng-model="UnbookedBooking.DriverName" ng-maxlength="100" ng-pattern="/^[a-zA-Z\s\.\-_]*$/" />
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedDriverName.$error" multiple ng-if='unbooked_form.UnbookedDriverName.$dirty && UnbookedBooking.DriverName'>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2">
                                <md-input-container class="md-block">
                                    <label>Driver ID</label>
                                    <input type="text" name="UnbookedDriverID" ng-model="UnbookedBooking.DriverID" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" />
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedDriverID.$error" multiple ng-if='unbooked_form.UnbookedDriverID.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Row 2: Facility and Location -->
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <input type="text" ng-model="UnbookedBooking.FacilityName" readonly style="background-color: #f5f5f5; cursor: not-allowed;" />
                                    <div class="error-space">
                                        <small style="color: #666; font-size: 11px;">Facility is set to your current facility</small>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Type</label>
                                    <md-select name="UnbookedParkTypeID" ng-model="UnbookedBooking.ParkTypeID" required ng-change="onUnbookedParkTypeChange()">
                                        <md-option ng-repeat="parkType in ParkTypes" value="{{parkType.ParkTypeID}}">
                                            {{parkType.ParkTypeName}}
                                        </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedParkTypeID.$error" multiple ng-if='unbooked_form.UnbookedParkTypeID.$dirty'>
                                            <div ng-message="required">Parking type is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Location</label>
                                    <md-select name="UnbookedParkingLocationID" ng-model="UnbookedBooking.ParkingLocationID" required>
                                        <md-option ng-repeat="location in UnbookedParkingLocations" value="{{location.ParkingLocationID}}">
                                            {{location.ParkingLocationName}}
                                        </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedParkingLocationID.$error" multiple ng-if='unbooked_form.UnbookedParkingLocationID.$dirty'>
                                            <div ng-message="required">Parking location is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Carrier</label>
                                    <md-select name="UnbookedCarrierID" ng-model="UnbookedBooking.CarrierID" required>
                                        <md-option ng-repeat="carrier in Carriers" value="{{carrier.CarrierID}}">
                                            {{carrier.CarrierName}}
                                        </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedCarrierID.$error" multiple ng-if='unbooked_form.UnbookedCarrierID.$dirty'>
                                            <div ng-message="required">Carrier is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Row 3: Vehicle and Load Details -->
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Vehicle Type</label>
                                    <md-select name="UnbookedTruckTypeID" ng-model="UnbookedBooking.TruckTypeID" required ng-change="onUnbookedVehicleTypeChange()">
                                        <md-option ng-repeat="vehicleType in VehicleTypes" value="{{vehicleType.TruckTypeID}}">
                                            {{vehicleType.TruckTypeName}}
                                        </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedTruckTypeID.$error" multiple ng-if='unbooked_form.UnbookedTruckTypeID.$dirty'>
                                            <div ng-message="required">Vehicle type is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Load Type</label>
                                    <md-select name="UnbookedLoadType" ng-model="UnbookedBooking.LoadType" required aria-label="select">
                                        <md-option value="Pallet">Pallet</md-option>
                                        <md-option value="Rack">Rack</md-option>
                                        <md-option value="Mixed">Mixed</md-option>
                                        <md-option value="Other">Other</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedLoadType.$error" multiple ng-if='unbooked_form.UnbookedLoadType.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Load Number</label>
                                    <input type="text" name="UnbookedLoadNumber" ng-model="UnbookedBooking.LoadNumber" required ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" />
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedLoadNumber.$error" multiple ng-if='unbooked_form.UnbookedLoadNumber.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Load Quantity</label>
                                    <input type="number" name="UnbookedLoadQuantity" ng-model="UnbookedBooking.LoadQuantity" min="1" step="1" />
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedLoadQuantity.$error" multiple ng-if='unbooked_form.UnbookedLoadQuantity.$dirty'>
                                            <div ng-message="min">Quantity must be at least 1.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Seal ID</label>
                                    <input type="text" name="UnbookedSealID" ng-model="UnbookedBooking.SealID" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_]+$/" />
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedSealID.$error" multiple ng-if='unbooked_form.UnbookedSealID.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens and underscores are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Row 4: Check-in Information -->
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Check-in Date</label>
                                    <input type="text" ng-model="UnbookedCheckIn.Date" readonly />
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Check-in Time</label>
                                    <input type="text" ng-model="UnbookedCheckIn.Time" readonly />
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Dock Lock Engaged?</label>
                                    <md-select name="UnbookedDockLockEngaged" ng-model="UnbookedBooking.DockLockEngaged" required>
                                        <md-option value="Yes">Yes</md-option>
                                        <md-option value="No">No</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedDockLockEngaged.$error" multiple ng-if='unbooked_form.UnbookedDockLockEngaged.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Notes</label>
                                    <input type="text" name="UnbookedNotes" ng-model="UnbookedBooking.Notes" ng-minlength="3" ng-maxlength="100" ng-pattern="/^[a-zA-Z0-9\s\.\-_,]+$/" />
                                    <div class="error-space">
                                        <div ng-messages="unbooked_form.UnbookedNotes.$error" multiple ng-if='unbooked_form.UnbookedNotes.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                            <div ng-message="pattern">Only letters, numbers, spaces, dots, hyphens, underscores and commas are allowed.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Action Buttons -->
                            <div class="col-md-12" style="text-align: center; margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <md-button class="md-raised md-primary btn-w-md"
                                           ng-disabled="unbooked_form.$invalid || showUnbookedVehicleError || UnbookedBooking.busy"
                                           ng-click="createUnbookedBooking()">
                                    <span ng-show="!UnbookedBooking.busy">
                                        <i class="material-icons" style="margin-right: 5px;">add_circle</i>
                                        Create Booking & Proceed
                                    </span>
                                    <span ng-show="UnbookedBooking.busy">
                                        <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular>
                                    </span>
                                </md-button>

                                <md-button class="md-raised" ng-click="cancelUnbookedForm()" style="margin-left: 15px;">
                                    <i class="material-icons" style="margin-right: 5px;">cancel</i>
                                    Cancel
                                </md-button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Operations Section -->
                <div id="steps-section" class="row mt-10" ng-if="BookingProcessed">
                    <div class="col-md-12">
                        <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 15px; margin: 20px 0; border-radius: 8px;">
                            <h4 style="margin: 0; display: flex; align-items: center;">
                                <i class="material-icons" style="margin-right: 10px;">build</i>
                                Dock/Release Operations
                                <span style="margin-left: auto; font-size: 14px; background: rgba(255,255,255,0.2); padding: 4px 12px; border-radius: 20px;">
                                    <span ng-if="!Booking.DockingCompleted && !Booking.ReleaseCompleted">In Progress</span>
                                    <span ng-if="Booking.DockingCompleted && !Booking.ReleaseCompleted">Docking Complete</span>
                                    <span ng-if="Booking.ReleaseCompleted">Fully Complete</span>
                                </span>
                            </h4>
                            <p style="margin: 5px 0 0 0; opacity: 0.9;">
                                <span ng-if="!Booking.DockingCompleted">Resume docking operations from where you left off.</span>
                                <span ng-if="Booking.DockingCompleted && !Booking.ReleaseCompleted">Docking completed. Proceed with release operations.</span>
                                <span ng-if="Booking.ReleaseCompleted">All operations completed successfully.</span>
                            </p>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <md-card style="height: 100%;">
                            <md-toolbar class="md-table-toolbar md-default" style="background: linear-gradient(135deg, #f9f6ed 0%, #ffeaa7 100%);">
                                <div class="md-toolbar-tools">
                                    <i class="material-icons" style="margin-right: 8px;">local_shipping</i>
                                    <h3>Trailer Docking</h3>
                                    <div flex></div>
                                    <span ng-if="DockingSteps.length > 0" style="background: rgba(0,0,0,0.1); padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                                        {{getCompletedSteps('docking')}} / {{DockingSteps.length}} Steps
                                    </span>
                                </div>
                            </md-toolbar>

                            <div class="row" style="padding: 20px;">
                                <div class="col-md-12">
                                    <div ng-if="DockingSteps.length === 0" style="text-align: center; padding: 40px; color: #666;">
                                        <i class="material-icons" style="font-size: 48px; margin-bottom: 10px; opacity: 0.5;">info</i>
                                        <p>No docking steps configured for this vehicle type and dock lock setting.</p>
                                    </div>

                                    <div ng-if="DockingSteps.length > 0">
                                        <h5 style="color: #495057; margin-bottom: 15px;">
                                            <strong>Safety Checklist:</strong>
                                        </h5>

                                        <div ng-repeat="step in DockingSteps" style="margin-bottom: 12px;">
                                            <md-checkbox ng-model="step.completed"
                                                         class="md-primary"
                                                         ng-disabled="Booking.DockingCompleted"
                                                         ng-change="handleStepChange(step, 'docking')">
                                                <span style="font-size: 14px;">
                                                    Step {{step.StepNo}}: {{step.Description}}
                                                    <span ng-if="step.SpotterTPVRRequired == 1" style="color: #ff9800; font-weight: bold;">**</span>
                                                </span>
                                            </md-checkbox>
                                        </div>

                                        <div style="margin-top: 20px; padding: 10px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
                                            <small style="color: #856404;">
                                                <strong>** Spotter TPVR Required:</strong> These steps require spotter verification.
                                            </small>
                                        </div>
                                    </div>

                                    <div class="col-md-12 btns-row" ng-if="DockingSteps.length > 0" style="margin-top: 20px;">
                                        <md-button class="md-raised btn-w-md md-warn btn-w-md"
                                                   ng-if="!Booking.DockingCompleted"
                                                   ng-click="reportUnsafe('docking')">
                                            <i class="material-icons" style="margin-right: 5px;">warning</i>
                                            Unsafe to Proceed
                                        </md-button>
                                        <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                                   ng-disabled="Booking.DockingCompleted"
                                                   ng-click="showCompletionTPVR('docking')">
                                            <i class="material-icons" style="margin-right: 5px;">check_circle</i>
                                            <span ng-if="!Booking.DockingCompleted">Complete Docking</span>
                                            <span ng-if="Booking.DockingCompleted">Docking Completed</span>
                                        </md-button>
                                    </div>
                                </div>
                            </div>
                        </md-card>
                    </div>

                    <div class="col-md-6" ng-if="Booking.DockingCompleted">
                        <md-card style="height: 100%;">
                            <md-toolbar class="md-table-toolbar md-default" style="background: linear-gradient(135deg, #d9e8fb 0%, #a9c5ec 100%);">
                                <div class="md-toolbar-tools">
                                    <i class="material-icons" style="margin-right: 8px;">exit_to_app</i>
                                    <h3>Trailer Release</h3>
                                    <div flex></div>
                                    <span ng-if="ReleaseSteps.length > 0" style="background: rgba(0,0,0,0.1); padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                                        {{getCompletedSteps('release')}} / {{ReleaseSteps.length}} Steps
                                    </span>
                                </div>
                            </md-toolbar>

                            <div class="row" style="padding: 20px;">
                                <div class="col-md-12">
                                    <div ng-if="ReleaseSteps.length === 0" style="text-align: center; padding: 40px; color: #666;">
                                        <i class="material-icons" style="font-size: 48px; margin-bottom: 10px; opacity: 0.5;">info</i>
                                        <p>No release steps configured for this vehicle type and dock lock setting.</p>
                                    </div>

                                    <div ng-if="ReleaseSteps.length > 0">
                                        <h5 style="color: #495057; margin-bottom: 15px;">
                                            <strong>Safety Checklist:</strong>
                                        </h5>

                                        <div ng-repeat="step in ReleaseSteps" style="margin-bottom: 12px;">
                                            <md-checkbox ng-model="step.completed"
                                                         class="md-primary"
                                                         ng-disabled="Booking.ReleaseCompleted"
                                                         ng-change="handleStepChange(step, 'release')">
                                                <span style="font-size: 14px;">
                                                    Step {{step.StepNo}}: {{step.Description}}
                                                    <span ng-if="step.SpotterTPVRRequired == 1" style="color: #ff9800; font-weight: bold;">**</span>
                                                </span>
                                            </md-checkbox>
                                        </div>

                                        <div style="margin-top: 20px; padding: 10px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
                                            <small style="color: #856404;">
                                                <strong>** Spotter TPVR Required:</strong> These steps require spotter verification.
                                            </small>
                                        </div>
                                    </div>

                                    <div class="col-md-12 btns-row" ng-if="ReleaseSteps.length > 0" style="margin-top: 20px;">
                                        <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                                   ng-disabled="Booking.ReleaseCompleted"
                                                   ng-click="showCompletionTPVR('release')">
                                            <i class="material-icons" style="margin-right: 5px;">check_circle</i>
                                            <span ng-if="!Booking.ReleaseCompleted">Complete Release</span>
                                            <span ng-if="Booking.ReleaseCompleted">Release Completed</span>
                                        </md-button>
                                    </div>
                                </div>
                            </div>
                        </md-card>
                    </div>
                </div>

            </md-card>
        </article>

    </div>
</div>

<!-- TPVR Modal Template -->
<script type="text/ng-template" id="tpvr-modal.html">
    <md-dialog aria-label="TPVR Verification" style="max-width:600px; max-height: 88%; min-width: 400px;">
        <form name="tpvrForm" class="form-validation">
            <md-toolbar>
                <div class="md-toolbar-tools">
                    <h2>TPVR - {{tpvrData.stepDescription}}</h2>
                    <span flex></span>
                    <md-button class="md-icon-button" ng-click="cancel()">
                        <md-icon class="material-icons" aria-label="Close dialog">close</md-icon>
                    </md-button>
                </div>
            </md-toolbar>

            <md-dialog-content>
                <div class="md-dialog-content">
                    <div class="row">
                        <div class="col-md-12">
                            <!-- Error Message Display -->
                            <div ng-show="tpvrErrorMessage" style="background: #f8d7da; padding: 15px; margin-bottom: 20px; border-radius: 6px; border-left: 4px solid #dc3545;">
                                <p style="margin: 0; color: #721c24;">
                                    <strong>Error:</strong> {{tpvrErrorMessage}}
                                </p>
                            </div>

                            <div style="background: #fff3cd; padding: 15px; margin-bottom: 20px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <p style="margin: 0; color: #856404;">
                                    <strong>Spotter TPVR Required:</strong> This step requires spotter verification to proceed.
                                </p>
                                <p style="margin: 5px 0 0 0; color: #856404; font-size: 14px;">
                                    Step {{tpvrData.stepNo}}: {{tpvrData.stepDescription}}
                                </p>
                            </div>

                            <md-input-container class="md-block">
                                <label>TDR Spotter</label>
                                <input required name="TDRSpotter" id="TDRSpotter" ng-model="tpvrDetails.TDRSpotter" ng-maxlength="50" type="text" autocomplete="off">
                                <div ng-messages="tpvrForm.TDRSpotter.$error" multiple ng-if='tpvrForm.TDRSpotter.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    <div ng-message="maxlength">Max length 50.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Password</label>
                                <input required name="Password" id="TPVRPassword" ng-model="tpvrDetails.Password" ng-maxlength="50" type="password" autocomplete="off">
                                <div ng-messages="tpvrForm.Password.$error" multiple ng-if='tpvrForm.Password.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    <div ng-message="maxlength">Max length 50.</div>
                                </div>
                            </md-input-container>
                        </div>
                    </div>
                </div>
            </md-dialog-content>

            <md-dialog-actions layout="row" style="justify-content: center;">
                <md-button class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Cancel</md-button>
                <md-button class="md-button md-raised btn-w-md md-primary"
                           ng-disabled="tpvrForm.$invalid || tpvrBusy"
                           ng-click="verifyTPVR()">
                    <span ng-show="!tpvrBusy">Verify</span>
                    <span ng-show="tpvrBusy">
                        <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular>
                    </span>
                </md-button>
            </md-dialog-actions>
        </form>
    </md-dialog>
</script>

<!-- Completion TPVR Modal Template -->
<script type="text/ng-template" id="completion-tpvr-modal.html">
    <md-dialog aria-label="Completion TPVR" style="max-width:600px; max-height: 88%; min-width: 400px;">
        <form name="completionTPVRForm" class="form-validation">
            <md-toolbar>
                <div class="md-toolbar-tools">
                    <h2>TPVR - Complete {{completionData.operationType}}</h2>
                    <span flex></span>
                    <md-button class="md-icon-button" ng-click="cancel()">
                        <md-icon class="material-icons" aria-label="Close dialog">close</md-icon>
                    </md-button>
                </div>
            </md-toolbar>

            <md-dialog-content>
                <div class="md-dialog-content">
                    <div class="row">
                        <div class="col-md-12">
                            <!-- Error Message Display -->
                            <div ng-show="completionErrorMessage" style="background: #f8d7da; padding: 15px; margin-bottom: 20px; border-radius: 6px; border-left: 4px solid #dc3545;">
                                <p style="margin: 0; color: #721c24;">
                                    <strong>Error:</strong> {{completionErrorMessage}}
                                </p>
                            </div>

                            <div style="background: #e8f5e8; padding: 15px; margin-bottom: 20px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <p style="margin: 0; color: #155724;">
                                    <strong>TDR Lead Authorization Required:</strong> This operation requires TDR Lead verification to complete.
                                </p>
                                <p style="margin: 5px 0 0 0; color: #155724; font-size: 14px;">
                                    Operation: {{completionData.operationType}} Completion
                                </p>
                            </div>

                            <md-input-container class="md-block">
                                <label>TDR Lead</label>
                                <input required name="TDRLead" id="TDRLead" ng-model="completionDetails.TDRLead" ng-maxlength="50" type="text" autocomplete="off">
                                <div ng-messages="completionTPVRForm.TDRLead.$error" multiple ng-if='completionTPVRForm.TDRLead.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    <div ng-message="maxlength">Max length 50.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Password</label>
                                <input required name="Password" id="CompletionPassword" ng-model="completionDetails.Password" ng-maxlength="50" type="password" autocomplete="off">
                                <div ng-messages="completionTPVRForm.Password.$error" multiple ng-if='completionTPVRForm.Password.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    <div ng-message="maxlength">Max length 50.</div>
                                </div>
                            </md-input-container>
                        </div>
                    </div>
                </div>
            </md-dialog-content>

            <md-dialog-actions layout="row" style="justify-content: center;">
                <md-button class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Cancel</md-button>
                <md-button class="md-button md-raised btn-w-md md-primary"
                           ng-disabled="completionTPVRForm.$invalid || completionBusy"
                           ng-click="verifyCompletion()">
                    <span ng-show="!completionBusy">Complete {{completionData.operationType}}</span>
                    <span ng-show="completionBusy">
                        <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular>
                    </span>
                </md-button>
            </md-dialog-actions>
        </form>
    </md-dialog>
</script>

<!-- Unsafe TPVR Modal Template -->
<script type="text/ng-template" id="unsafe-tpvr-modal.html">
    <md-dialog aria-label="Unsafe TPVR" style="max-width:600px; max-height: 88%; min-width: 400px;">
        <form name="unsafeTPVRForm" class="form-validation">
            <md-toolbar>
                <div class="md-toolbar-tools">
                    <h2>TPVR - Unsafe to Proceed</h2>
                    <span flex></span>
                    <md-button class="md-icon-button" ng-click="cancel()">
                        <md-icon class="material-icons" aria-label="Close dialog">close</md-icon>
                    </md-button>
                </div>
            </md-toolbar>

            <md-dialog-content>
                <div class="md-dialog-content">
                    <div class="row">
                        <div class="col-md-12">
                            <!-- Error Message Display -->
                            <div ng-show="unsafeErrorMessage" style="background: #f8d7da; padding: 15px; margin-bottom: 20px; border-radius: 6px; border-left: 4px solid #dc3545;">
                                <p style="margin: 0; color: #721c24;">
                                    <strong>Error:</strong> {{unsafeErrorMessage}}
                                </p>
                            </div>

                            <div style="background: #fff3cd; padding: 15px; margin-bottom: 20px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <p style="margin: 0; color: #856404;">
                                    <strong>Safety Alert:</strong> This operation has been marked as unsafe to proceed.
                                </p>
                                <p style="margin: 5px 0 0 0; color: #856404; font-size: 14px;">
                                    TDR Manager authorization and C3PO entry are required to continue.
                                </p>
                            </div>

                            <md-input-container class="md-block">
                                <label>TDR Manager</label>
                                <input required name="TDRManager" id="TDRManager" ng-model="unsafeDetails.TDRManager" ng-maxlength="50" type="text" autocomplete="off">
                                <div ng-messages="unsafeTPVRForm.TDRManager.$error" multiple ng-if='unsafeTPVRForm.TDRManager.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    <div ng-message="maxlength">Max length 50.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Password</label>
                                <input required name="Password" id="UnsafePassword" ng-model="unsafeDetails.Password" ng-maxlength="50" type="password" autocomplete="off">
                                <div ng-messages="unsafeTPVRForm.Password.$error" multiple ng-if='unsafeTPVRForm.Password.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    <div ng-message="maxlength">Max length 50.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>C3PO Entry ID</label>
                                <input required name="C3POEntryID" id="C3POEntryID" ng-model="unsafeDetails.C3POEntryID" ng-maxlength="100" type="text" autocomplete="off">
                                <div ng-messages="unsafeTPVRForm.C3POEntryID.$error" multiple ng-if='unsafeTPVRForm.C3POEntryID.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    <div ng-message="maxlength">Max length 100.</div>
                                </div>
                            </md-input-container>
                        </div>
                    </div>
                </div>
            </md-dialog-content>

            <md-dialog-actions layout="row" style="justify-content: center;">
                <md-button class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Cancel</md-button>
                <md-button class="md-button md-raised btn-w-md md-warn"
                           ng-disabled="unsafeTPVRForm.$invalid || unsafeBusy"
                           ng-click="verifyUnsafe()">
                    <span ng-show="!unsafeBusy">Proceed Unsafe</span>
                    <span ng-show="unsafeBusy">
                        <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular>
                    </span>
                </md-button>
            </md-dialog-actions>
        </form>
    </md-dialog>
</script>


