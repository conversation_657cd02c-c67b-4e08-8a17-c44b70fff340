<?php
	session_start();
	include_once("../database/admin_extended.class.php");
	$obj = new AdminExtendedClass();

    if($_POST['ajax'] == "GetMPNAttributeValues") {
		$result = $obj->GetMPNAttributeValues($_POST);
		echo $result;
	}

    if($_GET['ajax'] == "SearchActiveShipments") {
		$result = $obj->SearchActiveShipments($_GET);
		echo $result;
	}

    if($_POST['ajax'] == "ManageMPN") {
		$result = $obj->ManageMPN($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UploadMPNFile") {
		$result = $obj->UploadMPNFile($_FILES);
		echo $result;
	}

	if($_POST['ajax'] == "UploadAttributesFile") {
		$result = $obj->UploadAttributesFile($_FILES);
		echo $result;
	}

	if($_POST['ajax'] == "UploadMPNAttributesFile") {
		$result = $obj->UploadMPNAttributesFile($_FILES);
		echo $result;
	}

	if($_POST['ajax'] == "UploadWarrantySerials") {
		$result = $obj->UploadWarrantySerials($_FILES);
		echo $result;
	}

	if($_POST['ajax'] == "GetContainerTypeList"){
  		$result = $obj->GetContainerTypeList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "NewByProducts"){
		$result = $obj->NewByProducts($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetByProductsDetails"){
  		$result = $obj->GetByProductsDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetByProductsList"){
  		$result = $obj->GetByProductsList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GenerateByProductsListXLS"){
  		$result = $obj->GenerateByProductsListXLS($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "NewCarrier"){
		$result = $obj->NewCarrier($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetCarrierDetails"){
  		$result = $obj->GetCarrierDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCarrierList"){
  		$result = $obj->GetCarrierList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GenerateCarrierListXLS"){
  		$result = $obj->GenerateCarrierListXLS($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "NewRemovalCode"){
		$result = $obj->NewRemovalCode($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetRemovalCodeDetails"){
  		$result = $obj->GetRemovalCodeDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetPartTypes"){
		$result = $obj->GetPartTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetRemovalCodeList"){
  		$result = $obj->GetRemovalCodeList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GenerateRemovalCodeListXLS"){
  		$result = $obj->GenerateRemovalCodeListXLS($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GenerateWasteCodeListXLS"){
  		$result = $obj->GenerateWasteCodeListXLS($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetRemovalDispositions") {
		$result = $obj->GetRemovalDispositions($_POST);
	  echo $result;
  	}

	if($_POST['ajax'] == "GetRemovalDispositions1") {
		$result = $obj->GetRemovalDispositions1($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "SaveConfiguration") {
		$result = $obj->SaveConfiguration($_POST);
	  echo $result;
  	}

	if($_POST['ajax'] == "GetDailyExtractedList"){
		$result = $obj->GetDailyExtractedList($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "ManualUpload"){
		$result = $obj->ManualUpload($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "ManualUpload1"){
		$result = $obj->ManualUpload1($_POST);
		echo $result;
  	}

	if($_GET['ajax'] == "ManualUpload1") {
		$_SESSION['user']['UserId'] = 0;
		$result = $obj->ManualUpload1($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetRecentMPNUploadDetails"){
		$result = $obj->GetRecentMPNUploadDetails($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "isAdminProfile") {
		if($_SESSION['user']['ProfileID'] == '1') {
			echo true;
		} else {
			echo false;
		}
  	}
  	if($_POST['ajax'] == "GetBinTypeList"){
  		$result = $obj->GetBinTypeList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateBinTypeListXLS") {
		$result = $obj->GenerateBinTypeListXLS($_POST);
		echo $result;
	}
	 if($_POST['ajax'] == "NewBinType"){
		$result = $obj->NewBinType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetBinTypeDetails"){
  		$result = $obj->GetBinTypeDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetWasteCodeList"){
		$result = $obj->GetWasteCodeList($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetWasteCodePartTypes"){
		$result = $obj->GetWasteCodePartTypes($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "NewWasteCode"){
		$result = $obj->NewWasteCode($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetWasteCodeDetails"){
		$result = $obj->GetWasteCodeDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetDispositionOverrideReasonList"){
		$result = $obj->GetDispositionOverrideReasonList($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "OverrideCodeSave"){
		$result = $obj->OverrideCodeSave($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetOverrideCodeDetails"){
		$result = $obj->GetOverrideCodeDetails($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "GetDispositionOverrideEligibilityList"){
		$result = $obj->GetDispositionOverrideEligibilityList($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "ManageDispositionOverrideEligibility"){
		$result = $obj->ManageDispositionOverrideEligibility($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetOverrideEligibilityDetails"){
		$result = $obj->GetOverrideEligibilityDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateDispositionOverrideReasonListXLS"){
  		$result = $obj->GenerateDispositionOverrideReasonListXLS($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateDispositionOverrideEligibilityListXLS"){
  		$result = $obj->GenerateDispositionOverrideEligibilityListXLS($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UserInputReasonSave"){
		$result = $obj->UserInputReasonSave($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetUserInputReasonDetails"){
  		$result = $obj->GetUserInputReasonDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetUserInputReasonsList"){
  		$result = $obj->GetUserInputReasonsList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GenerateUserInputReasonsListXLS"){
  		$result = $obj->GenerateUserInputReasonsListXLS($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "RecordNewReasonCode"){
		$result = $obj->RecordNewReasonCode($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSourceTypeConfigurationList") {
		$result = $obj->GetSourceTypeConfigurationList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAWSCustomers") {
		$result = $obj->GetAWSCustomers($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetMaterialTypes") {
		$result = $obj->GetMaterialTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCustomerTypes") {
		$result = $obj->GetCustomerTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ManageSourceTypeConfiguration") {
		$result = $obj->ManageSourceTypeConfiguration($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSourceTypeConfigurationDetails") {
		$result = $obj->GetSourceTypeConfigurationDetails($_POST);
		echo $result;
	}

	if($_GET['ajax'] == "GetMatchingLocationGroupsAll"){
		$result = $obj->GetMatchingLocationGroupsAll($_GET);
		echo $result;
	}

	if($_POST['ajax'] == "GetGroupsByFromGroupType"){
		$result = $obj->GetGroupsByFromGroupType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetGroupFullLocations") {
		$result = $obj->GetGroupFullLocations($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "ChangeAllLocationGroup") {
		$result = $obj->ChangeAllLocationGroup($_POST);
		echo $result;
	}



	if($_POST['ajax'] == "UpdatePalletLocationGroup") {
		$result = $obj->UpdatePalletLocationGroup($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateCustomPalletLocationGroup") {
		$result = $obj->UpdateCustomPalletLocationGroup($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateShippingContainerLocationGroup"){
		$result = $obj->UpdateShippingContainerLocationGroup($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "getpalletdetails") {
		$result = $obj->getpalletdetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetShippingContainerDetails"){
		$result = $obj->GetShippingContainerDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCPalletDetails") {
		$result = $obj->GetCPalletDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSortCriteria"){
		$result = $obj->GetSortCriteria($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetWorkstationGroups") {
		$result = $obj->GetWorkstationGroups($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetPartTypeList1"){
		$result = $obj->GetPartTypeList1($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCOOList"){
		$result = $obj->GetCOOList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAllSourceTypes"){
		$result = $obj->GetAllSourceTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSPECList"){
		$result = $obj->GetSPECList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSortDisposition"){
		$result = $obj->GetSortDisposition($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetPartTypeByMPN"){
		$result = $obj->GetPartTypeByMPN($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateParttypeMPN"){
		$result = $obj->ValidateParttypeMPN($_POST);
		echo $result;
	}





	if($_POST['ajax'] == "SelectSortBin"){
		$result = $obj->SelectSortBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSortLocation"){
			$result = $obj->GetSortLocation($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SortConfigurationSave"){
		$result = $obj->SortConfigurationSave($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetsortconfigurationList"){
			$result = $obj->GetsortconfigurationList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Deletesortconfiguration"){
		$result = $obj->Deletesortconfiguration($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetsortconfigurationDetails"){
			$result = $obj->GetsortconfigurationDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "updatesortconfigurationstatus"){
		$result = $obj->updatesortconfigurationstatus($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CheckIfPagePermission"){
		$result = $obj->CheckIfPagePermission($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetOverrideReasonCode"){
		$result = $obj->GetOverrideReasonCode($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "AddRecoveryCompleteCheckboxConfiguration"){
		$result = $obj->AddRecoveryCompleteCheckboxConfiguration($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "GetRecoveryCompleteConfigurationList"){
		$result = $obj->GetRecoveryCompleteConfigurationList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteRecoveryCompleteCheckboxConfiguration"){
		$result = $obj->DeleteRecoveryCompleteCheckboxConfiguration($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddRecoveryCompleteDestroyedConfiguration"){
		$result = $obj->AddRecoveryCompleteDestroyedConfiguration($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetRecoveryCompleteDestroyedConfigurationList"){
		$result = $obj->GetRecoveryCompleteDestroyedConfigurationList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteRecoveryCompleteDestroyedConfiguration"){
		$result = $obj->DeleteRecoveryCompleteDestroyedConfiguration($_POST);
		echo $result;
	}
	
	if($_POST['ajax'] == "UploadRigFile") {
		$result = $obj->UploadRigFile($_FILES);
		echo $result;
	}
	if($_POST['ajax'] == "UploadPartTypeFile") {
		$result = $obj->UploadPartTypeFile($_FILES);
		echo $result;
	}
	if($_POST['ajax'] == "UploadDispositionOverrideEligibilityFile") {
		$result = $obj->UploadDispositionOverrideEligibilityFile($_FILES);
		echo $result;
	}
	if($_POST['ajax'] == "UploadSiteFile") {
		$result = $obj->UploadSiteFile($_FILES);
		echo $result;
	}
	if($_POST['ajax'] == "UploadlocationgroupFile") {
		$result = $obj->UploadlocationgroupFile($_FILES);
		echo $result;
	}
	if($_POST['ajax'] == "UploadUserFile") {
		$result = $obj->UploadUserFile($_FILES);
		echo $result;
	}
	if($_POST['ajax'] == "UploadBinFile") {
		$result = $obj->UploadBinFile($_FILES);
		echo $result;
	}
	if($_POST['ajax'] == "UploadSiteDispositionFile") {
		$result = $obj->UploadSiteDispositionFile($_FILES);
		echo $result;
	}
	if($_POST['ajax'] == "UploadSortConfigurationFile") {
		$result = $obj->UploadSortConfigurationFile($_FILES);
		echo $result;
	}
	

?>