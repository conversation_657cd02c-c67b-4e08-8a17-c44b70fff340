<div class="row page" data-ng-controller="trucktypeConfiguration">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Truck Type Configuration</span>
                        <div flex></div>
                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">
                        <form name="trucktype_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <md-select name="FacilityID" ng-model="trucktype.FacilityID" required ng-disabled="true">
                                        <md-option ng-repeat="facility in Facility | filter:{ FacilityStatus : '!InActive'}" value="{{facility.FacilityID}}"> {{facility.FacilityName}} </md-option>                             
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="trucktype_form.FacilityID.$error" multiple ng-if='trucktype_form.FacilityID.$dirty'>                            
                                            <div ng-message="required">This is required.</div>                                            
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Truck Type Name</label>
                                    <input type="text" name="TruckTypeName"  ng-model="trucktype['TruckTypeName']"  required ng-maxlength="45" />
                                    <div class="error-space">
                                    <div ng-messages="trucktype_form.TruckTypeName.$error" multiple ng-if='trucktype_form.TruckTypeName.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-6">
                                    <md-input-container class="md-block">
                                        <label>Description</label>
                                        <input name="Description" ng-model="trucktype.Description" required ng-maxlength="250">  
                                        <div class="error-sapce">
                                            <div ng-messages="trucktype_form.Description.$error" multiple ng-if='trucktype_form.Description.$dirty'> 
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="maxlength">Max length 250.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="trucktype.Status" required aria-label="select">
                                        <md-option value="Active"> Active </md-option>
                                        <md-option value="Inactive"> Inactive </md-option>
                                    </md-select>
                                    <div class="error-space">
                                    <div ng-messages="trucktype_form.Status.$error" multiple ng-if='trucktype_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/TruckTypeConfiguration" style="text-decoration: none;">
                                    <md-button class="md-button md-raised btn-w-md  md-default">
                                        Cancel
                                    </md-button>
                                </a>

                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="trucktype_form.$invalid || trucktype.busy" ng-click="SaveTruckTypeConfiguration()">
                                <span ng-show="! trucktype.busy">Save</span>
                                <span ng-show="trucktype.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>

            <md-card class="no-margin-h pt-0">
                <md-toolbar class="md-table-toolbar md-default" ng-init="TruckTypeConfigurationList = true;">
                    <div class="md-toolbar-tools" style="cursor: pointer;">

                        <i ng-click="TruckTypeConfigurationList = !TruckTypeConfigurationList" class="material-icons md-primary" ng-show="TruckTypeConfigurationList">keyboard_arrow_up</i>
                        <i ng-click="TruckTypeConfigurationList = !TruckTypeConfigurationList" class="material-icons md-primary" ng-show="! TruckTypeConfigurationList">keyboard_arrow_down</i>
                        <span ng-click="TruckTypeConfigurationList = !TruckTypeConfigurationList">Truck Types List</span>
                        <div flex></div>
                        <a href="#!/TruckTypeConfiguration" ng-click="TruckTypeConfigurationxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex;">
                            <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                        </a>
                    </div>
                </md-toolbar>

                <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">
                    <p>No Truck Type Configuration Available </p>
                </div>

                <div class="row"  ng-show="TruckTypeConfigurationList">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div ng-show="pagedItems" class="pull-right mt-10">
                                    <small>
                                    Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                    to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                        <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                    of <span style="font-weight:bold;">{{total}}</span>
                                    </small>
                            </div>
                            <div style="clear:both;"></div>
                            <div class="table-responsive" style="overflow: auto;">
                                <table class="table table-striped mb-0">
                                    <thead>

                                        <tr class="th_sorting">
                                            <th style="min-width: 40px;">Edit</th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">
                                                <div style="min-width: 80px;">
                                                    Facility<i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>
                                                    <span ng-show="OrderBy == 'FacilityName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('TruckTypeName')" ng-class="{'orderby' : OrderBy == 'TruckTypeName'}">
                                                <div style="min-width: 80px;">
                                                    Truck Type Name<i class="fa fa-sort pull-right" ng-show="OrderBy != 'TruckTypeName'"></i>
                                                    <span ng-show="OrderBy == 'TruckTypeName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>

                                             <th style="cursor:pointer;" ng-click="MakeOrderBy('Description')" ng-class="{'orderby' : OrderBy == 'Description'}">
                                                <div style="min-width: 130px;">
                                                    Description<i class="fa fa-sort pull-right" ng-show="OrderBy != 'Description'"></i>
                                                    <span ng-show="OrderBy == 'Description'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">
                                                <div style="min-width: 100px;">
                                                    Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>
                                                    <span ng-show="OrderBy == 'Status'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                        </tr>

                                        <tr class="errornone">
                                            <td>&nbsp;</td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()"  aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="TruckTypeName" ng-model="filter_text[0].TruckTypeName" ng-change="MakeFilter()" aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="Description" ng-model="filter_text[0].Description" ng-change="MakeFilter()"  aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" />
                                                </md-input-container>
                                            </td>
                                        </tr>
                                    </thead>

                                    <tbody ng-show="pagedItems.length > 0">
                                        <tr ng-repeat="product in pagedItems">
                                            <td class="action-icons" style="min-width: 40px;">
                                                <span ng-click="EditTruckTypeConfiguration(product,$event)"><i class="material-icons text-danger edit">edit</i></span>
                                            </td>
                                            <td>
                                                {{product.FacilityName}}
                                            </td>
                                            <td>
                                                {{product.TruckTypeName}}
                                            </td>
                                            <td>
                                                {{product.Description}}
                                            </td>
                                            <td>
                                                {{product.Status}}
                                            </td>
                                        </tr>
                                    </tbody>

                                    <tfoot>
                                        <tr>
                                            <td colspan="5">
                                                <div>
                                                    <ul class="pagination">
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="firstPage()"><< First</a>
                                                        </li>
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="prevPage()"><< Prev</a>
                                                        </li>
                                                        <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                            <a style="cursor:pointer;">{{n+1}}</a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="nextPage()">Next >></a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="lastPage()">Last >></a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    </tfoot>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </md-card>
        </article>
    </div>
</div>
