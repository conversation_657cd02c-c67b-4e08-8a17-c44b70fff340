<div class="row page" data-ng-controller="AssignDispositionIneligibility">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Pick Disposition Eligibility Configuration</span>
                        <div flex></div>
                           <a href="#!/AssignDispositionIneligibilityList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i>Back to List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="assign_disposition_form" class="form-validation" data-ng-submit="submitForm()">
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Facility</label>
                                        <input type="text" ng-model="AssignDisposition.FacilityName" readonly style="background-color: #f5f5f5; cursor: not-allowed;" />
                                        <input type="hidden" ng-model="AssignDisposition.FacilityID" />
                                    </md-input-container>
                                </div>

                                <div class="col-md-3" >
                                    <md-input-container class="md-block">
                                        <label>Disposition From</label>
                                        <md-select name="DispositionFrom" ng-model="AssignDisposition.DispositionFrom" required aria-label="select" >
                                            <md-option ng-repeat="dis_position in PickEligibleDispositions" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}} <span style="color:red;" ng-show="dis_position.sub_disposition > 0">(Sub Disposition)</span> </md-option>
                                        </md-select>
                                        <div class="error-space">
                                            <div ng-messages="assign_disposition_form.DispositionFrom.$error" multiple ng-if='assign_disposition_form.DispositionFrom.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3" >
                                    <md-input-container class="md-block">
                                        <label>Disposition To</label>   
                                        <md-select name="DispositionTo" ng-model="AssignDisposition.DispositionTo" required aria-label="select" >
                                            <md-option ng-repeat="dis_position in AllDispositions" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}} <span style="color:red;" ng-show="dis_position.sub_disposition > 0">(Sub Disposition)</span> </md-option>
                                        </md-select>
                                        <div class="error-space"> 
                                            <div ng-messages="assign_disposition_form.DispositionTo.$error" multiple ng-if='assign_disposition_form.DispositionTo.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>  
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Part Types</label>
                                        <md-select name="part_types" ng-model="AssignDisposition.part_types" required aria-label="select" multiple ng-change="handlePartTypeSelection()">
                                            <md-option value="All">All</md-option>
                                            <md-option ng-repeat="part_type in PartTypes" value="{{part_type.parttypeid}}">{{part_type.parttype}}</md-option>
                                        </md-select>
                                        <div class="error-space">
                                        <div ng-messages="assign_disposition_form.part_types.$error" multiple ng-if='assign_disposition_form.part_types.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">                                            
                                        <label>Status</label>
                                        <md-select name="Status" ng-model="AssignDisposition.Status" required aria-label="select">
                                            <md-option value="Active"> Active </md-option>
                                            <md-option value="Inactive"> Inactive </md-option>
                                        </md-select>  
                                         <div class="error-space"> 
                                        <div ng-messages="assign_disposition_form.Status.$error" multiple ng-if='assign_disposition_form.Status.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div> 
                                        </div>                                            
                                    </md-input-container>
                                </div>                                

                                <div class="col-md-12 btns-row">
                                    <a href="#!/AssignDispositionIneligibilityList" style="text-decoration: none;">
                                        <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                    </a>
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                    data-ng-disabled="assign_disposition_form.$invalid" ng-click="ManageAssignDispositionIneligibility()">
                                    <span ng-show="! AssignDisposition.busy">Save</span>
                                    <span ng-show="AssignDisposition.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                                </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>
