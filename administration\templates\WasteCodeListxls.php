<?php
session_start();
include_once("../../config.php");
$data = $_SESSION['WasteCodeListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "WasteCodeList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Classification Code List');
$header = array('Classification Code','Disposition','Facility','Part Type','Description','Classification Type','Status');

$sql = "select r.*,f.FacilityName,d.disposition,ss.StatusName from waste_codes r,facility f,disposition d,statusses ss where f.FacilityID = r.FacilityID AND d.disposition_id = r.disposition_id AND r.StatusID = ss.StatusID";

// Apply filters if they exist
if($data[0] && count($data[0]) > 0) {
    foreach ($data[0] as $key => $value) {
        if($value != '') {
            if($key == 'WasteCode') {
                $sql = $sql . " AND r.WasteCode like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'disposition') {
                $sql = $sql . " AND d.disposition like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'FacilityName') {
                $sql = $sql . " AND f.FacilityName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'Description') {
                $sql = $sql . " AND r.Description like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'StatusName') {
                $sql = $sql . " AND ss.StatusName like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'part_type') {
                $sql = $sql . " AND r.part_type like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'WasteClassificationType') {
                $sql = $sql . " AND r.WasteClassificationType like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
        }
    }
}

// Apply sorting if it exists
if(isset($data['OrderBy']) && $data['OrderBy'] != '') {
    $order_by_type = (isset($data['OrderByType']) && $data['OrderByType'] == 'asc') ? 'asc' : 'desc';
    
    if($data['OrderBy'] == 'WasteCode') {
        $sql = $sql . " order by r.WasteCode ".$order_by_type." ";
    } else if($data['OrderBy'] == 'disposition') {
        $sql = $sql . " order by d.disposition ".$order_by_type." ";
    } else if($data['OrderBy'] == 'FacilityName') {
        $sql = $sql . " order by f.FacilityName ".$order_by_type." ";
    } else if($data['OrderBy'] == 'Description') {
        $sql = $sql . " order by r.Description ".$order_by_type." ";
    } else if($data['OrderBy'] == 'StatusName') {
        $sql = $sql . " order by ss.StatusName ".$order_by_type." ";
    } else if($data['OrderBy'] == 'part_type') {
        $sql = $sql . " order by r.part_type ".$order_by_type." ";
    } else if($data['OrderBy'] == 'WasteClassificationType') {
        $sql = $sql . " order by r.WasteClassificationType ".$order_by_type." ";
    }
} else {
    $sql = $sql . " order by part_type asc ";
}

$query = mysqli_query($connectionlink1,$sql); 
while($row = mysqli_fetch_assoc($query))
{
    $row2  = array($row['WasteCode'],$row['disposition'],$row['FacilityName'],$row['part_type'],$row['Description'],$row['WasteClassificationType'],$row['StatusName']);
    $rows[] = $row2;
}

$sheet_name = 'WasteCodeList';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 6);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 
