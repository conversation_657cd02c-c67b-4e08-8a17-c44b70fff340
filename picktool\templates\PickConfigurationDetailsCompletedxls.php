<?php
session_start();

// Check if export data exists in session
if (!isset($_SESSION['PickConfigurationDetailsCompletedExport']) || empty($_SESSION['PickConfigurationDetailsCompletedExport'])) {
    die('No export data available. Please try the export again.');
}

$exportData = $_SESSION['PickConfigurationDetailsCompletedExport'];

// Clear the session data after use
unset($_SESSION['PickConfigurationDetailsCompletedExport']);

// Set headers for Excel download
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="Completed_Pick_Configuration_Details_' . date('Y-m-d_H-i-s') . '.xls"');
header('Pragma: no-cache');
header('Expires: 0');

// Start Excel output
echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
echo '<head>';
echo '<meta http-equiv="Content-Type" content="text/html; charset=utf-8">';
echo '<meta name="ProgId" content="Excel.Sheet">';
echo '<meta name="Generator" content="Microsoft Excel 11">';
echo '<style>';
echo 'table { border-collapse: collapse; }';
echo 'th, td { border: 1px solid #000; padding: 5px; text-align: left; }';
echo 'th { background-color: #f0f0f0; font-weight: bold; }';
echo '</style>';
echo '</head>';
echo '<body>';

echo '<table>';

// Header row
if (!empty($exportData)) {
    echo '<tr>';
    foreach (array_keys($exportData[0]) as $header) {
        echo '<th>' . htmlspecialchars($header) . '</th>';
    }
    echo '</tr>';

    // Data rows
    foreach ($exportData as $row) {
        echo '<tr>';
        foreach ($row as $cell) {
            echo '<td>' . htmlspecialchars($cell) . '</td>';
        }
        echo '</tr>';
    }
} else {
    echo '<tr><td colspan="8">No data available for export</td></tr>';
}

echo '</table>';
echo '</body>';
echo '</html>';
?>
