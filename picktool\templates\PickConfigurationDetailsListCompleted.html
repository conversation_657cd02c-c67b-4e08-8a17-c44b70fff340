<div ng-controller="PickConfigurationDetailsListCompleted" class="page">

    
    <div class="row ui-section mb-0">
        <div class="col-md-12">
            <article class="article">
                <div class="body_inner_content">
                    <md-card class="no-margin-h pt-0">
                        <md-toolbar class="md-table-toolbar md-default" ng-init="PickDetailsList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">
                                <i ng-click="PickDetailsList = !PickDetailsList" class="material-icons md-primary" ng-show="PickDetailsList">keyboard_arrow_up</i>
                                <i ng-click="PickDetailsList = !PickDetailsList" class="material-icons md-primary" ng-show="!PickDetailsList">keyboard_arrow_down</i>
                                <span ng-click="PickDetailsList = !PickDetailsList">Completed Pick Configuration Details List</span>
                                <div flex></div>

                                <a ng-click="ExportPickDetailsxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                                    <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg"></md-icon> 
                                    <span>Export to Excel</span>
                                </a>
                                <a href="#!/PickConfigurationDetailsList" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">arrow_back</i> Back to Active List
                                </a>
                            </div>
                        </md-toolbar>
                        
                        <div class="callout callout-info" ng-show="!busy && groupedConfigurations.length == 0">
                            <p>No Completed Pick Configuration Details available</p>
                        </div>

                        <div class="row" ng-show="PickDetailsList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div ng-show="groupedConfigurations.length > 0" class="pull-right pageditems">
                                        <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                            of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-container table-responsive" style="overflow: auto;">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr class="th_sorting">
                                                    <th style="min-width: 80px;">History</th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('PickPathName')" ng-class="{'orderby' : OrderBy == 'PickPathName'}">
                                                        <div>
                                                            Pick Path <i class="fa fa-sort pull-right" ng-show="OrderBy != 'PickPathName'"></i>
                                                            <span ng-show="OrderBy == 'PickPathName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('parttype')" ng-class="{'orderby' : OrderBy == 'parttype'}">
                                                        <div>
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'parttype'"></i>
                                                            <span ng-show="OrderBy == 'parttype'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('mpn')" ng-class="{'orderby' : OrderBy == 'mpn'}">
                                                        <div>
                                                            MPN <i class="fa fa-sort pull-right" ng-show="OrderBy != 'mpn'"></i>
                                                            <span ng-show="OrderBy == 'mpn'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th>Pick Progress</th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FromDisposition')" ng-class="{'orderby' : OrderBy == 'FromDisposition'}">
                                                        <div>
                                                            Pick From Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FromDisposition'"></i>
                                                            <span ng-show="OrderBy == 'FromDisposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ToDisposition')" ng-class="{'orderby' : OrderBy == 'ToDisposition'}">
                                                        <div>
                                                            Assign Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ToDisposition'"></i>
                                                            <span ng-show="OrderBy == 'ToDisposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CreatedDate')" ng-class="{'orderby' : OrderBy == 'CreatedDate'}">
                                                        <div>
                                                            Pick Created Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CreatedDate'"></i>
                                                            <span ng-show="OrderBy == 'CreatedDate'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th>Configuration Status</th>
                                                </tr>

                                                <!-- Search/Filter Row -->
                                                <tr class="errornone">
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="PickPathName" ng-model="filter_text[0].PickPathName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="parttype" ng-model="filter_text[0].parttype" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="mpn" ng-model="filter_text[0].mpn" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="PickProgress" ng-model="filter_text[0].PickProgress" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FromDisposition" ng-model="filter_text[0].FromDisposition" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ToDisposition" ng-model="filter_text[0].ToDisposition" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="CreatedDate" ng-model="filter_text[0].CreatedDate" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ConfigurationStatus" ng-model="filter_text[0].ConfigurationStatus" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                </tr>
                                            </thead>

                                            <tbody ng-show="groupedConfigurations.length > 0">
                                                <tr ng-repeat="config in groupedConfigurations">
                                                    <td style="vertical-align: top; padding-top: 15px;">
                                                        <md-button class="md-icon-button" ng-click="showConfigurationTrackingHistory(config)" aria-label="View Configuration Tracking History">
                                                            <md-icon class="material-icons text-primary">history</md-icon>
                                                        </md-button>
                                                    </td>
                                                    <td style="vertical-align: top; padding-top: 15px;">{{config.PickPathName}}</td>
                                                    <td style="vertical-align: top; padding-top: 15px;">{{config.parttype}}</td>
                                                    <td style="vertical-align: top; padding-top: 15px;">
                                                        <div ng-repeat="detail in config.details" style="padding: 4px 0; border-bottom: 1px solid #f0f0f0;">
                                                            {{detail.mpn || 'N/A'}}
                                                        </div>
                                                    </td>
                                                    <td style="vertical-align: top; padding-top: 15px;">
                                                        <div ng-repeat="detail in config.details" style="padding: 4px 0; border-bottom: 1px solid #f0f0f0;">
                                                            {{detail.PickCompleted || 0}} / {{detail.PickQuantity}}
                                                        </div>
                                                    </td>
                                                    <td style="vertical-align: top; padding-top: 15px;">{{config.FromDisposition}}</td>
                                                    <td style="vertical-align: top; padding-top: 15px;">{{config.ToDisposition}}</td>
                                                    <td style="vertical-align: top; padding-top: 15px;">{{config.CreatedDate | date:'MM/dd/yyyy HH:mm'}}</td>
                                                    <td style="vertical-align: top; padding-top: 15px;">{{config.ConfigurationStatus}}</td>
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="8">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)">
                                                                    <a href>{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </md-card>
                </div>
            </article>
        </div>
    </div>

    <!-- Pick Detail Tracking History Dialog Template -->
    <script type="text/ng-template" id="pickDetailTrackingDialog.html">
        <md-dialog aria-label="Pick Detail History" style="min-width: 900px; max-width: 95vw;">
            <md-toolbar>
                <div class="md-toolbar-tools">
                    <md-icon class="material-icons">history</md-icon>
                    <h2 ng-if="selectedDetail.mpn">Pick Detail History: {{selectedDetail.PickPathName}} - {{selectedDetail.parttype}} - {{selectedDetail.mpn}}</h2>
                    <h2 ng-if="!selectedDetail.mpn">Configuration Tracking History: {{selectedDetail.PickPathName}}</h2>
                    <span flex></span>
                    <md-button class="md-icon-button" ng-click="closeDialog()">
                        <md-icon class="material-icons">close</md-icon>
                    </md-button>
                </div>
            </md-toolbar>

            <md-dialog-content style="max-width: none; max-height: 70vh;">
                <div class="md-dialog-content" style="padding: 20px;">

                    <!-- Loading State -->
                    <div ng-show="trackingHistoryLoading" class="text-center" style="padding: 40px;">
                        <md-progress-circular md-mode="indeterminate" md-diameter="40"></md-progress-circular>
                        <p style="margin-top: 20px;">Loading pick detail history...</p>
                    </div>

                    <!-- Empty State -->
                    <div ng-show="!trackingHistoryLoading && trackingHistory.length === 0" class="text-center" style="padding: 40px;">
                        <md-icon class="material-icons" style="font-size: 48px; opacity: 0.5; color: #999;">history</md-icon>
                        <p style="margin-top: 20px; color: #666;">No history records found for this pick detail.</p>
                    </div>

                    <!-- History Table -->
                    <div ng-show="!trackingHistoryLoading && trackingHistory.length > 0">
                        <div style="margin-bottom: 20px;">
                            <small style="color: #666;">
                                <md-icon class="material-icons" style="font-size: 14px; vertical-align: middle;">info</md-icon>
                                Showing {{trackingHistory.length}} history record{{trackingHistory.length !== 1 ? 's' : ''}}
                            </small>
                        </div>

                        <div class="tracking-table-responsive">
                            <table class="tracking-table tracking-table-striped tracking-table-hover">
                                <thead>
                                    <tr>
                                        <th style="width: 150px;">Date/Time</th>
                                        <th style="width: 120px;">MPN</th>
                                        <th>Action</th>
                                        <th style="width: 120px;">Module Name</th>
                                        <th style="width: 100px;">Created By</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="record in trackingHistory">
                                        <td style="vertical-align: middle;">
                                            <div style="font-size: 13px;">
                                                <div style="font-weight: 500;">{{record.CreatedDate}}</div>
                                            </div>
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <span style="font-size: 12px; color: #666;">
                                                <span ng-if="record.mpn">{{record.mpn}}</span>
                                                <span ng-if="!record.mpn" style="font-style: italic; color: #999;">Configuration</span>
                                            </span>
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <span style="font-size: 13px; color: #333;">
                                                {{record.Action}}
                                            </span>
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <span style="font-size: 12px; color: #666;">{{record.ModuleName}}</span>
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <span style="font-size: 12px; color: #666;">{{record.CreatedBy}}</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </md-dialog-content>

            <md-dialog-actions layout="row">
                <span flex></span>
                <md-button ng-click="closeDialog()" class="md-primary">
                    Close
                </md-button>
            </md-dialog-actions>
        </md-dialog>
    </script>

</div>

<style>
/* Pick Detail History Table Styles - Scoped to tracking table only */
.tracking-table-responsive {
    border: 1px solid #ddd;
    border-radius: 4px;
}

.tracking-table {
    width: 100%;
    margin-bottom: 0;
    border-collapse: collapse;
    background-color: #fff;
}

.tracking-table th,
.tracking-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #ddd;
    text-align: left;
}

.tracking-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    font-size: 13px;
}

.tracking-table-striped tbody tr:nth-of-type(odd) {
    background-color: #f8f9fa;
}

.tracking-table-hover tbody tr:hover {
    background-color: #e8f4f8;
    cursor: default;
}

.tracking-table tbody tr:last-child td {
    border-bottom: none;
}
</style>
